2025-04-04 20:46:49,703 - C<PERSON>QuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_204649.log
2025-04-04 20:46:49,706 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:46:49,706 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:46:49,706 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:46:49,706 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True}
2025-04-04 20:46:49,707 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:46:49,707 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:46:52,329 - CpaQuebecParser - CRITICAL - Критическая ошибка выполнения (Selenium): Не удалось запустить и настроить undetected_chromedriver.
2025-04-04 20:46:52,330 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/main.py", line 82, in run_parser
    with CpaQuebecParser(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/selenium_parser/orchestrator.py", line 156, in __enter__
    self.browser_manager.__enter__() # Запускаем драйвер
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/selenium_parser/browser.py", line 117, in __enter__
    raise RuntimeError("Не удалось запустить и настроить undetected_chromedriver.")
RuntimeError: Не удалось запустить и настроить undetected_chromedriver.

2025-04-04 20:46:52,331 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 4
