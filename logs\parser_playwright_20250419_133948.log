2025-04-19 13:39:48,749 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_133948.log
2025-04-19 13:39:48,752 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 13:39:48,752 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-19 13:39:48,752 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 13:39:48,753 - <PERSON><PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals'}
2025-04-19 13:39:48,753 - CpaQuebecParser - INFO - ==================================================
2025-04-19 13:39:48,753 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-19 13:39:57,307 - CpaQuebecParser - DEBUG - Пауза на 2.49 сек. (после driver.get)
2025-04-19 13:39:59,019 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
