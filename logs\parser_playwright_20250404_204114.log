2025-04-04 20:41:14,249 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_204114.log
2025-04-04 20:41:14,252 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:41:14,253 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 20:41:14,253 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:41:14,253 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True}
2025-04-04 20:41:14,253 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:41:14,253 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:41:25,469 - CpaQuebecParser - DEBUG - Пауза на 1.68 сек. (после driver.get)
2025-04-04 20:41:29,625 - CpaQuebecParser - DEBUG - Пауза на 0.86 сек. (после клика Reset (XPath))
2025-04-04 20:41:33,896 - CpaQuebecParser - DEBUG - Пауза на 0.61 сек. (после очистки формы)
2025-04-04 20:42:26,489 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
