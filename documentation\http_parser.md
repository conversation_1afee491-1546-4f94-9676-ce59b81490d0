# HTTP-парсер для CPA Quebec (requests-based)

В этой документации описан новый легковесный HTTP‑парсер на базе `requests`, который заменяет Selenium/Playwright-решение и позволяет работать напрямую с скрытым API сайта.

## 1. Обзор

Новый парсер (`cpa_quebec_requests`) отправляет HTTP POST-запросы к скрытому API каталога CPA Quebec и получает JSON-ответ со списком CPA по заданным категориям. Это обеспечивает:

- Быстрый и стабильный сбор данных без запуска браузера
- Минимальные накладные расходы на CPU и память
- Простую настройку таймаутов и повторных попыток
- Лёгкую параллелизацию и масштабирование

## 2. Установка

1. Клонируйте или обновите репозиторий:
   ```bash
   git clone <url-репозитория>
   cd <папка-проекта>
   ```

2. Создайте виртуальное окружение и активируйте его (рекомендуется):
   ```bash
   python3 -m venv venv
   source venv/bin/activate   # Linux/macOS
   # или .\venv\Scripts\activate для Windows
   ```

3. Установите зависимости:
   ```bash
   pip install -r requirements.txt
   ```

# Дополнительная зависимость для парсинга HTML
```bash
pip install beautifulsoup4
```

> При необходимости удалите или закомментируйте зависимости `selenium` и `undetected-chromedriver` в `requirements.txt`.

## 3. Структура проекта

```
cpa_quebec_requests/        # Новый пакет HTTP-парсера
├── client.py               # HTTP-клиент для работы с API
├── parser.py               # Логика итерации по категориям и страницам
├── saver.py                # Сохранение результатов в JSON и Excel
└── main.py                 # CLI: запуск парсера (argparse)

cpa_quebec/                 # Настройки и общие константы
└── config.py               # BASE_URL, OUTPUT_DIR, CLIENT_CATEGORIES_TO_PARSE и др.

requirements.txt            # requests, fake-useragent, pandas и пр.
``` 

## 4. Настройка

- **BASE_URL**, **OUTPUT_DIR**, **CLIENT_CATEGORIES_TO_PARSE** и другие параметры задаются в файле `cpa_quebec/config.py`.
- По умолчанию исходный URL: `https://cpaquebec.ca`.

## 5. Использование

CLI-параметры нового парсера:

- `--by-category`      — запуск парсинга по всем категориям из `config.py`.
- `--category NAME`    — парсить только указанную категорию (точное совпадение).
- `--get-details`      — собирать подробную информацию о каждом профиле (с полями name_detail, title, company, ...).
- `--debug`            — включить подробное логирование (`DEBUG`).

**Пример запуска**:
```bash
# Парсинг всех категорий, headless, без подробного лога
python -m cpa_quebec_requests.main --by-category

# Парсинг категории 'Individuals' с деталями и отладкой
python -m cpa_quebec_requests.main --category "Individuals" --get-details --debug
```

## 6. Описание модулей

### client.py

- **`CpaQuebecClient`** — инициализирует `requests.Session()` с заголовками (User-Agent, AJAX).
- Метод `search(criteria: dict, page_number: int) -> str` или `dict` отправляет POST на:
  `/api/sitecore/FindACPA/FindACPABottinFormSubmit?Length=8` и возвращает фрагмент HTML или JSON.

### parser.py

- **`CpaQuebecParser`** — принимает список категорий из `config.py`.
- Метод `_build_criteria(category: str)` конструирует полный набор параметров формы.
- Метод `parse_category(category: str)` обходит страницы, парсит HTML через `BeautifulSoup`, извлекает ссылки на профили.
- Метод `parse_all()` запускает `parse_category` для каждой категории и возвращает словарь {category: [items]}.

### details.py

- **`DetailsParser`** — HTTP‑парсер детальной страницы профиля CPA через `requests` + `BeautifulSoup`.
- Метод `parse(profile_url: str) -> Optional[Dict]` возвращает словарь с полями:
  `name_detail`, `title`, `company`, `address`, `phone_detail`, `email_detail`, `website`, `languages`, `practice_areas`, `clients`.

### saver.py

- Функция `save_results(data: dict, timestamp: str = None)`:
  - Сохраняет полный словарь категорий в JSON (`OUTPUT_DIR`).
  - Собирает в одну таблицу (с колонкой `Category`) и сохраняет Excel через `pandas`.

### main.py

- Консольный интерфейс (`argparse`), который:
  1. Инициализирует `CpaQuebecParser`.
  2. Запускает нужный режим (`--by-category` или `--category`).
  3. Сохраняет результаты с помощью `save_results`.

## 7. Потенциальное расширение

- Добавить детализацию профилей CPA (GET по `profile_url` + парсинг HTML через `BeautifulSoup`).
- Реализовать асинхронный клиент (`httpx.AsyncClient`) для ускорения запросов.
- Внедрить логику повторных попыток и прокси-конфигурации (`requests.adapters.Retry`).

---
*Старые инструкции по Selenium/Playwright и их архитектура сохранены в `parser_logic.md`.* 