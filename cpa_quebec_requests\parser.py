from typing import List, Dict, Any
from .client import CpaQuebecClient
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from cpa_quebec.config import CLIENT_CATEGORIES_TO_PARSE
import logging

# mapping category name to its NoRepertoire id for API
CATEGORY_IDS = {
    "Individuals": "1878",
    "Large companies": "1877",
    "NFPOs": "1873",
    "Professional firms": "1871",
    "Public corporations": "1879",
    "Retailers": "1876",
    "Self-employed workers": "1880",
    "SMEs": "1874",
    "Start-ups": "1872",
    "Syndicates of co-owners": "1875",
}

class CpaQuebecParser:
    """
    Парсит каталог CPA Quebec через скрытый HTTP API без браузера.
    """
    def __init__(self,
                 base_url: str = None):
        # Инициализируем HTTP-клиент
        self.base_url = base_url or "https://cpaquebec.ca"
        self.client = CpaQuebecClient(base_url=self.base_url)
        # Список категорий для парсинга из config
        self.categories = CLIENT_CATEGORIES_TO_PARSE
        # Логгер для подробного вывода
        self.logger = logging.getLogger(__name__)
        self.logger.debug(f"Initialized HTTP parser with base_url={self.base_url}")

    def _build_criteria(self, category: str) -> Dict[str, str]:
        """
        Строит полный набор параметров формы для поиска:
        - Для каждой категории задаёт Selected=true/false, Desc и NoRepertoire.
        """
        criteria: Dict[str, str] = {}
        total = len(self.categories)
        half = total // 2
        for idx, cat in enumerate(self.categories):
            # Определяем колонку и индекс
            if idx < half:
                col = "LeftColumn"
                index = idx
            else:
                col = "RightColumn"
                index = idx - half
            prefix = f"ListeClienteleDesservies{col}[{index}]"
            # Задаём флаг selected
            criteria[f"{prefix}.Selected"] = "true" if cat == category else "false"
            # Добавляем описание и идентификатор
            criteria[f"{prefix}.Desc"] = cat
            criteria[f"{prefix}.NoRepertoire"] = CATEGORY_IDS[cat]
        # По умолчанию без фильтра "Accepting new clients"
        criteria['ChckAcceptClients'] = 'false'
        self.logger.debug(f"Built criteria for category '{category}': {criteria}")
        return criteria

    def parse_category(self, category: str) -> List[Dict[str, Any]]:
        """
        Пытается спарсить ПЕРВУЮ страницу результатов для категории.
        ВНИМАНИЕ: Пагинация и фильтрация по категории через API, скорее всего, не работают из-за редиректа.
        Возвращает список записей с полями 'name' и 'profile_url'.
        """
        self.logger.info(f"Attempting to parse first page for category '{category}' (pagination likely broken)")
        from bs4 import BeautifulSoup  # импорт здесь если не объявлен сверху
        import re
        results: List[Dict[str, Any]] = []
        criteria = self._build_criteria(category)

        self.logger.debug(f"Requesting category '{category}', page 0 (effective)")
        raw_response = self.client.search(criteria=criteria, page_number=0)
        # Обработка ответа (JSON или HTML)
        if isinstance(raw_response, dict):
            self.logger.debug(f"Received JSON response with keys: {list(raw_response.keys())}")
            html_fragment = None
            for v in raw_response.values():
                if isinstance(v, str) and '<' in v:
                    html_fragment = v
                    break
            if not html_fragment:
                html_fragment = str(raw_response)
                self.logger.warning("Could not find HTML fragment in JSON response, using string representation.")
            soup = BeautifulSoup(html_fragment, 'html.parser')
        elif isinstance(raw_response, str):
            self.logger.debug(f"Received response HTML length: {len(raw_response)}")
            soup = BeautifulSoup(raw_response, 'html.parser')
        else:
            self.logger.error(f"Received unexpected response type: {type(raw_response)}")
            return [] # Возвращаем пустой список при неожиданном типе

        # Ищем контейнер (возможно, нужно будет изменить селекторы после анализа HTML)
        container = (
            soup.find(id='SectionResultats') or
            soup.find(id='AfficherResultatsListContainer') or
            soup.find(id='AfficherResultatsListStaticContainer') or
            soup # Fallback на весь документ, если контейнеры не найдены
        )
        if container == soup:
            self.logger.debug("Specific result containers not found, parsing the whole document.")

        # Извлекаем ссылки на профили CPA
        page_items: List[Dict[str, Any]] = []
        profile_link_pattern = '/en/find-a-cpa/' # Уточнить по реальному HTML
        for a in container.find_all('a', href=True):
            href = a['href']
            # Фильтруем по шаблону профиля CPA
            if profile_link_pattern not in href or href == profile_link_pattern : # Исключаем ссылку на сам каталог
                continue
            name = a.get_text(strip=True)
            # Собираем полный URL
            profile_url = urljoin(self.base_url, href)
            # Проверка на дубликаты перед добавлением (простая, но не идеальная)
            if not any(item['profile_url'] == profile_url for item in page_items):
                page_items.append({'name': name, 'profile_url': profile_url})

        self.logger.debug(f"Found {len(page_items)} unique profile links on the page.")
        results.extend(page_items)

        self.logger.info(f"Finished parsing first page for category '{category}', total items found: {len(results)}")
        return results

    def parse_all(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Последовательно парсит все категории из config.
        Возвращает словарь вида {category: [items]}.
        """
        self.logger.info("Starting parse_all for all categories")
        all_data: Dict[str, List[Dict[str, Any]]] = {}
        for idx, category in enumerate(self.categories):
            self.logger.info(f"Parsing category '{category}' ({idx+1}/{len(self.categories)})")
            all_data[category] = self.parse_category(category)
        self.logger.info("Completed parse_all for all categories")
        return all_data 