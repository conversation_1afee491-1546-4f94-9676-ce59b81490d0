2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : Logging setup complete. Level: INFO. Log file: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/parser_log_20250505_181030.log
2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : Debug directory created: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030
2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION STARTED =====
2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : Arguments: {'by_category': True, 'category': None, 'all': False, 'no_captcha': False, 'no_details': False, 'visible': True, 'debug': True, 'log_level': 'INFO', 'max_pages': 500, 'use_system_browser': True}
2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : Anti-Captcha API Key found (ending with ...cc18b). Automatic solving ENABLED.
2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : Attempting to find system Chrome/Chromium...
2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : Using system browser found at: /usr/bin/google-chrome
2025-05-05 18:10:30 [INFO    ] cpa_quebec_parser   : Initializing Playwright...
2025-05-05 18:10:31 [INFO    ] cpa_quebec_parser   : Launching browser (headed)...
2025-05-05 18:10:31 [INFO    ] cpa_quebec_parser   : Browser launched successfully. Version: 134.0.6998.165
2025-05-05 18:10:32 [INFO    ] cpa_quebec_parser   : Browser context created.
2025-05-05 18:10:32 [INFO    ] cpa_quebec_parser   : New page created.
2025-05-05 18:10:32 [INFO    ] cpa_quebec_parser   : Mode: Processing all 10 categories.
2025-05-05 18:10:32 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Individuals ---
2025-05-05 18:10:32 [INFO    ] cpa_quebec_parser   : ===== STARTING CATEGORY PROCESSING: Individuals =====
2025-05-05 18:10:33 [INFO    ] cpa_quebec_parser   : Navigating to base URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/
2025-05-05 18:10:37 [INFO    ] cpa_quebec_parser   : Base URL page loaded.
2025-05-05 18:10:38 [INFO    ] cpa_quebec_parser   : Found cookie banner button with selector: text=Tout refuser
2025-05-05 18:10:39 [INFO    ] cpa_quebec_parser   : Cookie banner closed.
2025-05-05 18:10:40 [INFO    ] cpa_quebec_parser   : Selecting category checkbox: 'Individuals'
2025-05-05 18:10:44 [INFO    ] cpa_quebec_parser   : Checkbox for 'Individuals' checked successfully.
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Checking if CAPTCHA needs to be solved...
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Search button is already enabled. CAPTCHA might not be required.
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Clicking the search button...
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Form-specific selector failed, trying role-based selector with nth
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Found form with Search text
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Found submit button in form
2025-05-05 18:10:47 [WARNING ] cpa_quebec_parser   : Error trying to click form-filtered button: Locator.click: Element is outside of the viewport
Call log:
  - waiting for locator("form").filter(has_text="Search").locator("button[type='submit']").first
  -     - locator resolved to <button type="submit">Search</button>
  -   - attempting click action
  -     - scrolling into view if needed
  -     - done scrolling

2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Found 2 search buttons by role
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Trying to click search button #1
2025-05-05 18:10:47 [WARNING ] cpa_quebec_parser   : Failed to click search button #1: Locator.click: Element is outside of the viewport
Call log:
  - waiting for get_by_role("button", name="Search").first
  -     - locator resolved to <button type="submit">Search</button>
  -   - attempting click action
  -     - scrolling into view if needed
  -     - done scrolling

2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Trying to click search button #2
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Search button #2 clicked successfully
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Trying JavaScript click on first search button
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : JavaScript click executed
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : Search initiated successfully.
2025-05-05 18:10:47 [INFO    ] cpa_quebec_parser   : --- Processing results page 1 for category 'Individuals' ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Individuals': Page.wait_for_timeout: Target page, context or browser has been closed
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3205, in process_category
    await page.wait_for_timeout(2000)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/async_api/_generated.py", line 11408, in wait_for_timeout
    await self._impl_obj.wait_for_timeout(timeout=timeout)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_page.py", line 1079, in wait_for_timeout
    await self._main_frame.wait_for_timeout(timeout)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_frame.py", line 756, in wait_for_timeout
    await self._channel.send("waitForTimeout", locals_to_params(locals()))
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TargetClosedError: Page.wait_for_timeout: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Individuals_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Individuals_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Individuals' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Large companies ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Large companies': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Large companies_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Large companies_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Large companies' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: NFPOs ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'NFPOs': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_NFPOs_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_NFPOs_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'NFPOs' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Professional firms ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Professional firms': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Professional firms_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Professional firms_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Professional firms' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Public corporations ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Public corporations': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Public corporations_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Public corporations_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Public corporations' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Retailers ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Retailers': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Retailers_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Retailers_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Retailers' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Self-employed workers ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Self-employed workers': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Self-employed workers_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Self-employed workers_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Self-employed workers' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: SMEs ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'SMEs': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_SMEs_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_SMEs_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'SMEs' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Start-ups ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Start-ups': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Start-ups_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Start-ups_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Start-ups' due to error.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Syndicates of co-owners ---
2025-05-05 18:10:52 [ERROR   ] cpa_quebec_parser   : Error processing category 'Syndicates of co-owners': page не должен быть закрыт
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3651, in run
    cat_results = await process_category(page, category_name, get_details=get_details_flag)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 152, in async_wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2959, in process_category
    assert not page.is_closed(), "page не должен быть закрыт"
AssertionError: page не должен быть закрыт
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/screenshot_error_fatal_category_Syndicates of co-owners_2025-05-05_18-10-52.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_181030/html_error_fatal_category_Syndicates of co-owners_2025-05-05_18-10-52.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : Skipping category 'Syndicates of co-owners' due to error.
2025-05-05 18:10:52 [WARNING ] cpa_quebec_parser   : No results collected to save.
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : Cleaning up resources...
2025-05-05 18:10:52 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION FINISHED =====
