2025-04-04 21:17:34,310 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_211734.log
2025-04-04 21:17:34,314 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 21:17:34,314 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 21:17:34,314 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 21:17:34,314 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 21:17:34,315 - CpaQuebecParser - INFO - ==================================================
2025-04-04 21:17:34,315 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 21:17:46,671 - CpaQuebecParser - DEBUG - Пауза на 1.53 сек. (после driver.get)
2025-04-04 21:17:48,298 - CpaQuebecParser - DEBUG - Пауза на 0.32 сек. (после scroll к кнопке cookie)
2025-04-04 21:17:59,964 - CpaQuebecParser - DEBUG - Пауза на 0.73 сек. (после клика Reset (XPath))
2025-04-04 21:18:03,370 - CpaQuebecParser - DEBUG - Пауза на 0.71 сек. (после очистки формы)
2025-04-04 21:18:04,852 - CpaQuebecParser - DEBUG - Пауза на 3.52 сек. (между категориями, после Individuals)
2025-04-04 21:18:06,776 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
