use whitehole::{Parser, rule, char, rep, ws};

#[derive(Debug)]
struct Card {
    title: String,
    link: String,
}

// Правило для парсинга карточки CPA
rule! {
    card(r: &str) -> Card {
        ws!()?
        >> div(class="card")
        >> h3() >> text().collect()  // Название
        >> ws!()?
        >> a(href=link) >> text().collect()  // Ссылка
        => |(title, link)| Card { title, link }
    }
}

// Правило для формы поиска
rule! {
    search_form(r: &str) -> (String, String) {
        ws!()?
        >> form(id="search-form")
        >> input(name="query") >> attr("value").collect()
        >> select(name="category") >> option(selected=true) >> text().collect()
        => |(query, category)| (query, category)
    }
}

#[tokio::main]
async fn main() {
    let html = std::fs::read_to_string("page.html").unwrap();
    let mut parser = Parser::new(&html);
    
    // Парсим карточки
    while let Ok((card, _)) = parser.parse(card) {
        println!("Найдена карточка: {:?}", card);
    }
    
    // Парсим форму поиска
    if let Ok((query, category)) = parser.parse(search_form) {
        println!("Параметры поиска: {}, {}", query, category);
    }
}