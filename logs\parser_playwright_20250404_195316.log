2025-04-04 19:53:16,325 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_195316.log
2025-04-04 19:53:16,328 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:53:16,328 - C<PERSON><PERSON>uebecParser - INFO - ==================================================
2025-04-04 19:53:16,328 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:53:16,328 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:53:16,328 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:53:16,329 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:53:24,326 - CpaQuebecParser - DEBUG - Пауза на 0.71 сек. (после навигации)
2025-04-04 19:53:34,979 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 19:53:35,416 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
