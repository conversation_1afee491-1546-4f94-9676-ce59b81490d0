2025-04-25 17:34:49,029 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250425_173449.log
2025-04-25 17:34:49,032 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-25 17:34:49,032 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-25 17:34:49,032 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals', 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-25 17:34:49,033 - CpaQuebecParser - INFO - ==================================================
2025-04-25 17:34:49,033 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-25 17:35:07,321 - CpaQuebecParser - INFO - Парсинг (Selenium) завершен. Найдено и сохранено записей: 0
2025-04-25 17:35:07,351 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 0
