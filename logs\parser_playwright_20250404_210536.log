2025-04-04 21:05:36,376 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_210536.log
2025-04-04 21:05:36,380 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 21:05:36,380 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 21:05:36,380 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 21:05:36,380 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 21:05:36,380 - CpaQuebecParser - INFO - ==================================================
2025-04-04 21:05:36,381 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 21:05:49,725 - CpaQuebecParser - DEBUG - Пауза на 1.46 сек. (после driver.get)
2025-04-04 21:05:51,306 - CpaQuebecParser - DEBUG - Пауза на 0.62 сек. (после scroll к кнопке cookie)
2025-04-04 21:06:03,880 - CpaQuebecParser - DEBUG - Пауза на 0.54 сек. (после клика Reset (XPath))
2025-04-04 21:06:09,704 - CpaQuebecParser - DEBUG - Пауза на 0.64 сек. (после очистки формы)
2025-04-04 21:08:11,963 - CpaQuebecParser - DEBUG - Пауза на 3.88 сек. (между категориями, после Individuals)
2025-04-04 21:08:21,759 - CpaQuebecParser - DEBUG - Пауза на 1.78 сек. (после driver.get)
2025-04-04 21:09:55,448 - CpaQuebecParser - DEBUG - Пауза на 0.71 сек. (после клика Reset (XPath))
2025-04-04 21:10:01,071 - CpaQuebecParser - DEBUG - Пауза на 0.84 сек. (после очистки формы)
2025-04-04 21:10:29,736 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
