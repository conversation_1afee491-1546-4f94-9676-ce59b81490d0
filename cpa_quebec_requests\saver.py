import os
import json
from datetime import datetime
from typing import Dict, List, Any
import pandas as pd
import logging
logger = logging.getLogger(__name__)
from cpa_quebec.config import OUTPUT_DIR, JSON_FILENAME_TEMPLATE, EXCEL_FILENAME_TEMPLATE


def save_results(data: Dict[str, List[Dict[str, Any]]], timestamp: str = None) -> None:
    """
    Сохраняет результаты парсинга:
      - полный словарь категорий в JSON-файл
      - все записи в Excel с колонкой Category
    """
    if timestamp is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # JSON
    json_name = JSON_FILENAME_TEMPLATE.format(timestamp=timestamp)
    json_path = os.path.join(OUTPUT_DIR, json_name)
    logger.info(f"Saving JSON results to {json_path}")
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    # Excel
    rows: List[Dict[str, Any]] = []
    for category, items in data.items():
        for item in items:
            row = item.copy()
            row['Category'] = category
            rows.append(row)

    df = pd.DataFrame(rows)
    excel_name = EXCEL_FILENAME_TEMPLATE.format(timestamp=timestamp)
    excel_path = os.path.join(OUTPUT_DIR, excel_name)
    logger.info(f"Saving Excel results to {excel_path}")
    df.to_excel(excel_path, index=False)  