# CPA Quebec Parser

Парсер для сбора данных о CPA (Certified Public Accountants) из каталога CPA Quebec.

## Режимы работы

Парсер поддерживает два режима работы:

1. **HTTP-режим** - использует только HTTP-запросы без браузера. Быстрый, но не может обойти капчу.
2. **Гибридный режим** - использует Selenium для обхода капчи, а затем переключается на HTTP-запросы для более быстрого сбора данных.

## Установка зависимостей

```bash
pip install -r requirements.txt
```

Для гибридного режима также требуется:
- Google Chrome
- ChromeDriver (устанавливается автоматически через undetected_chromedriver)

## Использование

### HTTP-режим (без браузера)

```bash
# Парсинг всех категорий
python -m cpa_quebec_requests.main --by-category

# Парсинг конкретной категории
python -m cpa_quebec_requests.main --category "Individuals"

# Сбор детальной информации о профилях
python -m cpa_quebec_requests.main --by-category --get-details

# Включение режима отладки
python -m cpa_quebec_requests.main --by-category --debug
```

### Гибридный режим (Selenium + HTTP)

```bash
# Парсинг всех категорий
python -m cpa_quebec_requests.main --by-category --hybrid

# Парсинг конкретной категории
python -m cpa_quebec_requests.main --category "Individuals" --hybrid

# Запуск браузера в видимом режиме
python -m cpa_quebec_requests.main --by-category --hybrid --no-headless

# Отключение решения капчи
python -m cpa_quebec_requests.main --by-category --hybrid --no-captcha

# Сбор детальной информации о профилях
python -m cpa_quebec_requests.main --by-category --hybrid --get-details

# Включение режима отладки
python -m cpa_quebec_requests.main --by-category --hybrid --debug
```

## Решение капчи

Для решения капчи в гибридном режиме используется сервис 2Captcha. Для его работы необходимо:

1. Зарегистрироваться на сайте [2Captcha](https://2captcha.com/)
2. Пополнить баланс
3. Получить API-ключ
4. Создать файл `.env` в корне проекта со следующим содержимым:

```
TWOCAPTCHA_API_KEY=ваш_ключ_api
```

## Результаты

Результаты парсинга сохраняются в директории `output` в форматах JSON и Excel.

## Структура проекта

- `cpa_quebec_requests/` - основной модуль парсера
  - `main.py` - точка входа
  - `updated_parser.py` - HTTP-парсер
  - `hybrid_parser.py` - гибридный парсер (Selenium + HTTP)
  - `client.py` - HTTP-клиент
  - `saver.py` - сохранение результатов
  - `details.py` - парсинг деталей профилей

## Решение проблем

### Капча

Если HTTP-парсер не может получить результаты из-за капчи, используйте гибридный режим с опцией `--hybrid`.

### Видимый режим браузера

Если вы хотите видеть, что происходит в браузере, используйте опцию `--no-headless`.

### Отладка

Для получения подробной информации о работе парсера используйте опцию `--debug`.
