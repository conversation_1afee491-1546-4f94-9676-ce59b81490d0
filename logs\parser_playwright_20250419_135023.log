2025-04-19 13:50:23,085 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_135023.log
2025-04-19 13:50:23,087 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 13:50:23,091 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-19 13:50:23,091 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 13:50:23,092 - <PERSON><PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals'}
2025-04-19 13:50:23,092 - CpaQuebecParser - INFO - ==================================================
2025-04-19 13:50:23,092 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-19 13:50:36,047 - CpaQuebecParser - DEBUG - Пауза на 1.66 сек. (после driver.get)
2025-04-19 13:50:37,916 - CpaQuebecParser - DEBUG - Пауза на 0.52 сек. (после scroll к кнопке cookie)
2025-04-19 13:51:19,729 - CpaQuebecParser - DEBUG - Пауза на 0.68 сек. (после клика Reset (XPath))
2025-04-19 13:51:24,031 - CpaQuebecParser - DEBUG - Пауза на 0.96 сек. (после очистки формы)
2025-04-19 13:51:30,263 - CpaQuebecParser - DEBUG - Пауза на 2.39 сек. (перед retry 2 для категории Individuals)
2025-04-19 13:51:32,930 - CpaQuebecParser - DEBUG - Пауза на 4.72 сек. (перед retry 3 для категории Individuals)
2025-04-19 13:51:36,746 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
