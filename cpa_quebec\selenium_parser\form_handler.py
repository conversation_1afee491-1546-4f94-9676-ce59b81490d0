import logging
import re
from typing import List, Tuple, Optional, Dict, Any
from playwright.sync_api import Page, Locator, TimeoutError as PlaywrightTimeoutError
from .navigation import Navigator # Для сохранения отладки
from ..utils import random_sleep, sanitize_filename
from ..config import SHORT_TIMEOUT, BASE_URL # Убедитесь, что таймауты определены

# --- Основные селекторы (остаются приоритетными) ---
# (Оставляем их здесь для ясности, но основной упор на эвристику в fallback)
LAST_NAME_INPUT_SELECTOR = 'input[placeholder="Last name of CPA"]'
FIRST_NAME_INPUT_SELECTOR = 'input[placeholder="First name of CPA"]'
REGION_SELECT_SELECTOR = 'select[name="region"]'  # если есть, иначе удалить
CITY_INPUT_SELECTOR = 'input[placeholder="City"]'
CATEGORY_CHECKBOX_XPATH_TEMPLATE = 'label:has-text("{category_name}") input[type="checkbox"]'
SEARCH_BUTTON_SELECTOR = 'button:has-text("SEARCH")'
CLEAR_BUTTON_SELECTOR = 'button:has-text("RESET SEARCH")'
ACCEPTING_CLIENTS_CHECKBOX_SELECTOR = 'label:has-text("Accepting new clients") input[type="checkbox"]'
# ACCEPTING_CLIENTS_CHECKBOX_XPATH = "//label[contains(normalize-space(), 'Accepting new clients')]/input[@type='checkbox']"

# --- Параметры для Эвристик ---
HEURISTIC_CONFIG = {
    "input": {
        "last_name": {"keywords": ["nom", "last", "family", "surname"], "label_keywords": ["last name", "nom", "family name"]},
        "first_name": {"keywords": ["prenom", "first", "given"], "label_keywords": ["first name", "prénom", "given name"]},
        "city": {"keywords": ["ville", "city"], "label_keywords": ["city", "ville"]},
        # Добавить другие поля при необходимости
    },
    "select": {
        "region": {"keywords": ["region", "province", "state"], "label_keywords": ["region", "province", "state"]},
    },
    "button": {
        "search": {"keywords": ["search", "find", "rechercher", "trouver"], "roles": ["button", "link"]}, # Иногда поиск - это ссылка
        "clear": {"keywords": ["clear", "reset", "effacer", "réinitialiser"], "roles": ["button", "link"]},
    },
    "checkbox": {
        "accepting_clients": {"label_keywords": ["accepting new clients", "nouveaux clients"]},
        # Категории будут обрабатываться отдельно по тексту
    }
}

# Веса для системы оценки
SCORE_WEIGHTS = {
    "visible": 5,
    "enabled": 3,
    "exact_text": 10,
    "partial_text": 5,
    "label_exact_text": 8,
    "label_partial_text": 4,
    "id_match": 7,
    "name_match": 6,
    "value_match": 4,
    "placeholder_match": 3,
    "aria_label_match": 5,
    "role_match": 2,
}

class FormHandler:
    """Отвечает за взаимодействие с формами поиска."""

    # Селекторы вынесены для удобства (НУЖНО ПРОВЕРИТЬ АКТУАЛЬНОСТЬ!)
    LAST_NAME_SELECTOR = "#dnn_ctr1009_FindCpa_txtLastName"
    FIRST_NAME_SELECTOR = "#dnn_ctr1009_FindCpa_txtFirstName"
    CITY_SELECTOR = "#dnn_ctr1009_FindCpa_txtCity"
    REGION_DROPDOWN_SELECTOR = "#dnn_ctr1009_FindCpa_ddlRegion"
    CATEGORIES_CONTAINER_SELECTOR = "#dnn_ctr1009_FindCpa_cblClients"
    RESULTS_CONTAINER_SELECTOR = "#dnn_ctr1009_FindCpa_pnlResults" # Для ожидания после поиска

    def __init__(self, page: Page, logger: logging.Logger, navigator: Navigator):
        self.page = page
        self.logger = logger
        self.navigator = navigator # Используем для save_debug_info

    # --- Улучшенные Вспомогательные Эвристические Функции ---

    def _score_candidate(self, locator: Locator, criteria: Dict[str, Any]) -> int:
        """Оценивает кандидата на основе набора критериев."""
        score = 0
        try:
            # Базовые проверки состояния
            if locator.is_visible(timeout=500): score += SCORE_WEIGHTS["visible"]
            if locator.is_enabled(timeout=500): score += SCORE_WEIGHTS["enabled"]

            # Проверка текста (для кнопок, ссылок)
            if "text_patterns" in criteria:
                text_content = locator.text_content(timeout=500) or ""
                for pattern in criteria["text_patterns"]:
                    if re.fullmatch(pattern, text_content, re.IGNORECASE):
                        score += SCORE_WEIGHTS["exact_text"]
                        break # Точное совпадение важнее
                    elif re.search(pattern, text_content, re.IGNORECASE):
                        score += SCORE_WEIGHTS["partial_text"]

            # Проверка атрибутов
            for attr, weight_key in [("id", "id_match"), ("name", "name_match"),
                                     ("value", "value_match"), ("placeholder", "placeholder_match"),
                                     ("aria-label", "aria_label_match")]:
                if f"{attr}_keywords" in criteria:
                    attr_value = locator.get_attribute(attr, timeout=500) or ""
                    for keyword in criteria[f"{attr}_keywords"]:
                         # Простое вхождение ключевого слова
                         if keyword.lower() in attr_value.lower():
                             score += SCORE_WEIGHTS[weight_key]
                             break # Достаточно одного совпадения для атрибута

            # Проверка роли (если указана)
            if "roles" in criteria:
                 # Playwright locator.evaluate('el => el.role') может быть ненадёжным
                 # Проще проверить тег или тип для известных ролей
                 tag = locator.evaluate('el => el.tagName.toLowerCase()', timeout=500)
                 el_type = locator.get_attribute('type', timeout=500)
                 current_role = tag
                 if tag == 'input' and el_type in ['button', 'submit', 'reset']:
                     current_role = el_type

                 if current_role in criteria["roles"]:
                     score += SCORE_WEIGHTS["role_match"]

        except PlaywrightTimeoutError:
            self.logger.debug("Таймаут при оценке кандидата, оценка снижена.")
            return score // 2 # Штраф за таймаут
        except Exception as e:
            self.logger.warning(f"Ошибка при оценке кандидата: {e}")
            return 0 # Нулевая оценка при ошибке
        return score

    def _find_best_candidate(self, candidates: Locator, criteria: Dict[str, Any]) -> Optional[Locator]:
        """Находит лучшего кандидата из списка по системе оценки."""
        best_score = -1
        best_candidate = None
        ties = 0 # Счетчик кандидатов с одинаковым лучшим счетом

        candidate_list = candidates.all() # Получаем список локаторов
        self.logger.debug(f"Найдено {len(candidate_list)} кандидатов для оценки.")

        if not candidate_list:
            return None

        scored_candidates = []
        for i, candidate in enumerate(candidate_list):
            score = self._score_candidate(candidate, criteria)
            self.logger.debug(f"Кандидат {i} получил оценку: {score}")
            scored_candidates.append((score, candidate))

        # Сортируем по убыванию оценки
        scored_candidates.sort(key=lambda x: x[0], reverse=True)

        if not scored_candidates:
             return None

        best_score = scored_candidates[0][0]
        best_candidate = scored_candidates[0][1]

        # Проверяем на неоднозначность (если оценка > 0)
        if best_score > 0 and len(scored_candidates) > 1:
            ties = sum(1 for score, _ in scored_candidates if score == best_score)
            if ties > 1:
                self.logger.warning(f"Эвристический поиск нашел {ties} кандидатов с одинаковой высшей оценкой ({best_score}). Возвращаем первого.")
                # Можно добавить логику для разрешения неоднозначности или вернуть None

        if best_score <= 0:
             self.logger.warning("Ни один из кандидатов не получил положительной оценки.")
             return None

        self.logger.info(f"Выбран лучший кандидат с оценкой {best_score}.")
        return best_candidate


    def _find_element_heuristically(self, element_type: str, config_key: str, search_area: Optional[Locator] = None) -> Optional[Locator]:
        """Основная эвристическая функция для поиска элементов формы."""
        self.logger.debug(f"Запуск эвристического поиска для '{element_type}' с ключом '{config_key}'.")
        criteria = HEURISTIC_CONFIG.get(element_type, {}).get(config_key, {})
        if not criteria:
            self.logger.error(f"Конфигурация эвристики для {element_type}/{config_key} не найдена.")
            return None

        target_roles = criteria.get("roles", [element_type]) # По умолчанию роль совпадает с типом
        search_context = search_area or self.page

        # Собираем локаторы по разным признакам
        locators_to_check = []

        # 1. Поиск по тексту/значению (для кнопок)
        if "keywords" in criteria and element_type == "button":
            text_patterns = [re.escape(kw) for kw in criteria["keywords"]]
            regex_pattern = re.compile("|".join(text_patterns), re.IGNORECASE)
            for role in target_roles:
                 # Используем get_by_role и get_by_text для первичного отбора
                 locators_to_check.append(search_context.get_by_role(role, name=regex_pattern))
                 # Также ищем по атрибуту value (для input[type=button/submit])
                 if role == 'input':
                     for kw in criteria["keywords"]:
                         locators_to_check.append(search_context.locator(f"input[type='submit'][value~='{kw}' i], input[type='button'][value~='{kw}' i]"))


        # 2. Поиск по атрибутам (для input, select)
        if "keywords" in criteria and element_type in ["input", "select"]:
             attr_selectors = []
             for attr in ["id", "name", "placeholder"]: # Приоритетные атрибуты
                 for keyword in criteria["keywords"]:
                     attr_selectors.append(f"{element_type}[{attr}*='{keyword}' i]") # *= содержит (регистронезависимо)
             if attr_selectors:
                 locators_to_check.append(search_context.locator(", ".join(attr_selectors)))

        # 3. Поиск по связанному Label (для input, select, checkbox)
        if "label_keywords" in criteria:
            label_patterns = [re.escape(kw) for kw in criteria["label_keywords"]]
            regex_pattern = re.compile("|".join(label_patterns), re.IGNORECASE)
            # Ищем label по тексту
            labels = search_context.get_by_text(regex_pattern).locator("xpath=./ancestor-or-self::label")
            for label in labels.all():
                label_for = label.get_attribute("for")
                target_element = None
                if label_for:
                    # Ищем элемент по id, указанному в 'for'
                    target_element = search_context.locator(f"#{label_for}")
                else:
                    # Ищем элемент внутри label
                    target_element = label.locator(element_type) # Простой поиск внутри

                if target_element and target_element.count() > 0:
                    # Добавляем найденный через label элемент как кандидата
                    # Передаем информацию о label для оценки
                    criteria['label_text'] = label.text_content() # Для скоринга
                    locators_to_check.append(target_element)


        # Объединяем все найденные локаторы (нужно быть осторожным с дубликатами)
        # Playwright не предоставляет простого способа объединить локаторы без дубликатов
        # Поэтому будем оценивать все найденные элементы из .all() каждого локатора
        all_candidates_locator = None
        if locators_to_check:
             # Создаем объединенный локатор (может содержать дубликаты, но оценка разберется)
             # Это не идеальный способ, но простой для реализации
             combined_selector = ", ".join([loc.locator_string for loc in locators_to_check if hasattr(loc, 'locator_string')]) # Ненадежно
             # Лучше собрать все элементы и оценить уникальные
             unique_elements = {}
             for loc in locators_to_check:
                 try:
                     elements = loc.all(timeout=1000) # Короткий таймаут для сбора
                     for el in elements:
                         # Используем внутренний ID элемента Playwright для уникальности
                         element_handle = el._element_handle
                         if element_handle not in unique_elements:
                              unique_elements[element_handle] = el
                 except Exception as e:
                     self.logger.debug(f"Ошибка при сборе кандидатов из локатора: {e}")

             if unique_elements:
                  # Создаем новый локатор из уникальных элементов (сложно и не рекомендуется API)
                  # Проще передать список элементов на оценку
                  return self._find_best_candidate_from_list(list(unique_elements.values()), criteria)


        self.logger.warning(f"Не удалось собрать кандидатов для эвристического поиска {element_type}/{config_key}.")
        return None


    def _find_best_candidate_from_list(self, candidates: List[Locator], criteria: Dict[str, Any]) -> Optional[Locator]:
         """Находит лучшего кандидата из списка локаторов."""
         # Эта функция дублирует часть логики _find_best_candidate, но работает со списком
         best_score = -1
         best_candidate = None
         ties = 0

         if not candidates:
             self.logger.debug("Список кандидатов для оценки пуст.")
             return None

         scored_candidates = []
         for i, candidate in enumerate(candidates):
             score = self._score_candidate(candidate, criteria)
             self.logger.debug(f"Кандидат {i} (из списка) получил оценку: {score}")
             scored_candidates.append((score, candidate))

         scored_candidates.sort(key=lambda x: x[0], reverse=True)

         if not scored_candidates or scored_candidates[0][0] <= 0:
             self.logger.warning("Ни один из кандидатов в списке не получил положительной оценки.")
             return None

         best_score = scored_candidates[0][0]
         best_candidate = scored_candidates[0][1]

         if len(scored_candidates) > 1:
             ties = sum(1 for score, _ in scored_candidates if score == best_score)
             if ties > 1:
                 self.logger.warning(f"Эвристический поиск (из списка) нашел {ties} кандидатов с одинаковой высшей оценкой ({best_score}). Возвращаем первого.")

         self.logger.info(f"Выбран лучший кандидат (из списка) с оценкой {best_score}.")
         return best_candidate


    def _find_checkbox_by_label_heuristic(self, label_text: str, search_area: Optional[Locator] = None) -> Optional[Locator]:
        """Улучшенная эвристика для поиска чекбокса по тексту связанного label."""
        self.logger.debug(f"Запуск эвристического поиска чекбокса по тексту label: '{label_text}'")
        search_context = search_area or self.page
        candidates = []
        scores = []

        try:
            # Ищем label, содержащий текст (регистронезависимо)
            # Отдаем предпочтение точному совпадению текста
            exact_label_pattern = re.compile(f"^{re.escape(label_text)}$", re.IGNORECASE)
            partial_label_pattern = re.compile(re.escape(label_text), re.IGNORECASE)

            # Сначала ищем точные совпадения
            exact_labels = search_context.get_by_text(exact_label_pattern).locator("xpath=./ancestor-or-self::label")
            # Затем частичные, исключая уже найденные точные (сложно сделать эффективно)
            partial_labels = search_context.get_by_text(partial_label_pattern).locator("xpath=./ancestor-or-self::label")

            processed_labels = {} # Чтобы не обрабатывать один и тот же label дважды

            for label_locator in [exact_labels, partial_labels]:
                is_exact_match = (label_locator == exact_labels)
                for label in label_locator.all():
                    label_handle = label._element_handle
                    if label_handle in processed_labels: continue
                    processed_labels[label_handle] = True

                    label_for = label.get_attribute("for")
                    checkbox = None
                    score = 0

                    if label_for:
                        checkbox = search_context.locator(f"input[type='checkbox'][id='{label_for}']")
                    else:
                        checkbox = label.locator("input[type='checkbox']") # Внутри label

                    if checkbox and checkbox.count() == 1:
                        cb_locator = checkbox.first
                        # Оцениваем найденный чекбокс
                        score += SCORE_WEIGHTS["visible"] if cb_locator.is_visible(timeout=500) else 0
                        score += SCORE_WEIGHTS["enabled"] if cb_locator.is_enabled(timeout=500) else 0
                        score += SCORE_WEIGHTS["label_exact_text"] if is_exact_match else SCORE_WEIGHTS["label_partial_text"]
                        # Можно добавить оценку близости DOM, но это сложно
                        candidates.append(cb_locator)
                        scores.append(score)
                        self.logger.debug(f"Найден кандидат-чекбокс для '{label_text}' с оценкой {score} (точное совпадение label: {is_exact_match}).")
                    elif checkbox and checkbox.count() > 1:
                         self.logger.warning(f"Найдено несколько чекбоксов ({checkbox.count()}) для label '{label_text}'. Пропускаем.")


            if not candidates:
                self.logger.warning(f"Эвристика не нашла чекбоксов для label '{label_text}'.")
                return None

            # Выбираем лучший по оценке
            best_score_idx = scores.index(max(scores))
            best_candidate = candidates[best_score_idx]
            # Проверка на неоднозначность
            if scores.count(max(scores)) > 1:
                 self.logger.warning(f"Эвристика нашла несколько чекбоксов для '{label_text}' с одинаковой высшей оценкой. Возвращаем первый.")

            self.logger.info(f"Выбран лучший чекбокс для '{label_text}' с оценкой {max(scores)}.")
            return best_candidate

        except Exception as e:
            self.logger.error(f"Ошибка во время эвристического поиска чекбокса для '{label_text}': {e}")
            return None


    # --- Обновленные Методы с Fallback на Эвристику ---

    def _get_element(self, primary_selector: str, heuristic_func, *heuristic_args, **heuristic_kwargs) -> Optional[Locator]:
        """Обобщенная функция для получения элемента: сначала по селектору, потом эвристически."""
        element = None
        try:
            element = self.page.locator(primary_selector)
            # Быстрая проверка видимости/существования
            element.wait_for(state="attached", timeout=SHORT_TIMEOUT) # Ждем появления в DOM
            # element.wait_for(state="visible", timeout=SHORT_TIMEOUT) # Можно добавить, но может замедлить
            self.logger.debug(f"Элемент найден по основному селектору: {primary_selector}")
            return element.first # Возвращаем первый, если их несколько по селектору
        except PlaywrightTimeoutError:
            self.logger.warning(f"Таймаут при поиске по основному селектору '{primary_selector}'. Запуск эвристики.")
            element = heuristic_func(*heuristic_args, **heuristic_kwargs)
        except Exception as e:
            self.logger.error(f"Ошибка при поиске по основному селектору '{primary_selector}': {e}. Запуск эвристики.")
            element = heuristic_func(*heuristic_args, **heuristic_kwargs)

        if not element:
             self.logger.error(f"Не удалось найти элемент ни по селектору '{primary_selector}', ни эвристически.")
             # self.navigator.save_debug_info(f"element_not_found_{primary_selector.replace(' ','_')}")
             return None
        else:
             self.logger.info(f"Элемент найден с помощью эвристики.")
             return element # Эвристическая функция уже должна вернуть лучший Locator

    def select_category(self, category_name: str) -> None:
        """Выбирает чекбокс категории."""
        self.logger.info(f"Выбор категории: {category_name}")

        # Сначала пробуем прямой XPath селектор с точным текстом
        try:
            # Ищем любой элемент с точным текстом категории
            exact_text_element = self.page.locator(f'label:has-text("{category_name}")')
            if exact_text_element.count() > 0:
                # Проверяем, есть ли чекбокс внутри этого элемента
                checkbox = exact_text_element.locator('input[type="checkbox"]')
                if checkbox.count() > 0:
                    self.logger.info(f"Найден чекбокс для категории '{category_name}' по точному тексту")
                    checkbox.first.check()
                    return

                # Если чекбокс не найден внутри, проверяем атрибут for
                label_for = exact_text_element.first.get_attribute("for")
                if label_for:
                    checkbox = self.page.locator(f'#{label_for}')
                    if checkbox.count() > 0 and checkbox.first.get_attribute("type") == "checkbox":
                        self.logger.info(f"Найден чекбокс для категории '{category_name}' по атрибуту for")
                        checkbox.first.check()
                        return
        except Exception as e:
            self.logger.warning(f"Ошибка при поиске чекбокса по точному тексту: {e}")

        # Если не нашли по точному совпадению, ищем по частичному
        try:
            # Ищем все лейблы в форме
            labels = self.page.locator("form label")
            for i in range(labels.count()):
                label = labels.nth(i)
                text = label.inner_text().strip()

                # Проверяем, содержит ли текст лейбла название категории
                if category_name.lower() in text.lower():
                    # Проверяем, есть ли чекбокс внутри лейбла
                    checkbox = label.locator('input[type="checkbox"]')
                    if checkbox.count() > 0:
                        self.logger.info(f"Найден чекбокс для категории '{category_name}' внутри лейбла")
                        checkbox.first.check()
                        return

                    # Если чекбокс не найден внутри, проверяем атрибут for
                    label_for = label.get_attribute("for")
                    if label_for:
                        checkbox = self.page.locator(f'#{label_for}')
                        if checkbox.count() > 0 and checkbox.first.get_attribute("type") == "checkbox":
                            self.logger.info(f"Найден чекбокс для категории '{category_name}' по атрибуту for")
                            checkbox.first.check()
                            return
        except Exception as e:
            self.logger.warning(f"Ошибка при поиске чекбокса по частичному совпадению: {e}")

        # Если все методы не сработали, пробуем последний вариант - поиск по XPath
        try:
            xpath_selector = f"//label[contains(text(), '{category_name}')]/input[@type='checkbox'] | //label[contains(text(), '{category_name}')]//input[@type='checkbox']"
            checkbox = self.page.locator(xpath_selector)
            if checkbox.count() > 0:
                self.logger.info(f"Найден чекбокс для категории '{category_name}' по XPath")
                checkbox.first.check()
                return
        except Exception as e:
            self.logger.error(f"Ошибка при поиске чекбокса по XPath: {e}")

        # Если ничего не нашли, сообщаем об ошибке
        self.logger.error(f"Не удалось найти чекбокс для категории '{category_name}'")
        # Создаем скриншот для отладки
        self.page.screenshot(path=f'debug_screenshot_{category_name}_not_found.png', full_page=True)

    def click_captcha(self) -> None:
        self.logger.info("Клик по reCAPTCHA checkbox")
        try:
            frame = self.page.frame_locator('iframe[title="reCAPTCHA"]')
            checkbox = frame.locator('#recaptcha-anchor')
            checkbox.click(timeout=10000)
            self.logger.info("Клик по reCAPTCHA выполнен")
            self.page.screenshot(path='debug_screenshot_captcha_clicked.png', full_page=True)
            return
        except Exception as e:
            self.logger.error(f"Ошибка при клике по reCAPTCHA: {e}")
            self.page.screenshot(path='debug_screenshot_captcha_error.png', full_page=True)
            # Пробуем альтернативный способ
            try:
                # Попробуем найти любой фрейм reCAPTCHA
                frames = self.page.frames
                for frame in frames:
                    if "recaptcha" in frame.url.lower():
                        self.logger.info(f"Найден фрейм reCAPTCHA: {frame.url}")
                        try:
                            # Пробуем найти чекбокс внутри фрейма
                            checkbox = frame.locator('.recaptcha-checkbox')
                            if checkbox.count() > 0:
                                checkbox.first.click(timeout=5000)
                                self.logger.info("Клик по альтернативному чекбоксу reCAPTCHA выполнен")
                                self.page.screenshot(path='debug_screenshot_captcha_clicked_alt.png', full_page=True)
                                return
                        except Exception as inner_e:
                            self.logger.warning(f"Ошибка при клике по альтернативному чекбоксу: {inner_e}")
            except Exception as alt_e:
                self.logger.error(f"Ошибка при поиске альтернативного фрейма reCAPTCHA: {alt_e}")

            # Если все методы не сработали, сообщаем об этом
            self.logger.error("Не удалось найти и кликнуть по reCAPTCHA")

    def click_search_button(self) -> None:
        self.logger.info("Клик по кнопке SEARCH")

        # Сначала пробуем по основному селектору
        try:
            button = self.page.locator(SEARCH_BUTTON_SELECTOR)
            if button.count() > 0 and button.first.is_visible():
                button.first.click(timeout=10000)
                self.logger.info("Кнопка SEARCH нажата по основному селектору")
                return
        except Exception as e:
            self.logger.warning(f"Ошибка при клике по основному селектору кнопки SEARCH: {e}")

        # Пробуем альтернативные селекторы
        selectors = [
            'a.button.radius[data-webform-action="Rechercher"]',
            'button:has-text("SEARCH")',
            'input[type="submit"][value="SEARCH"]',
            'input[type="button"][value="SEARCH"]',
            'a:has-text("SEARCH")',
            'button.search-button',
            'button[type="submit"]',
            'input[type="submit"]'
        ]

        for selector in selectors:
            try:
                button = self.page.locator(selector)
                if button.count() > 0 and button.first.is_visible():
                    button.first.click(timeout=5000)
                    self.logger.info(f"Кнопка SEARCH нажата по селектору: {selector}")
                    return
            except Exception as e:
                self.logger.debug(f"Ошибка при клике по селектору {selector}: {e}")

        # Если ни один селектор не сработал, пробуем найти по тексту
        try:
            # Ищем любой элемент с текстом SEARCH
            search_elements = self.page.get_by_text(re.compile(r"\bSEARCH\b", re.IGNORECASE))
            if search_elements.count() > 0:
                for i in range(search_elements.count()):
                    element = search_elements.nth(i)
                    tag_name = element.evaluate('el => el.tagName.toLowerCase()')
                    if tag_name in ['button', 'a', 'input']:
                        element.click(timeout=5000)
                        self.logger.info(f"Кнопка SEARCH нажата по тексту (элемент {tag_name})")
                        return
        except Exception as e:
            self.logger.error(f"Ошибка при поиске кнопки SEARCH по тексту: {e}")

        # Если все методы не сработали, сообщаем об ошибке
        self.logger.error("Не удалось найти и нажать кнопку SEARCH")
        # Создаем скриншот для отладки
        self.page.screenshot(path='debug_screenshot_search_button_not_found.png', full_page=True)

    def click_search(self) -> None:
        """Нажимает кнопку поиска."""
        self.logger.info("Нажатие кнопки поиска.")
        search_button = self._get_element(
            SEARCH_BUTTON_SELECTOR,
            self._find_element_heuristically,
            element_type="button", config_key="search"
        )

        if not search_button:
            raise ValueError("Кнопка поиска не найдена.")

        try:
            self.logger.info("Клик по кнопке поиска.")
            search_button.click(timeout=SHORT_TIMEOUT)
        except Exception as e:
            self.logger.error(f"Ошибка при клике на кнопку поиска: {e}")
            raise

    def fill_search_criteria(self, last_name: str | None, first_name: str | None, region: str | None, city: str | None) -> None:
        """Заполняет поля поиска, используя эвристику при необходимости."""
        fields = {
            "last_name": (last_name, LAST_NAME_INPUT_SELECTOR),
            "first_name": (first_name, FIRST_NAME_INPUT_SELECTOR),
            "city": (city, CITY_INPUT_SELECTOR),
        }
        select_fields = {
             "region": (region, REGION_SELECT_SELECTOR),
        }

        # Заполнение input полей
        for key, (value, selector) in fields.items():
            if value:
                self.logger.info(f"Заполнение поля '{key}': {value}")
                input_element = self._get_element(
                    selector,
                    self._find_element_heuristically,
                    element_type="input", config_key=key
                )
                if input_element:
                    try:
                        input_element.fill(value, timeout=SHORT_TIMEOUT)
                    except Exception as e:
                         self.logger.error(f"Ошибка при заполнении поля '{key}': {e}")
                else:
                     self.logger.error(f"Не удалось найти поле ввода для '{key}'.")

        # Выбор из select
        for key, (value, selector) in select_fields.items():
             if value:
                 self.logger.info(f"Выбор опции '{key}': {value}")
                 select_element = self._get_element(
                     selector,
                     self._find_element_heuristically,
                     element_type="select", config_key=key
                 )
                 if select_element:
                     try:
                         # Выбор по тексту опции (может потребоваться эвристика и для опций)
                         select_element.select_option(label=value, timeout=SHORT_TIMEOUT)
                     except Exception as e:
                          self.logger.error(f"Ошибка при выборе опции для '{key}': {e}")
                 else:
                      self.logger.error(f"Не удалось найти выпадающий список для '{key}'.")


    def select_accepting_new_clients(self) -> None:
        """Выбирает чекбокс 'Accepting new clients'."""
        self.logger.info("Выбор чекбокса 'Accepting new clients'.")
        checkbox = self._get_element(
            ACCEPTING_CLIENTS_CHECKBOX_SELECTOR, # Или XPath, если он был определен
            self._find_checkbox_by_label_heuristic,
            label_text="Accepting new clients" # Используем текст напрямую
            # Или можно использовать config_key:
            # self._find_element_heuristically, element_type="checkbox", config_key="accepting_clients"
            # Но _find_checkbox_by_label_heuristic более специфичен
        )

        if not checkbox:
            self.logger.error("Не удалось найти чекбокс 'Accepting new clients'.")
            # Решаем, критична ли ошибка. Если да: raise ValueError(...)
            return

        try:
            if not checkbox.is_checked(timeout=SHORT_TIMEOUT):
                self.logger.info("Клик по чекбоксу 'Accepting new clients'.")
                checkbox.check(timeout=SHORT_TIMEOUT)
                self.page.wait_for_timeout(500)
            else:
                self.logger.info("Чекбокс 'Accepting new clients' уже выбран.")
        except Exception as e:
            self.logger.error(f"Ошибка при клике на чекбокс 'Accepting new clients': {e}")
            # Возможно, стоит перевыбросить исключение, если это критично
            # raise

    def clear(self) -> None:
        """Очищает форму, используя кнопку или ручную очистку."""
        self.logger.info("Очистка формы.")
        clear_button = self._get_element(
            CLEAR_BUTTON_SELECTOR,
            self._find_element_heuristically,
            element_type="button", config_key="clear"
        )

        if clear_button:
            try:
                self.logger.info("Нажатие кнопки очистки формы.")
                clear_button.click(timeout=SHORT_TIMEOUT)
                self.page.wait_for_timeout(1000) # Пауза после очистки
                return # Успешно очистили кнопкой
            except Exception as e:
                self.logger.warning(f"Не удалось нажать кнопку очистки ({e}). Пробуем очистить поля вручную.")

        # Запасной вариант: ручная очистка
        self.logger.info("Выполнение ручной очистки полей формы.")
        fields_to_clear_selectors = [
            LAST_NAME_INPUT_SELECTOR,
            FIRST_NAME_INPUT_SELECTOR,
            CITY_INPUT_SELECTOR,
            # Добавить другие поля ввода, если они есть
        ]
        checkboxes_to_uncheck_selectors = [
             ACCEPTING_CLIENTS_CHECKBOX_SELECTOR,
             # Можно добавить селектор для всех чекбоксов категорий, например:
             "//div[contains(@class,'client-categories')]//input[@type='checkbox']" # Пример XPath
        ]
        selects_to_reset_selectors = [
             REGION_SELECT_SELECTOR,
        ]

        for selector in fields_to_clear_selectors:
            try:
                element = self.page.locator(selector)
                if element.is_visible(timeout=500):
                    element.clear(timeout=SHORT_TIMEOUT)
            except Exception: pass # Игнорируем ошибки при ручной очистке отдельных полей

        for selector in checkboxes_to_uncheck_selectors:
             try:
                 checkboxes = self.page.locator(selector).all(timeout=500)
                 for cb in checkboxes:
                     if cb.is_checked(timeout=500):
                         cb.uncheck(timeout=SHORT_TIMEOUT)
             except Exception: pass

        for selector in selects_to_reset_selectors:
             try:
                 element = self.page.locator(selector)
                 if element.is_visible(timeout=500):
                      # Сброс на первую опцию (обычно пустая или "выберите")
                      element.select_option(index=0, timeout=SHORT_TIMEOUT)
             except Exception: pass
