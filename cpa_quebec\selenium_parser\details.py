import logging
import traceback
import re
from typing import Optional, Dict, List

# --- Заменяем импорты Playwright на Selenium ---
# from playwright.sync_api import Page, Locator
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, NoSuchElementException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from ..utils import random_sleep, clean_text, extract_emails, extract_phones, sanitize_filename
from ..config import MAX_RETRY_ATTEMPTS, RETRY_DELAY, MEDIUM_TIMEOUT_SEC, SHORT_TIMEOUT_SEC # Добавляем таймауты
from .navigation import Navigator # Navigator уже адаптирован

class DetailsParser:
    """Извлекает детальную информацию со страницы профиля CPA (Selenium)."""

    # --- Селекторы (НУЖНО ПРОВЕРИТЬ АКТУАЛЬНОСТЬ!) ---
    # Оставляем CSS селекторы
    NAME_SELECTOR = 'h1.profile-name'
    TITLE_SELECTOR = '.profile-title'
    COMPANY_LINK_SELECTOR = '.profile-company a'
    COMPANY_TEXT_SELECTOR = '.profile-company' # Запасной, если ссылки нет
    ADDRESS_SELECTOR = '.profile-address'
    PHONE_SELECTOR = '.profile-phone'
    EMAIL_SELECTOR = '.profile-email a[href^="mailto:"]' # Ищем ссылку mailto
    EMAIL_FALLBACK_SELECTOR = '.profile-email' # Запасной, если ссылки нет
    WEBSITE_SELECTOR = '.profile-website a'
    WEBSITE_FALLBACK_SELECTOR = '.profile-website'
    LANGUAGES_SELECTOR = '.profile-languages' # Обычно содержит текст
    LANGUAGES_LIST_SELECTOR = '.profile-languages li' # Если языки в списке
    PRACTICE_AREAS_SELECTOR = '.profile-practice-areas li' # Области как список
    CLIENTS_SELECTOR = '.profile-clients li' # Клиенты как список
    # Добавим селектор для ожидания загрузки основной части контента
    PROFILE_CONTAINER_SELECTOR = 'div.profile-details' # Примерный селектор контейнера

    # --- Обновляем __init__ ---
    def __init__(self, driver: WebDriver, logger: logging.Logger, navigator: Navigator, debug: bool = False):
        self.driver = driver
        self.logger = logger
        self.navigator = navigator
        self.debug = debug
        self.wait_medium = WebDriverWait(self.driver, MEDIUM_TIMEOUT_SEC)
        self.wait_short = WebDriverWait(self.driver, SHORT_TIMEOUT_SEC)

    # --- Адаптируем parse ---
    def parse(self, profile_url: str) -> Optional[Dict]:
        """Переходит на страницу профиля и извлекает данные (Selenium)."""
        self.logger.info(f"Парсинг детальной страницы (Selenium): {profile_url}")
        details = {}
        retries = 0

        while retries < MAX_RETRY_ATTEMPTS:
            if retries > 0:
                self.logger.warning(f"Повторная попытка {retries}/{MAX_RETRY_ATTEMPTS} для детальной страницы {profile_url}...")
                random_sleep(RETRY_DELAY * retries, RETRY_DELAY * retries * 1.5)

            # Используем адаптированный navigator.goto
            # Убираем wait_until='load', т.к. Selenium ждет document.readyState == 'complete' по умолчанию
            if not self.navigator.goto(profile_url):
                self.logger.error(f"Не удалось загрузить детальную страницу {profile_url} (попытка {retries + 1}).")
                # Сохраняем скриншот/html через navigator
                safe_filename = sanitize_filename(profile_url)
                self.navigator.save_debug_info(f"detail_page_load_fail_{safe_filename}_{retries+1}")
                retries += 1
                continue

            try:
                 # --- Ожидание загрузки основного контента (необязательно, но может повысить стабильность) ---
                 try:
                     self.wait_medium.until(
                         EC.presence_of_element_located((By.CSS_SELECTOR, self.PROFILE_CONTAINER_SELECTOR))
                     )
                     self.logger.debug(f"Контейнер профиля '{self.PROFILE_CONTAINER_SELECTOR}' найден.")
                 except TimeoutException:
                      self.logger.warning(f"Контейнер профиля '{self.PROFILE_CONTAINER_SELECTOR}' не найден за {MEDIUM_TIMEOUT_SEC} сек. Данные могут быть неполными.")
                      # Не прерываем, продолжаем попытку извлечения
                 random_sleep(0.5, 1.0, "после ожидания контейнера профиля")

                 # --- Извлечение данных с использованием find_elements ---

                 # Имя
                 name_elements = self.driver.find_elements(By.CSS_SELECTOR, self.NAME_SELECTOR)
                 if name_elements: details['name_detail'] = clean_text(name_elements[0].text)

                 # Должность
                 title_elements = self.driver.find_elements(By.CSS_SELECTOR, self.TITLE_SELECTOR)
                 if title_elements: details['title'] = clean_text(title_elements[0].text)

                 # Компания
                 company_link_elements = self.driver.find_elements(By.CSS_SELECTOR, self.COMPANY_LINK_SELECTOR)
                 if company_link_elements:
                     details['company'] = clean_text(company_link_elements[0].text)
                 else:
                     # Запасной вариант, если ссылка не найдена
                     company_text_elements = self.driver.find_elements(By.CSS_SELECTOR, self.COMPANY_TEXT_SELECTOR)
                     if company_text_elements:
                         details['company'] = clean_text(company_text_elements[0].text)

                 # Адрес
                 address_elements = self.driver.find_elements(By.CSS_SELECTOR, self.ADDRESS_SELECTOR)
                 if address_elements: details['address'] = clean_text(address_elements[0].text)

                 # Телефон (извлекаем из текста)
                 phone_elements = self.driver.find_elements(By.CSS_SELECTOR, self.PHONE_SELECTOR)
                 if phone_elements:
                      phone_text = phone_elements[0].text
                      details['phone_detail'] = extract_phones(phone_text) # Используем функцию из utils
                 else:
                     details['phone_detail'] = []

                 # Электронная почта
                 email_elements = self.driver.find_elements(By.CSS_SELECTOR, self.EMAIL_SELECTOR)
                 if email_elements:
                      # Пытаемся извлечь из href ссылки mailto:
                      mail_href = email_elements[0].get_attribute('href')
                      if mail_href and mail_href.startswith('mailto:'):
                           details['email_detail'] = extract_emails(mail_href[7:]) # Убираем mailto:
                      else:
                           # Если href не mailto, пробуем из текста ссылки
                           details['email_detail'] = extract_emails(email_elements[0].text)
                 else:
                     # Запасной вариант, если ссылка не найдена, ищем в тексте блока
                     email_fallback_elements = self.driver.find_elements(By.CSS_SELECTOR, self.EMAIL_FALLBACK_SELECTOR)
                     if email_fallback_elements:
                         details['email_detail'] = extract_emails(email_fallback_elements[0].text)
                     else:
                         details['email_detail'] = []

                 # Веб-сайт
                 website_elements = self.driver.find_elements(By.CSS_SELECTOR, self.WEBSITE_SELECTOR)
                 if website_elements:
                     href = website_elements[0].get_attribute('href')
                     if href: details['website'] = href
                 else:
                     # Запасной вариант - текст из блока
                     website_fallback_elements = self.driver.find_elements(By.CSS_SELECTOR, self.WEBSITE_FALLBACK_SELECTOR)
                     if website_fallback_elements:
                          # Попытка извлечь URL из текста, если это возможно
                          # Здесь может потребоваться более сложная логика или просто сохранить текст
                          details['website'] = clean_text(website_fallback_elements[0].text)

                 # Языки (пробуем из списка или из общего блока)
                 language_list_elements = self.driver.find_elements(By.CSS_SELECTOR, self.LANGUAGES_LIST_SELECTOR)
                 if language_list_elements:
                     details['languages'] = [clean_text(lang.text) for lang in language_list_elements if lang.text]
                 else:
                     languages_block_elements = self.driver.find_elements(By.CSS_SELECTOR, self.LANGUAGES_SELECTOR)
                     if languages_block_elements:
                         # Если это один блок, просто берем его текст
                         details['languages'] = [clean_text(languages_block_elements[0].text)]
                     else:
                         details['languages'] = []

                 # Практические области (из списка li)
                 practice_area_elements = self.driver.find_elements(By.CSS_SELECTOR, self.PRACTICE_AREAS_SELECTOR)
                 if practice_area_elements:
                     details['practice_areas'] = [clean_text(area.text) for area in practice_area_elements if area.text]
                 else:
                     details['practice_areas'] = []

                 # Клиенты (из списка li)
                 client_elements = self.driver.find_elements(By.CSS_SELECTOR, self.CLIENTS_SELECTOR)
                 if client_elements:
                     details['clients'] = [clean_text(client.text) for client in client_elements if client.text]
                 else:
                     details['clients'] = []

                 self.logger.info(f"Успешно извлечены данные с детальной страницы: {profile_url}")
                 # Сохраняем скриншот успешного парсинга
                 safe_filename = sanitize_filename(profile_url)
                 self.navigator.save_debug_info(f"detail_page_success_{safe_filename}")
                 return details

            except StaleElementReferenceException as stale_err:
                  self.logger.warning(f"Элемент устарел (StaleElementReferenceException) при парсинге {profile_url}: {stale_err}. Попытка {retries + 1}")
                  safe_filename = sanitize_filename(profile_url)
                  self.navigator.save_debug_info(f"detail_page_stale_{safe_filename}_{retries+1}")
                  retries += 1
                  continue # Переходим к следующей попытке
            except Exception as e:
                self.logger.error(f"Ошибка при извлечении данных с детальной страницы {profile_url} (попытка {retries + 1}): {e}", exc_info=self.debug)
                safe_filename = sanitize_filename(profile_url)
                self.navigator.save_debug_info(f"detail_page_extract_fail_{safe_filename}_{retries+1}")
                retries += 1
                continue # Переходим к следующей попытке

        # Если все попытки исчерпаны
        self.logger.error(f"Не удалось получить данные с детальной страницы {profile_url} после {MAX_RETRY_ATTEMPTS} попыток.")
        safe_filename = sanitize_filename(profile_url)
        self.navigator.save_debug_info(f"detail_page_failed_all_attempts_{safe_filename}")
        return None
