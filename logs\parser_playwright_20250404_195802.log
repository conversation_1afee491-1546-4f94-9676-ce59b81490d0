2025-04-04 19:58:02,529 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_195802.log
2025-04-04 19:58:02,532 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:58:02,532 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 19:58:02,533 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:58:02,533 - C<PERSON>Q<PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:58:02,533 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:58:02,533 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:58:08,852 - CpaQuebecParser - DEBUG - Пауза на 1.04 сек. (после навигации)
2025-04-04 19:58:22,036 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 1
