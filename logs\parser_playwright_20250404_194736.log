2025-04-04 19:47:36,497 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_194736.log
2025-04-04 19:47:36,504 - C<PERSON><PERSON><PERSON>becParser - INFO - ==================================================
2025-04-04 19:47:36,504 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:47:36,506 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': False, 'accepting_new_clients': False}
2025-04-04 19:47:36,507 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:47:36,507 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-04 19:47:37,928 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): local variable 'retries' referenced before assignment
2025-04-04 19:47:37,928 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 96, in run_parser
    results = cpa_parser.parse(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 192, in parse
    self.results = self._search_cpa(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 243, in _search_cpa
    while retries < MAX_RETRY_ATTEMPTS:
UnboundLocalError: local variable 'retries' referenced before assignment

2025-04-04 19:47:37,929 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
