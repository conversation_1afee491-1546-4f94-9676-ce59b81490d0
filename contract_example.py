#!/usr/bin/env python3
"""
Пример использования контрактного программирования с инвариантами класса
"""
from new03 import invariant, precondition, postcondition

@invariant(
    lambda self: self.balance >= 0,
    "Баланс счета не может быть отрицательным"
)
class BankAccount:
    """Пример класса с инвариантом - банковский счет.
    
    Инвариант: баланс счета всегда должен быть неотрицательным.
    """
    
    def __init__(self, initial_balance: float = 0):
        """Создает новый банковский счет с указанным начальным балансом.
        
        Args:
            initial_balance: Начальный баланс счета (должен быть >= 0)
        """
        if initial_balance < 0:
            raise ValueError("Начальный баланс не может быть отрицательным")
        self.balance = initial_balance
    
    @precondition(
        lambda self, amount: amount > 0,
        "Сумма депозита должна быть положительной"
    )
    @postcondition(
        lambda result, self, amount: self.balance > 0,
        "После депозита баланс должен быть положительным"
    )
    def deposit(self, amount: float) -> float:
        """Вносит деньги на счет.
        
        Args:
            amount: Сумма для внесения (должна быть > 0)
            
        Returns:
            float: Новый баланс счета
            
        Предусловия:
            - Сумма депозита должна быть положительной
            
        Постусловия:
            - После депозита баланс должен быть положительным
        """
        self.balance += amount
        return self.balance
    
    @precondition(
        lambda self, amount: amount > 0,
        "Сумма снятия должна быть положительной"
    )
    @precondition(
        lambda self, amount: self.balance >= amount,
        "Недостаточно средств на счете"
    )
    def withdraw(self, amount: float) -> float:
        """Снимает деньги со счета.
        
        Args:
            amount: Сумма для снятия (должна быть > 0 и <= баланса)
            
        Returns:
            float: Новый баланс счета
            
        Предусловия:
            - Сумма снятия должна быть положительной
            - На счете должно быть достаточно средств
        """
        self.balance -= amount
        return self.balance

if __name__ == "__main__":
    # Демонстрация использования класса с контрактами
    account = BankAccount(100)
    print(f"Начальный баланс: {account.balance}")
    
    # Нормальные операции
    print(f"После депозита: {account.deposit(50)}")
    print(f"После снятия: {account.withdraw(30)}")
    
    try:
        # Нарушение предусловия - отрицательная сумма
        account.deposit(-10)
    except Exception as e:
        print(f"Ошибка при депозите отрицательной суммы: {e}")
    
    try:
        # Нарушение предусловия - недостаточно средств
        account.withdraw(1000)
    except Exception as e:
        print(f"Ошибка при снятии слишком большой суммы: {e}")
    
    try:
        # Нарушение инварианта - прямое изменение баланса
        account.balance = -50
    except Exception as e:
        print(f"Ошибка при установке отрицательного баланса: {e}")
