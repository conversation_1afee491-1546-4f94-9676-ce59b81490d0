2025-04-20 00:08:12,276 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250420_000812.log
2025-04-20 00:08:12,279 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-20 00:08:12,279 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - ==================================================
2025-04-20 00:08:12,279 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-20 00:08:12,279 - <PERSON><PERSON><PERSON>ue<PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-20 00:08:12,279 - CpaQuebecParser - INFO - ==================================================
2025-04-20 00:08:12,279 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-20 00:08:18,385 - CpaQuebecParser - DEBUG - Пауза на 1.18 сек. (после driver.get)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  