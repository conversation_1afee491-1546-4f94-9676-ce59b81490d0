2025-04-04 21:24:31,170 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_212431.log
2025-04-04 21:24:31,172 - <PERSON><PERSON><PERSON><PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 21:24:31,173 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 21:24:31,173 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 21:24:31,173 - C<PERSON>Q<PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 21:24:31,173 - CpaQuebecParser - INFO - ==================================================
2025-04-04 21:24:31,173 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 21:24:43,093 - CpaQuebecParser - DEBUG - Пауза на 1.49 сек. (после driver.get)
2025-04-04 21:24:44,946 - CpaQuebecParser - DEBUG - Пауза на 0.41 сек. (после scroll к кнопке cookie)
2025-04-04 21:24:56,625 - CpaQuebecParser - DEBUG - Пауза на 0.67 сек. (после клика Reset (XPath))
2025-04-04 21:24:59,131 - CpaQuebecParser - DEBUG - Пауза на 0.88 сек. (после очистки формы)
2025-04-04 21:25:01,013 - CpaQuebecParser - DEBUG - Пауза на 3.46 сек. (между категориями, после Individuals)
2025-04-04 21:25:04,534 - CpaQuebecParser - DEBUG - Пауза на 2.59 сек. (перед retry 2 для категории Large companies)
2025-04-04 21:25:05,158 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
