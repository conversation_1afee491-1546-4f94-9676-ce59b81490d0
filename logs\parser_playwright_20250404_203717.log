2025-04-04 20:37:17,704 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_203717.log
2025-04-04 20:37:17,708 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 20:37:17,709 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:37:17,709 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True}
2025-04-04 20:37:17,710 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:37:17,710 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:39:26,452 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
