2025-04-25 17:22:52,719 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250425_172252.log
2025-04-25 17:22:52,722 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Parser - DEBUG - Режим отладки DEBUG включен.
2025-04-25 17:22:52,722 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-25 17:22:52,722 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-25 17:22:52,722 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals', 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-25 17:22:52,722 - CpaQuebecParser - INFO - ==================================================
2025-04-25 17:22:52,722 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-25 17:23:04,768 - CpaQuebecParser - DEBUG - Пауза на 2.06 сек. (после driver.get)
2025-04-25 17:23:06,942 - CpaQuebecParser - DEBUG - Пауза на 0.48 сек. (после scroll к кнопке cookie)
2025-04-25 17:23:48,704 - CpaQuebecParser - DEBUG - Пауза на 0.79 сек. (после клика Reset (XPath))
2025-04-25 17:23:51,260 - CpaQuebecParser - DEBUG - Пауза на 0.68 сек. (после очистки формы)
2025-04-25 17:26:35,031 - CpaQuebecParser - DEBUG - Пауза на 2.01 сек. (между категориями, после Individuals)
2025-04-25 17:26:37,040 - CpaQuebecParser - INFO - Парсинг (Selenium) завершен. Найдено и сохранено записей: 0
2025-04-25 17:26:37,044 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 0
