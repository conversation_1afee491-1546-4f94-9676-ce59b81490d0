#!/usr/bin/env python3
"""
Упрощенный парсер CPA Quebec Directory
"""
import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path
import argparse

from playwright.async_api import async_playwright, Page

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("simple_cpa_parser")

# Константы
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"
OUTPUT_DIR = Path("output")
CLIENT_CATEGORIES = [
    "Individuals",
    "Large companies",
    "NFPOs",
    "Professional firms",
    "Public corporations",
    "Retailers",
    "Self-employed workers",
    "SMEs",
    "Start-ups",
    "Syndicates of co-owners",
]

async def close_cookies_banner(page: Page) -> bool:
    """Закрывает баннер с cookie, если он присутствует."""
    logger.debug("Проверка наличия баннера cookie...")

    cookie_button_selectors = [
        "text=Tout refuser",        # Французский
        "text=Reject All",          # Английский
        "text=Decline All",         # Альтернативный английский
        "button:has-text('Tout refuser')", # Более точный селектор
        "button:has-text('Reject All')",
        "button:has-text('Decline All')",
        "#onetrust-reject-all-handler", # OneTrust specific
        ".cc-btn.cc-dismiss",       # CookieConsent specific
    ]

    for selector in cookie_button_selectors:
        try:
            button = page.locator(selector).first
            await button.wait_for(state="attached", timeout=2000)

            if await button.is_visible(timeout=1500):
                logger.info(f"Найдена кнопка закрытия баннера cookie: {selector}")
                await button.click(timeout=5000, delay=100)
                logger.info("Баннер cookie закрыт.")
                await page.wait_for_timeout(500)
                return True
        except Exception:
            continue

    logger.debug("Баннер cookie не найден или уже закрыт.")
    return False

async def process_category(page: Page, category: str = None) -> list:
    """Обрабатывает одну категорию и возвращает результаты."""
    logger.info(f"Обработка категории: {category or 'Все'}")

    # Переходим на страницу поиска
    logger.info(f"Переход на страницу: {BASE_URL}")
    await page.goto(BASE_URL, wait_until="domcontentloaded")
    await page.wait_for_load_state("networkidle")

    # Закрываем баннер cookie, если он есть
    await close_cookies_banner(page)

    # Если указана категория, выбираем её
    if category:
        logger.info(f"Выбор категории: {category}")
        try:
            # Находим и кликаем на выпадающий список категорий
            category_dropdown = page.locator("select#clientCategory, select[name='clientCategory']")
            await category_dropdown.wait_for(state="visible", timeout=5000)

            # Выбираем категорию
            await category_dropdown.select_option(label=category)
            logger.info(f"Категория '{category}' выбрана успешно")
            await page.wait_for_timeout(1000)
        except Exception as e:
            logger.error(f"Ошибка при выборе категории: {e}")
            return []

    # Нажимаем кнопку поиска
    logger.info("Нажатие кнопки поиска...")
    try:
        # Более точный селектор для кнопки поиска в форме
        search_button = page.locator("form button[type='submit']:has-text('Search')").first

        # Если не нашли, пробуем другие селекторы
        if await search_button.count() == 0:
            logger.info("Пробуем альтернативный селектор для кнопки поиска...")
            search_button = page.locator("form input[type='submit'][value='Search']").first

        # Если все еще не нашли, пробуем JavaScript
        if await search_button.count() == 0:
            logger.info("Используем JavaScript для отправки формы...")
            await page.evaluate("""
                () => {
                    const forms = document.querySelectorAll('form');
                    if (forms.length > 0) {
                        forms[0].submit();
                        return true;
                    }
                    return false;
                }
            """)
            logger.info("Форма отправлена через JavaScript")
        else:
            # Если нашли кнопку, прокручиваем к ней и кликаем
            logger.info("Прокрутка к кнопке поиска...")
            await search_button.scroll_into_view_if_needed()
            await page.wait_for_timeout(1000)  # Ждем завершения прокрутки

            # Пробуем клик через JavaScript, если обычный клик не сработает
            try:
                await search_button.click(timeout=5000)
                logger.info("Кнопка поиска нажата")
            except Exception as click_error:
                logger.warning(f"Не удалось кликнуть на кнопку: {click_error}")
                logger.info("Пробуем клик через JavaScript...")

                # Используем JavaScript для клика
                await page.evaluate("""
                    () => {
                        const buttons = document.querySelectorAll('form button[type="submit"]');
                        for (const btn of buttons) {
                            if (btn.textContent.includes('Search')) {
                                btn.click();
                                return true;
                            }
                        }
                        return false;
                    }
                """)
                logger.info("Клик выполнен через JavaScript")
    except Exception as e:
        logger.error(f"Ошибка при нажатии кнопки поиска: {e}")
        return []

    # Ждем загрузки результатов
    await page.wait_for_load_state("networkidle")
    await page.wait_for_timeout(2000)

    # Собираем результаты
    logger.info("Сбор результатов...")
    results = []

    try:
        # Проверяем наличие результатов
        result_items = page.locator(".search-result-item, .result-card, article.cpa-entry, div.profile-summary, li.item")
        count = await result_items.count()

        if count == 0:
            logger.warning("Результаты не найдены")
            return []

        logger.info(f"Найдено {count} результатов")

        # Собираем данные из каждого результата
        for i in range(count):
            item = result_items.nth(i)

            try:
                # Извлекаем имя
                name_element = item.locator("h2, h3, .name, .title")
                name = await name_element.inner_text() if await name_element.count() > 0 else "Нет имени"

                # Извлекаем URL профиля
                profile_url = None
                link_element = item.locator("a[href*='directory']")
                if await link_element.count() > 0:
                    profile_url = await link_element.get_attribute("href")

                # Добавляем результат
                results.append({
                    "name": name.strip(),
                    "profile_url": profile_url,
                    "category": category,
                    "timestamp": datetime.now().isoformat()
                })

                logger.debug(f"Добавлен результат: {name.strip()}")
            except Exception as e:
                logger.warning(f"Ошибка при обработке результата {i}: {e}")
                continue
    except Exception as e:
        logger.error(f"Ошибка при сборе результатов: {e}")

    logger.info(f"Собрано {len(results)} результатов для категории '{category or 'Все'}'")
    return results

async def main():
    """Основная функция."""
    # Парсинг аргументов командной строки
    parser = argparse.ArgumentParser(description="Упрощенный парсер CPA Quebec Directory")
    parser.add_argument("--visible", action="store_true", help="Запустить браузер в видимом режиме")
    parser.add_argument("--category", type=str, help="Обработать только указанную категорию клиентов")
    parser.add_argument("--all-categories", action="store_true", help="Обработать все категории клиентов")

    args = parser.parse_args()

    # Создаем директорию для результатов, если её нет
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # Определяем категории для обработки
    categories_to_process = []
    if args.all_categories:
        categories_to_process = CLIENT_CATEGORIES
        logger.info(f"Режим: обработка всех {len(categories_to_process)} категорий")
    elif args.category:
        categories_to_process = [args.category]
        logger.info(f"Режим: обработка одной категории: {args.category}")
    else:
        categories_to_process = [""]  # Пустая строка для режима "Все"
        logger.info("Режим: обработка без фильтров категорий ('Все')")

    # Запускаем Playwright
    async with async_playwright() as playwright:
        logger.info("Запуск браузера...")
        browser = await playwright.chromium.launch(headless=not args.visible)

        context = await browser.new_context(
            viewport={"width": 1366, "height": 768},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36"
        )

        page = await context.new_page()
        logger.info("Браузер запущен успешно")

        # Обрабатываем каждую категорию
        all_results = []
        for category in categories_to_process:
            category_display = category if category else "Все"
            logger.info(f"--- Начало обработки категории: {category_display} ---")

            try:
                results = await process_category(page, category)
                logger.info(f"Категория '{category_display}' обработана. Найдено {len(results)} элементов.")
                all_results.extend(results)
                await asyncio.sleep(2)  # Пауза между категориями
            except Exception as e:
                logger.error(f"Ошибка при обработке категории '{category_display}': {e}")
                continue

        # Сохраняем результаты
        if all_results:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            outfile = OUTPUT_DIR / f"cpa_results_{timestamp}.json"

            logger.info(f"Сохранение {len(all_results)} результатов в {outfile}...")
            with open(outfile, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)

            logger.info(f"Результаты успешно сохранены в {outfile}")
        else:
            logger.warning("Нет результатов для сохранения")

        # Закрываем браузер
        await context.close()
        await browser.close()
        logger.info("Браузер закрыт")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Выполнение скрипта прервано пользователем")
    except Exception as e:
        logger.critical(f"Необработанное исключение: {e}", exc_info=True)
