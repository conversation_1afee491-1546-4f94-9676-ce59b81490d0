2025-03-26 19:51:44,025 - C<PERSON>QuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195144.log
2025-03-26 19:51:44,028 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-03-26 19:51:44,028 - C<PERSON>QuebecParser - INFO - ==================================================
2025-03-26 19:51:44,028 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-03-26 19:51:44,028 - C<PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 30, 'page_timeout': 60, 'get_details': True, 'by_category': True}
2025-03-26 19:51:44,028 - CpaQuebecParser - INFO - ==================================================
2025-03-26 19:51:44,029 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-03-26 19:51:44,029 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195144.log
2025-03-26 19:51:44,029 - CpaQuebecParser - INFO - Инициализация парсера CPA Quebec (Playwright)
2025-03-26 19:51:44,030 - CpaQuebecParser - WARNING - API ключ для 2Captcha не найден. Решение капчи будет пропущено.
2025-03-26 19:51:44,030 - CpaQuebecParser - INFO - Запуск браузера Chromium (Headless: True) через Playwright...
2025-03-26 19:51:44,636 - CpaQuebecParser - INFO - Браузер Chromium запущен.
2025-03-26 19:51:44,640 - CpaQuebecParser - INFO - Используется User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********
2025-03-26 19:51:44,667 - CpaQuebecParser - INFO - Контекст браузера создан.
2025-03-26 19:51:44,731 - CpaQuebecParser - INFO - Новая страница создана.
2025-03-26 19:51:44,737 - CpaQuebecParser - INFO - Добавлен init script для скрытия navigator.webdriver.
2025-03-26 19:51:44,737 - CpaQuebecParser - INFO - ============================== ЗАПУСК ПАРСИНГА (Playwright) ==============================
2025-03-26 19:51:44,737 - CpaQuebecParser - INFO - Режим: Парсинг по категориям клиентов.
2025-03-26 19:51:44,738 - CpaQuebecParser - INFO - Запуск парсинга по категориям (Playwright): Arts and culture organizations, Co-ownership syndicates, Elderly individuals, Executives, Farmers, Indigenous communities, Individuals, Large businesses, Medium-sized businesses, Non-profit organizations, Professionals, Self-employed workers, Small businesses, Sports organizations, Start-ups
2025-03-26 19:51:44,738 - CpaQuebecParser - INFO - --- Обработка категории: Arts and culture organizations (1/15) ---
2025-03-26 19:51:44,738 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:45,209 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:45,210 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:45,459 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195145.png
2025-03-26 19:51:45,464 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195145.html
2025-03-26 19:51:45,464 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Arts and culture organizations'. Пропускаем.
2025-03-26 19:51:45,464 - CpaQuebecParser - INFO - --- Обработка категории: Co-ownership syndicates (2/15) ---
2025-03-26 19:51:45,464 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:45,615 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:45,615 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:45,844 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195145.png
2025-03-26 19:51:45,848 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195145.html
2025-03-26 19:51:45,848 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Co-ownership syndicates'. Пропускаем.
2025-03-26 19:51:45,849 - CpaQuebecParser - INFO - --- Обработка категории: Elderly individuals (3/15) ---
2025-03-26 19:51:45,849 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:46,009 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:46,009 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:46,237 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195146.png
2025-03-26 19:51:46,245 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195146.html
2025-03-26 19:51:46,245 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Elderly individuals'. Пропускаем.
2025-03-26 19:51:46,245 - CpaQuebecParser - INFO - --- Обработка категории: Executives (4/15) ---
2025-03-26 19:51:46,245 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:46,436 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:46,437 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:46,694 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195146.png
2025-03-26 19:51:46,699 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195146.html
2025-03-26 19:51:46,699 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Executives'. Пропускаем.
2025-03-26 19:51:46,699 - CpaQuebecParser - INFO - --- Обработка категории: Farmers (5/15) ---
2025-03-26 19:51:46,699 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:46,861 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:46,862 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:47,074 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195146.png
2025-03-26 19:51:47,079 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195146.html
2025-03-26 19:51:47,079 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Farmers'. Пропускаем.
2025-03-26 19:51:47,079 - CpaQuebecParser - INFO - --- Обработка категории: Indigenous communities (6/15) ---
2025-03-26 19:51:47,079 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:47,236 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:47,236 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:47,461 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195147.png
2025-03-26 19:51:47,465 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195147.html
2025-03-26 19:51:47,465 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Indigenous communities'. Пропускаем.
2025-03-26 19:51:47,465 - CpaQuebecParser - INFO - --- Обработка категории: Individuals (7/15) ---
2025-03-26 19:51:47,466 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:47,625 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:47,626 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:47,885 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195147.png
2025-03-26 19:51:47,890 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195147.html
2025-03-26 19:51:47,890 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Individuals'. Пропускаем.
2025-03-26 19:51:47,891 - CpaQuebecParser - INFO - --- Обработка категории: Large businesses (8/15) ---
2025-03-26 19:51:47,891 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:48,044 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:48,044 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:48,260 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195148.png
2025-03-26 19:51:48,272 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195148.html
2025-03-26 19:51:48,272 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Large businesses'. Пропускаем.
2025-03-26 19:51:48,273 - CpaQuebecParser - INFO - --- Обработка категории: Medium-sized businesses (9/15) ---
2025-03-26 19:51:48,273 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:48,440 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:48,440 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:48,671 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195148.png
2025-03-26 19:51:48,675 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195148.html
2025-03-26 19:51:48,675 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Medium-sized businesses'. Пропускаем.
2025-03-26 19:51:48,675 - CpaQuebecParser - INFO - --- Обработка категории: Non-profit organizations (10/15) ---
2025-03-26 19:51:48,675 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:48,839 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:48,839 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:49,069 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195148.png
2025-03-26 19:51:49,073 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195148.html
2025-03-26 19:51:49,073 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Non-profit organizations'. Пропускаем.
2025-03-26 19:51:49,073 - CpaQuebecParser - INFO - --- Обработка категории: Professionals (11/15) ---
2025-03-26 19:51:49,074 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:49,229 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:49,229 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:49,467 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195149.png
2025-03-26 19:51:49,474 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195149.html
2025-03-26 19:51:49,474 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Professionals'. Пропускаем.
2025-03-26 19:51:49,474 - CpaQuebecParser - INFO - --- Обработка категории: Self-employed workers (12/15) ---
2025-03-26 19:51:49,475 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:49,633 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:49,633 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:49,864 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195149.png
2025-03-26 19:51:49,867 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195149.html
2025-03-26 19:51:49,868 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Self-employed workers'. Пропускаем.
2025-03-26 19:51:49,868 - CpaQuebecParser - INFO - --- Обработка категории: Small businesses (13/15) ---
2025-03-26 19:51:49,868 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:50,033 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:50,034 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:50,266 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195150.png
2025-03-26 19:51:50,274 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195150.html
2025-03-26 19:51:50,274 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Small businesses'. Пропускаем.
2025-03-26 19:51:50,274 - CpaQuebecParser - INFO - --- Обработка категории: Sports organizations (14/15) ---
2025-03-26 19:51:50,274 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:50,428 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:50,428 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:50,696 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195150.png
2025-03-26 19:51:50,707 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195150.html
2025-03-26 19:51:50,709 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Sports organizations'. Пропускаем.
2025-03-26 19:51:50,709 - CpaQuebecParser - INFO - --- Обработка категории: Start-ups (15/15) ---
2025-03-26 19:51:50,710 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:51:50,888 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:51:50,890 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:51:51,144 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195150.png
2025-03-26 19:51:51,147 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195150.html
2025-03-26 19:51:51,148 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Start-ups'. Пропускаем.
2025-03-26 19:51:51,148 - CpaQuebecParser - INFO - Парсинг по категориям завершен. Всего найдено 0 уникальных записей.
2025-03-26 19:51:51,148 - CpaQuebecParser - WARNING - Поиск не дал результатов или произошла ошибка.
2025-03-26 19:51:51,148 - CpaQuebecParser - WARNING - Нет данных для сохранения.
2025-03-26 19:51:51,148 - CpaQuebecParser - INFO - Общее время парсинга (Playwright): 6.41 секунд.
2025-03-26 19:51:51,148 - CpaQuebecParser - INFO - ============================== ЗАВЕРШЕНИЕ ПАРСИНГА (Playwright) ==============================
2025-03-26 19:51:51,148 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-03-26 19:51:51,149 - CpaQuebecParser - INFO - Закрытие браузера и Playwright...
2025-03-26 19:51:51,211 - CpaQuebecParser - INFO - Браузер Playwright закрыт.
2025-03-26 19:51:51,226 - CpaQuebecParser - INFO - Playwright остановлен.
2025-03-26 19:51:51,227 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
