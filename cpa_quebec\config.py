#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# Основной URL каталога CPA Quebec
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"

# Корень проекта (определяется относительно текущего файла config.py)
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
OUTPUT_DIR = os.path.join(PROJECT_ROOT, "output")
LOGS_DIR = os.path.join(PROJECT_ROOT, "logs")
DEBUG_DIR = os.path.join(PROJECT_ROOT, "debug_playwright")

# --- Настройки Playwright и Таймауты ---
# User-Agent для использования (можно оставить пустым для User-Agent по умолчанию Playwright)
# DEFAULT_USER_AGENT определён ниже, после USER_AGENT
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
# Таймауты в миллисекундах
SHORT_TIMEOUT = 10 * 1000  # 10 секунд (для ожидания элементов)
MEDIUM_TIMEOUT = 20 * 1000 # 20 секунд
LONG_TIMEOUT = 60 * 1000   # 60 секунд (для загрузки страниц, решения капчи)
PAGE_LOAD_TIMEOUT = 90 * 1000 # 90 секунд (максимальное время загрузки страницы)
BROWSER_LAUNCH_TIMEOUT = 120 * 1000 # 120 секунд (максимальное время запуска браузера)

# Значение по умолчанию для User-Agent
DEFAULT_USER_AGENT = USER_AGENT

# Повторные попытки
MAX_RETRY_ATTEMPTS = 3  # Количество повторных попыток при ошибках
RETRY_DELAY = 2         # Задержка между повторными попытками (в секундах)

# Список категорий клиентов для парсинга (должен точно соответствовать тексту на сайте)
CLIENT_CATEGORIES_TO_PARSE = [
    "Individuals",
    "Large companies",
    "NFPOs",
    "Professional firms",
    "Public corporations",
    "Retailers",
    "Self-employed workers",
    "SMEs",
    "Start-ups",
    "Syndicates of co-owners",
]

# --- Настройки Логгирования ---
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# --- Настройки Сохранения ---
EXCEL_FILENAME_TEMPLATE = "cpa_quebec_results_{timestamp}.xlsx"
JSON_FILENAME_TEMPLATE = "cpa_quebec_results_{timestamp}.json"

# Убедимся, что директории существуют при загрузке конфига
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(DEBUG_DIR, exist_ok=True)

# --- Новые таймауты для Selenium (в секундах) ---
PAGE_LOAD_TIMEOUT_SEC = 90  # Таймаут загрузки страницы
IMPLICIT_WAIT_SEC = 10     # Неявное ожидание элементов
# SHORT_TIMEOUT, MEDIUM_TIMEOUT, LONG_TIMEOUT (в мс) используются для явных ожиданий Selenium
# Конвертируем их для удобства или используем напрямую
SHORT_TIMEOUT_SEC = SHORT_TIMEOUT // 1000
MEDIUM_TIMEOUT_SEC = MEDIUM_TIMEOUT // 1000
LONG_TIMEOUT_SEC = LONG_TIMEOUT // 1000

# --- Stagehand URL (Больше не используется с Selenium) ---
# STAGEHAND_URL = os.environ.get("STAGEHAND_URL")
