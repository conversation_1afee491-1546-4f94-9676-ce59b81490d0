2025-04-04 16:34:05,023 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_163405.log
2025-04-04 16:34:05,026 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 16:34:05,026 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:34:05,026 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:34:05,027 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:34:05,027 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:34:05,028 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): CaptchaSolver.__init__() missing 1 required positional argument: 'logger'
2025-04-04 16:34:05,029 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 82, in run_parser
    with CpaQuebecParser(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 71, in __init__
    self.captcha_solver = CaptchaSolver(api_key) if api_key else None
TypeError: CaptchaSolver.__init__() missing 1 required positional argument: 'logger'

2025-04-04 16:34:05,029 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
