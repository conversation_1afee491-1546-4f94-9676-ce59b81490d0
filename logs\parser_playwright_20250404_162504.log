2025-04-04 16:25:04,522 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_162504.log
2025-04-04 16:25:04,525 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 16:25:04,525 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:25:04,526 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:25:04,526 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:25:04,526 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:27:05,519 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 1
