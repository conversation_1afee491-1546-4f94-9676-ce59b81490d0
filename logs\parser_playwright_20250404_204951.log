2025-04-04 20:49:51,277 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_204951.log
2025-04-04 20:49:51,280 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:49:51,280 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 20:49:51,281 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:49:51,281 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True}
2025-04-04 20:49:51,281 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:49:51,281 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:50:07,756 - CpaQuebecParser - DEBUG - Пауза на 1.64 сек. (после driver.get)
2025-04-04 20:50:09,770 - CpaQuebecParser - DEBUG - Пауза на 0.44 сек. (после scroll к кнопке cookie)
2025-04-04 20:50:22,523 - CpaQuebecParser - DEBUG - Пауза на 0.61 сек. (после клика Reset (XPath))
2025-04-04 20:50:27,138 - CpaQuebecParser - DEBUG - Пауза на 1.00 сек. (после очистки формы)
2025-04-04 20:51:13,865 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
