2025-04-04 20:56:15,776 - C<PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_205615.log
2025-04-04 20:56:15,780 - <PERSON><PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:56:15,781 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:56:15,781 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:56:15,781 - C<PERSON>Q<PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 20:56:15,781 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:56:15,781 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 20:56:21,166 - CpaQuebecParser - CRITICAL - Критическая ошибка выполнения (Selenium): CpaQuebecParser.parse() got an unexpected keyword argument 'category_to_parse'
2025-04-04 20:56:21,167 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/main.py", line 114, in run_parser
    results = cpa_parser.parse(
TypeError: CpaQuebecParser.parse() got an unexpected keyword argument 'category_to_parse'

2025-04-04 20:56:21,173 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 4
