#!/usr/bin/env python3
from bs4 import BeautifulSoup

# Загружаем ответ API
with open('debug_response_Individuals.html', 'r', encoding='utf-8') as f:
    html = f.read()

soup = BeautifulSoup(html, 'html.parser')

# Ищем упоминания капчи
captcha = soup.find_all(string=lambda s: s and 'captcha' in s.lower())
print('Найдено упоминаний капчи:', len(captcha))
for c in captcha[:3]:
    print(f'- {c.strip()}')

# Ищем сообщения об ошибках
error_msgs = soup.find_all(class_=lambda c: c and ('error' in str(c).lower() or 'alert' in str(c).lower() or 'warning' in str(c).lower()))
print('Найдено сообщений об ошибках:', len(error_msgs))
for e in error_msgs[:3]:
    print(f'- {e.get_text().strip()}')

# Ищем форму с капчей
captcha_form = soup.find('form', id=lambda i: i and 'captcha' in i.lower())
if captcha_form:
    print('Найдена форма с капчей:', captcha_form.get('id'))
else:
    print('Форма с капчей не найдена')

# Ищем iframe с капчей
captcha_iframe = soup.find('iframe', attrs={'src': lambda s: s and 'recaptcha' in s.lower()})
if captcha_iframe:
    print('Найден iframe с капчей:', captcha_iframe.get('src'))
else:
    print('Iframe с капчей не найден')

# Ищем div с капчей
captcha_div = soup.find('div', class_=lambda c: c and 'g-recaptcha' in str(c))
if captcha_div:
    print('Найден div с капчей:', captcha_div.get('class'))
else:
    print('Div с капчей не найден')
