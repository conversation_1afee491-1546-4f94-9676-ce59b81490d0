import asyncio
import aiohttp
import logging
import time
import re
from urllib.parse import urlparse, parse_qs
from typing import Tu<PERSON>, Optional
from playwright.async_api import async_playwright, Page
from playwright.sync_api import Error as PlaywrightError

# Настройка логирования
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

# Глобальный ключ для Anti-Captcha
ANTICAPTCHA_KEY: Optional[str] = None


def build_cli():
    import argparse
    parser = argparse.ArgumentParser(description="CPA Quebec scraper with CAPTCHA support")
    parser.add_argument(
        "--anticaptcha-key", dest="anticaptcha_key", required=True,
        help="API key for Anti-Captcha service"
    )
    parser.add_argument(
        "--no-captcha", action="store_true",
        help="Completely skip any CAPTCHA handling"
    )
    parser.add_argument(
        "--visible", action="store_true",
        help="Run browser in visible (non-headless) mode"
    )
    return parser


async def extract_recaptcha_sitekey(page: Page) -> Tuple[bool, Optional[str]]:
    """Извлекает sitekey reCAPTCHA со страницы"""
    logger.debug("Extracting reCAPTCHA sitekey…")
    # Ищем iframe стандартной или enterprise reCAPTCHA
    try:
        iframe_locator = page.locator(
            "iframe[src*='api2/anchor'], iframe[src*='recaptcha'], iframe[src*='enterprise']"
        )
        count = await iframe_locator.count()
        for i in range(count):
            src = await iframe_locator.nth(i).get_attribute("src")
            if not src:
                continue
            parsed = urlparse(src)
            qs = parse_qs(parsed.query)
            for key in ("k", "sitekey"):
                site_key = qs.get(key, [None])[0]
                if site_key:
                    logger.info(f"Sitekey extracted: {site_key}")
                    return True, site_key
    except Exception as e:
        logger.debug(f"Error extracting from iframe: {e}")

    # fallback: data-sitekey на div
    try:
        div = page.locator("div.g-recaptcha[data-sitekey], [class*='g-recaptcha'][data-sitekey]").first
        site_key = await div.get_attribute("data-sitekey")
        if site_key:
            logger.info(f"Sitekey from data-sitekey: {site_key}")
            return True, site_key
    except Exception as e:
        logger.debug(f"Error extracting data-sitekey: {e}")

    return False, None


async def anticaptcha_request(
    method: str,
    session: aiohttp.ClientSession,
    **payload
) -> dict:
    """Выполняет HTTP запрос к Anti-Captcha API"""
    url = f"https://api.anti-captcha.com/{method}"
    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY
    logger.debug(f"Anti-Captcha {method} payload: {payload}")
    try:
        async with session.post(url, json=payload, timeout=30) as resp:
            resp.raise_for_status()
            data = await resp.json()
            logger.debug(f"Anti-Captcha {method} response: {data}")
            return data
    except Exception as e:
        logger.error(f"Error during {method}: {e}")
        return {"errorId": 1, "errorDescription": str(e)}


async def create_captcha_task(
    site_key: str,
    page_url: str,
    page: Optional[Page]
) -> Tuple[bool, Optional[int]]:
    """Создает задачу на решение NoCaptchaTaskProxyless"""
    if not ANTICAPTCHA_KEY:
        logger.error("Anti-Captcha key is not set")
        return False, None

    # Получаем User-Agent для enterprise reCAPTCHA
    user_agent = None
    if page and not page.is_closed():
        try:
            user_agent = await page.evaluate("() => navigator.userAgent")
            logger.debug(f"Browser User-Agent: {user_agent}")
        except Exception:
            user_agent = None

    task_payload = {
        "task": {
            "type": "NoCaptchaTaskProxyless",
            "websiteURL": page_url,
            "websiteKey": site_key,
        }
    }
    if user_agent:
        task_payload["task"]["userAgent"] = user_agent

    async with aiohttp.ClientSession() as session:
        result = await anticaptcha_request("createTask", session, **task_payload)

    if result.get("errorId", 1) == 0:
        task_id = result.get("taskId")
        logger.info(f"Anti-Captcha task created, id={task_id}")
        return True, task_id

    logger.error(f"Failed to create CAPTCHA task: {result}")
    return False, None


async def get_captcha_result(
    task_id: int,
    session: aiohttp.ClientSession,
    max_attempts: int = 60,
    initial_wait: int = 5
) -> Tuple[bool, Optional[str]]:
    """Ожидает результат решения капчи с адаптивным ожиданием"""
    await asyncio.sleep(initial_wait)
    for attempt in range(max_attempts):
        result = await anticaptcha_request("getTaskResult", session, taskId=task_id)
        if result.get("errorId", 1) > 0:
            logger.error(f"Error in getTaskResult: {result}")
            return False, None
        if result.get("status") == "ready":
            token = result.get("solution", {}).get("gRecaptchaResponse")
            if token:
                logger.info(f"CAPTCHA solved (token length={len(token)}) in {attempt+1} attempts")
                return True, token
        await asyncio.sleep(min(initial_wait + attempt, 10))

    logger.error("CAPTCHA solving timed out")
    return False, None


async def insert_captcha_token(page: Page, token: str) -> bool:
    """Вставляет g-recaptcha-response в форму и ждет активации кнопки"""
    logger.info("Inserting CAPTCHA token into page…")
    await page.evaluate(
        """
        (token) => {
            const form = document.querySelector(
                'form:has(button[type=submit]:has-text("Search"))'
            ) || document.querySelector('form');
            let field = document.querySelector('#g-recaptcha-response');
            if (!field) {
                field = document.createElement('textarea');
                field.id = 'g-recaptcha-response';
                field.name = 'g-recaptcha-response';
                field.style.display = 'none';
                (form || document.body).appendChild(field);
            }
            field.value = token;
            field.dispatchEvent(new Event('change', { bubbles: true }));
        }
        """,
        token
    )
    # Дожидаемся, что кнопка Search станет активной
    await page.wait_for_function(
        "() => !document.querySelector('button[type=submit]').disabled",
        timeout=10000
    )
    logger.info("CAPTCHA token applied and Search button enabled.")
    return True


async def solve_captcha(page: Page, no_captcha: bool) -> bool:
    """Основная функция решения капчи"""
    if no_captcha:
        logger.warning("Skipping CAPTCHA handling (--no-captcha)")
        return True

    ok, site_key = await extract_recaptcha_sitekey(page)
    if not ok or not site_key:
        logger.error("Could not extract sitekey")
        return False

    async with aiohttp.ClientSession() as session:
        ok, task_id = await create_captcha_task(site_key, page.url, page)
        if not ok:
            return False

        ok, token = await get_captcha_result(task_id, session)
        if not ok or not token:
            return False

    return await insert_captcha_token(page, token)


async def click_search(page: Page):
    """Надежный клик по кнопке Search"""
    button = page.get_by_role("button", name=re.compile(r"Search", re.I))
    await button.click()
    # Ждём завершения AJAX-запроса
    await page.wait_for_response(
        lambda r: "searchDirectory" in r.url and r.status == 200,
        timeout=10000
    )


async def process_category(page: Page, no_captcha: bool):
    """Обрабатывает категорию: решает капчу и парсит результаты"""
    # Решаем капчу и жмем Search
    await solve_captcha(page, no_captcha)
    await click_search(page)

    # Парсим результаты
    items = page.locator("ul#search-results li.searchResult-item")
    count = await items.count()
    logger.info(f"Found {count} result items")
    for i in range(count):
        item = items.nth(i)
        # Пример извлечения данных:
        title = await item.text_content()
        logger.info(f"Result {i+1}: {title.strip()}")


async def main():
    parser = build_cli().parse_args()
    global ANTICAPTCHA_KEY
    ANTICAPTCHA_KEY = parser.anticaptcha_key

    launch_args = {"headless": not parser.visible}
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(**launch_args)
        page = await browser.new_page()
        # Открываем страницу поиска
        await page.goto("https://www.cpaquebec.ca/searchDirectory")
        # Обработка основной категории
        await process_category(page, parser.no_captcha)
        await browser.close()


if __name__ == "__main__":
    asyncio.run(main())

