2025-05-05 17:58:37 [INFO    ] cpa_quebec_parser   : Logging setup complete. Level: INFO. Log file: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/parser_log_20250505_175837.log
2025-05-05 17:58:37 [INFO    ] cpa_quebec_parser   : Debug directory created: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_175837
2025-05-05 17:58:37 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION STARTED =====
2025-05-05 17:58:37 [INFO    ] cpa_quebec_parser   : Arguments: {'by_category': False, 'category': 'Individuals', 'all': False, 'no_captcha': True, 'no_details': False, 'visible': True, 'debug': True, 'log_level': 'INFO', 'max_pages': 500, 'use_system_browser': False}
2025-05-05 17:58:37 [WARNING ] cpa_quebec_parser   : Automatic CAPTCHA solving is DISABLED (--no-captcha).
2025-05-05 17:58:37 [INFO    ] cpa_quebec_parser   : Initializing Playwright...
2025-05-05 17:58:38 [INFO    ] cpa_quebec_parser   : Launching browser (headed)...
2025-05-05 17:58:38 [CRITICAL] cpa_quebec_parser   : A critical error occurred during script execution: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium-1161/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3610, in run
    browser = await playwright.chromium.launch(**launch_opts)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/async_api/_generated.py", line 14450, in launch
    await self._impl_obj.launch(
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_browser_type.py", line 96, in launch
    Browser, from_channel(await self._channel.send("launch", params))
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium-1161/chrome-linux/chrome
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-05-05 17:58:38 [INFO    ] cpa_quebec_parser   : Cleaning up resources...
2025-05-05 17:58:38 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION FINISHED =====
