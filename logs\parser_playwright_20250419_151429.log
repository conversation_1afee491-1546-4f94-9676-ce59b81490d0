2025-04-19 15:14:29,862 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_151429.log
2025-04-19 15:14:29,866 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Parser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 15:14:29,866 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-19 15:14:29,867 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 15:14:29,867 - <PERSON><PERSON><PERSON>ue<PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-19 15:14:29,867 - CpaQuebecParser - INFO - ==================================================
2025-04-19 15:14:29,868 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-19 15:14:45,018 - CpaQuebecParser - DEBUG - Пауза на 1.98 сек. (после driver.get)
2025-04-19 15:14:47,284 - CpaQuebecParser - DEBUG - Пауза на 0.55 сек. (после scroll к кнопке cookie)
2025-04-19 15:15:29,099 - CpaQuebecParser - DEBUG - Пауза на 0.56 сек. (после клика Reset (XPath))
2025-04-19 15:15:33,595 - CpaQuebecParser - DEBUG - Пауза на 0.86 сек. (после очистки формы)
2025-04-19 15:16:46,819 - CpaQuebecParser - DEBUG - Пауза на 3.72 сек. (между категориями, после Individuals)
2025-04-19 15:16:55,164 - CpaQuebecParser - DEBUG - Пауза на 1.81 сек. (после driver.get)
