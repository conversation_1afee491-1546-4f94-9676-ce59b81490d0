
# Документация: Парсер CPA Quebec Directory (Playwright)

## 1. Обзор

Этот Python-скрипт предназначен для автоматического сбора (парсинга) информации о сертифицированных бухгалтерах (CPA) из официального каталога CPA Quebec (<https://cpaquebec.ca/en/find-a-cpa/cpa-directory/>). Скрипт использует библиотеку **Playwright** для управления браузером Chromium, что позволяет ему взаимодействовать с динамическим контентом сайта, заполнять формы, обрабатывать пагинацию и извлекать данные.

Он поддерживает два основных режима работы:

1.  **Поиск по критериям:** Поиск CPA по фамилии, имени, региону или городу.
2.  **Парсинг по категориям:** Последовательный обход предопределенных категорий клиентов ("Clients served") и сбор данных для каждой категории.

Скрипт также включает опциональную интеграцию с сервисом **2Captcha** для автоматического решения reCAPTCHA, если она появляется во время поиска. Результаты сохраняются в форматах Excel (.xlsx) и JSON.

## 2. Возможности

*   **Автоматизация браузера:** Использует Playwright для управления браузером Chromium (в headless или видимом режиме).
*   **Два режима поиска:**
    *   Поиск по фамилии, имени, региону, городу.
    *   Последовательный парсинг по всем категориям "Clients served".
*   **Обработка динамического контента:** Взаимодействует со страницей, выполняя JavaScript и ожидая загрузки элементов.
*   **Заполнение форм:** Автоматически вводит критерии поиска или выбирает чекбоксы категорий.
*   **Обработка капчи:** Интеграция с 2Captcha API для решения reCAPTCHA v2 (опционально, требует API ключа).
*   **Пагинация:** Автоматически переходит по страницам с результатами.
*   **Извлечение данных:** Собирает основную информацию из списка результатов (имя, контакты, URL профиля).
*   **Детализация (опционально):** Может переходить на страницу профиля CPA для сбора более подробной информации (должность, компания, специализация, языки и т.д.).
*   **Дедупликация:** Избегает добавления дубликатов при парсинге по категориям (на основе URL профиля или комбинации полей).
*   **Сохранение результатов:** Сохраняет собранные данные в файлы Excel (.xlsx) и JSON (для отладки).
*   **Логгирование:** Ведет подробный лог работы в файл и выводит информацию в консоль.
*   **Отладка:** Поддерживает режим отладки с сохранением скриншотов и HTML-кода страниц в проблемных ситуациях.
*   **Гибкая настройка:** Параметры (таймауты, пути, User-Agent) вынесены в конфигурационный файл (`config.py`).

## 3. Необходимые условия

*   **Python:** Версия 3.8 или выше.
*   **pip:** Установленный менеджер пакетов Python.
*   **Браузер:** Скрипт установит Chromium через Playwright, но наличие установленного Chrome/Chromium в системе может быть полезно.
*   **Аккаунт 2Captcha (Опционально):** Если вы планируете использовать автоматическое решение капчи, необходим аккаунт на [2captcha.com](https://2captcha.com/) с положительным балансом и API ключ.

## 4. Установка и Настройка

1.  **Клонируйте репозиторий (или скопируйте файлы):**
    ```bash
    git clone <url-вашего-репозитория>
    cd <имя-папки-репозитория>
    ```
    Или просто разместите файлы (`main.py`, папка `cpa_quebec/`) в нужной директории.

2.  **Создайте и активируйте виртуальное окружение (Рекомендуется):**
    ```bash
    python -m venv venv
    # Windows
    .\venv\Scripts\activate
    # MacOS/Linux
    source venv/bin/activate
    ```

3.  **Установите зависимости:**
    ```bash
    pip install -r requirements.txt
    ```
    *(Убедитесь, что в `requirements.txt` есть как минимум `playwright`, `pandas`, `python-dotenv`, `requests`, `fake-useragent`, `openpyxl`)*

4.  **Установите браузеры для Playwright:**
    ```bash
    playwright install chromium
    ```
    *(Эта команда скачает и настроит браузер Chromium, который будет использоваться скриптом. Можно также установить `firefox` или `webkit`, но скрипт по умолчанию использует Chromium)*.

5.  **Настройте API ключ 2Captcha (Опционально):**
    *   Создайте файл с именем `.env` в корневой директории проекта.
    *   Добавьте в него строку с вашим API ключом:
        ```
        CAPTCHA_API_KEY=ВАШ_API_КЛЮЧ_ОТ_2CAPTCHA
        ```
    *   Если этот файл или ключ отсутствует, решение капчи будет пропущено.

6.  **Проверьте конфигурацию (Опционально):**
    *   Откройте файл `cpa_quebec/config.py`.
    *   Вы можете изменить базовый URL (`BASE_URL`), пути сохранения (`OUTPUT_DIR`, `LOGS_DIR`, `DEBUG_DIR`), таймауты, список категорий для парсинга (`CLIENT_CATEGORIES_TO_PARSE`) и другие параметры при необходимости.

## 5. Использование

Скрипт запускается из командной строки с помощью `python cpa_quebec/main.py`. Основные параметры:

*   `--by-category`: Запустить парсинг последовательно по всем категориям из `config.py`. Игнорирует другие критерии поиска (`--last-name`, `--city` и т.д.).
*   `--last-name "Фамилия"`: Указать фамилию для поиска.
*   `--first-name "Имя"`: Указать имя для поиска.
*   `--region "Регион"`: Указать регион для поиска (ищет по частичному совпадению текста в выпадающем списке).
*   `--city "Город"`: Указать город для поиска.
*   `--get-details` / `--no-details`: Включить (по умолчанию) или отключить сбор детальной информации со страниц профилей CPA.
*   `--visible`: Запустить браузер в видимом режиме (по умолчанию запускается в headless-режиме, без графического интерфейса).
*   `--debug`: Включить режим отладки с подробным логированием (уровень DEBUG) и сохранением скриншотов (`debug/screenshots_playwright/`).
*   `--no-captcha`: Принудительно отключить использование 2Captcha, даже если API ключ есть в `.env`.
*   `--output-dir "/путь/к/папке"`: Указать другую директорию для сохранения результатов.
*   `--wait-timeout <секунды>`: Изменить базовый таймаут ожидания Playwright (по умолчанию из `config.py`).
*   `--page-timeout <секунды>`: Изменить таймаут навигации Playwright (по умолчанию из `config.py`).
*   `--help`: Показать справку по всем аргументам.

**Примеры запуска:**

1.  **Парсинг всех CPA по категориям (headless, с деталями, с капчей, если настроено):**
    ```bash
    python cpa_quebec/main.py --by-category
    ```

2.  **Поиск по фамилии "Roy" в видимом режиме, без решения капчи, без деталей:**
    ```bash
    python cpa_quebec/main.py --last-name "Roy" --visible --no-captcha --no-details
    ```

3.  **Поиск по городу "Quebec", с подробным логированием:**
    ```bash
    python cpa_quebec/main.py --city "Quebec" --debug
    ```

4.  **Парсинг по категориям, сохраняя результаты в другую папку:**
    ```bash
    python cpa_quebec/main.py --by-category --output-dir "/data/cpa_results"
    ```

## 6. Структура Проекта

```
.
├── cpa_quebec/               # Основной пакет скрипта
│   ├── __init__.py           # Маркер пакета Python
│   ├── config.py             # Файл конфигурации (URL, пути, таймауты, категории)
│   ├── parser_playwright.py  # Главный класс парсера с использованием Playwright
│   ├── utils.py              # Вспомогательные функции, логгирование, CaptchaSolver
│   └── main.py               # Точка входа, обработка аргументов командной строки
├── logs/                     # Директория для сохранения лог-файлов
├── output/                   # Директория для сохранения результатов (.xlsx, .csv)
├── debug/                    # Директория для отладочных файлов
│   └── screenshots_playwright/ # Скриншоты при включенном --debug
├── venv/                     # Виртуальное окружение (если создано)
├── .env                      # Файл для API ключа 2Captcha (создается вручную)
└── requirements.txt          # Список зависимостей Python
```

*   **`main.py`**: Обрабатывает аргументы командной строки, инициализирует и запускает `PlaywrightParser`.
*   **`parser_playwright.py`**: Содержит класс `PlaywrightParser`, который инкапсулирует всю логику взаимодействия с браузером и сайтом с помощью Playwright.
*   **`utils.py`**: Содержит вспомогательные функции (очистка текста, извлечение email/телефонов, логирование, паузы) и класс `CaptchaSolver`.
*   **`config.py`**: Хранит константы и настройки (URL, пути, таймауты, список категорий).

## 7. Конфигурация

Основные параметры настраиваются в двух местах:

1.  **Файл `.env`:** (Создается вручную в корне проекта)
    *   `CAPTCHA_API_KEY`: Ваш API ключ от сервиса 2Captcha. Если не указан, решение капчи пропускается.

2.  **Файл `cpa_quebec/config.py`:**
    *   `BASE_URL`: Стартовый URL каталога CPA.
    *   `OUTPUT_DIR`, `LOGS_DIR`, `DEBUG_DIR`: Пути к папкам для результатов, логов и отладки.
    *   `DEFAULT_DRIVER_WAIT_TIMEOUT`, `DEFAULT_PAGE_LOAD_TIMEOUT`: Таймауты по умолчанию для Playwright (в секундах, конвертируются в мс в парсере).
    *   `CLIENT_CATEGORIES_TO_PARSE`: Список названий категорий "Clients served", которые будут парситься при запуске с флагом `--by-category`.

## 8. Логика Работы Парсера (`PlaywrightParser`)

1.  **Инициализация (`__init__`, `start_browser`):**
    *   Настраивается логгер.
    *   Загружается API ключ капчи (если есть).
    *   Запускается Playwright и браузер Chromium (headless или видимый).
    *   Создается контекст браузера с заданным User-Agent и размером окна.
    *   Создается новая страница (`self.page`).
    *   Устанавливаются таймауты по умолчанию.

2.  **Основной метод (`parse`):**
    *   Определяет режим работы (по категориям или по критериям).
    *   Вызывает соответствующий метод (`parse_by_categories` или `search_cpa`).
    *   Опционально вызывает `parse_detail_page` для каждой найденной записи, если включено `--get-details`.
    *   Сохраняет итоговые результаты с помощью `save_results`.

3.  **Поиск по критериям (`search_cpa`):**
    *   Переходит на `BASE_URL`.
    *   Закрывает баннер cookie (`close_cookie_banner`).
    *   Находит форму поиска.
    *   Заполняет поля (фамилия, имя, регион, город) с помощью `page.locator(...).fill()` или `select_option()`.
    *   **Не** выбирает чекбоксы "Clients served".
    *   Решает капчу, если она есть (`handle_captcha`).
    *   Находит и нажимает кнопку поиска (или отправляет форму через JS).
    *   Ожидает загрузки результатов.
    *   Снова проверяет и решает капчу (она может появиться *после* отправки).
    *   В цикле обрабатывает страницы результатов:
        *   Извлекает данные со страницы (`extract_cpa_list_from_page`).
        *   Добавляет уникальные результаты в общий список.
        *   Нажимает кнопку "Next" (`_click_next_page`) и ожидает загрузки.
        *   Повторяет до последней страницы или лимита.

4.  **Парсинг по категориям (`parse_by_categories`):**
    *   Итерирует по списку `CLIENT_CATEGORIES_TO_PARSE` из `config.py`.
    *   Для каждой категории:
        *   Переходит на `BASE_URL`.
        *   Закрывает cookie-баннер.
        *   Очищает форму поиска (`clear_search_form`).
        *   Выбирает чекбокс *только для текущей* категории (`select_client_category`).
        *   Решает капчу (`handle_captcha`).
        *   Нажимает кнопку поиска.
        *   Ожидает результаты и снова решает капчу при необходимости.
        *   В цикле обрабатывает страницы результатов (как в `search_cpa`), извлекая данные (`extract_cpa_list_from_page`) и нажимая "Next" (`_click_next_page`).
        *   Добавляет *уникальные* (по `profile_url` или другим полям) результаты в общий список `all_category_results`.

5.  **Извлечение данных со страницы списка (`extract_cpa_list_from_page`):**
    *   Пытается найти контейнер с элементами результатов по разным CSS-селекторам.
    *   Получает все элементы (`Locator.all()`).
    *   Для каждого элемента извлекает:
        *   Полный текст (`item.text_content()`).
        *   Имя (из заголовка/ссылки или первой строки текста).
        *   URL профиля (первая подходящая ссылка).
        *   Контактную информацию (из остального текста).
        *   Телефоны и Email с помощью `extract_phones`/`extract_emails` из `utils.py`.

6.  **Парсинг детальной страницы (`parse_detail_page`):**
    *   Переходит по URL профиля.
    *   Использует `page.locator(...).first.text_content()` и `get_attribute()` для извлечения специфичных полей (имя, должность, компания, адрес, телефон, email, сайт, списки экспертизы/секторов/языков).
    *   Возвращается на предыдущую страницу (`page.go_back()`).

7.  **Вспомогательные методы:**
    *   `handle_captcha`: Ищет капчу, вызывает `CaptchaSolver`, вставляет токен через JS.
    *   `close_cookie_banner`: Ищет и кликает кнопку закрытия баннера (включая поиск в iframe).
    *   `select_client_category`, `check_checkbox_selected`, `clear_search_form`: Используют JavaScript для надежного взаимодействия с чекбоксами и формой.
    *   `_click_next_page`: Находит и кликает кнопку/ссылку пагинации.
    *   `_save_debug_info`, `take_screenshot`: Сохраняют отладочную информацию.

8.  **Завершение (`stop_browser`, `__exit__`):**
    *   Закрывает страницу, контекст и браузер.
    *   Останавливает процесс Playwright.

## 9. Обработка Ошибок и Отладка

*   **Логирование:** Вся основная информация, предупреждения и ошибки записываются в лог-файл в папке `logs/` (имя файла включает дату и время запуска) и выводятся в консоль. Уровень логирования по умолчанию `INFO`.
*   **Режим Отладки (`--debug`):** Включает уровень логирования `DEBUG`, выводящий гораздо больше информации о шагах скрипта. Также активирует сохранение скриншотов каждого шага в папку `debug/screenshots_playwright/`.
*   **Отладочные Файлы:** При возникновении серьезных ошибок (таймауты загрузки, ошибки навигации, ошибки парсинга) скрипт сохраняет текущий HTML-код страницы и скриншот в папку `debug/` с префиксом, указывающим на место ошибки (например, `navigate_timeout_...`, `captcha_solve_failed_...`). Это помогает диагностировать проблемы.
*   **Таймауты:** Скрипт использует таймауты для ожидания загрузки страниц и появления элементов. Если сайт отвечает медленно, может потребоваться увеличить значения `DEFAULT_DRIVER_WAIT_TIMEOUT` и `DEFAULT_PAGE_LOAD_TIMEOUT` в `config.py` или через аргументы `--wait-timeout`/`--page-timeout`.
*   **Ошибки Playwright:** Скрипт обрабатывает специфические ошибки Playwright (`PlaywrightTimeoutError`, `PlaywrightError`), выводя информативные сообщения. В критических случаях (например, при потере соединения с браузером) парсинг может быть прерван.

## 10. Ограничения и Потенциальные Проблемы

*   **Изменения на сайте:** Веб-скрапинг чувствителен к изменениям в структуре HTML, CSS-селекторах и логике работы целевого сайта. Любые обновления на `cpaquebec.ca` могут потребовать адаптации кода парсера.
*   **Блокировки по IP:** При слишком частых или агрессивных запросах сайт может временно или постоянно заблокировать IP-адрес сервера, с которого запускается скрипт. Рекомендуется использовать паузы (`random_sleep`) и рассмотреть использование прокси-серверов для длительных парсингов.
*   **CAPTCHA:** Сайт может чаще показывать CAPTCHA при обнаружении автоматизированной активности. Работа без `--no-captcha` требует активного аккаунта 2Captcha с достаточным балансом. Решение капчи не всегда гарантировано и может занимать время.
*   **Производительность:** Запуск полноценного браузера (даже headless) потребляет больше ресурсов (CPU, RAM), чем простые HTTP-запросы. Для очень масштабного парсинга могут потребоваться более производительные решения (например, Scrapy + Playwright).
*   **JavaScript Ошибки:** Ошибки в JavaScript на самом сайте могут помешать работе парсера.
*   **Этические соображения:** Уважайте условия использования сайта `cpaquebec.ca`. Не создавайте чрезмерную нагрузку на сервер. Используйте собранные данные ответственно.

## 11. Зависимости

Основные зависимости (должны быть в `requirements.txt`):

*   `playwright`: Библиотека для автоматизации браузера.
*   `pandas`: Для работы с данными и сохранения в Excel.
*   `openpyxl`: Движок для записи файлов `.xlsx` (требуется для `pandas.to_excel`).
*   `python-dotenv`: Для загрузки переменных окружения из `.env` файла.
*   `requests`: Для взаимодействия с API 2Captcha.
*   `fake-useragent`: Для генерации случайных User-Agent строк.
*   *(Опционально)* `lxml`, `beautifulsoup4`: Могут быть полезны для альтернативного парсинга HTML, если Playwright-селекторы не справляются.

## 12. Лицензия

*(Укажите здесь лицензию, если она есть, например:)*
Лицензия MIT. См. файл `LICENSE` для дополнительной информации.

---
