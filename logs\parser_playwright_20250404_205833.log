2025-04-04 20:58:33,730 - C<PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_205833.log
2025-04-04 20:58:33,733 - <PERSON><PERSON><PERSON><PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:58:33,733 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 20:58:33,734 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:58:33,734 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 20:58:33,734 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:58:33,734 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 20:58:48,197 - CpaQuebecParser - DEBUG - Пауза на 1.72 сек. (после driver.get)
2025-04-04 20:58:50,058 - CpaQuebecParser - DEBUG - Пауза на 0.60 сек. (после scroll к кнопке cookie)
2025-04-04 20:59:02,803 - CpaQuebecParser - DEBUG - Пауза на 0.53 сек. (после клика Reset (XPath))
2025-04-04 20:59:11,108 - CpaQuebecParser - DEBUG - Пауза на 0.55 сек. (после очистки формы)
2025-04-04 21:00:02,755 - CpaQuebecParser - DEBUG - Пауза на 4.38 сек. (между категориями, после Individuals)
2025-04-04 21:00:13,692 - CpaQuebecParser - DEBUG - Пауза на 1.44 сек. (после driver.get)
2025-04-04 21:01:47,158 - CpaQuebecParser - DEBUG - Пауза на 0.66 сек. (после клика Reset (XPath))
2025-04-04 21:01:53,781 - CpaQuebecParser - DEBUG - Пауза на 0.63 сек. (после очистки формы)
2025-04-04 21:02:45,486 - CpaQuebecParser - DEBUG - Пауза на 3.35 сек. (между категориями, после Large companies)
2025-04-04 21:02:52,227 - CpaQuebecParser - DEBUG - Пауза на 1.59 сек. (после driver.get)
2025-04-04 21:03:06,343 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
