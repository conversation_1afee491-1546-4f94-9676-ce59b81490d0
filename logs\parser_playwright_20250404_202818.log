2025-04-04 20:28:18,480 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_202818.log
2025-04-04 20:28:18,482 - C<PERSON><PERSON><PERSON>becParser - INFO - ==================================================
2025-04-04 20:28:18,482 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:28:18,482 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False}
2025-04-04 20:28:18,482 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:28:18,483 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-04 20:28:23,536 - CpaQuebecParser - CRITICAL - Критическая ошибка выполнения (Selenium): FormHandler.__init__() takes from 3 to 4 positional arguments but 5 were given
2025-04-04 20:28:23,539 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/main.py", line 82, in run_parser
    with CpaQuebecParser(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/selenium_parser/orchestrator.py", line 154, in __enter__
    self._initialize_components() # Инициализируем компоненты
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/selenium_parser/orchestrator.py", line 129, in _initialize_components
    self.form_handler = FormHandler(self.driver, self.logger, self.debug, self.navigator) # Передаем navigator для _save_debug_screenshot
TypeError: FormHandler.__init__() takes from 3 to 4 positional arguments but 5 were given

2025-04-04 20:28:23,540 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 4
