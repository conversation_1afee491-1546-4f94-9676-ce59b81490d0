#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List, Dict, Any, Optional
import requests
import json
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import time
import os
import logging
from urllib.parse import urljoin
from cpa_quebec.config import CLIENT_CATEGORIES_TO_PARSE, OUTPUT_DIR

# Константы
BASE_URL = "https://cpaquebec.ca"
DIRECTORY_URL = f"{BASE_URL}/en/find-a-cpa/cpa-directory/"
API_URL = f"{BASE_URL}/api/sitecore/FindACPA/FindACPABottinFormSubmit?Length=8"

# Категории клиентов и их ID
CATEGORY_IDS = {
    "Individuals": "1878",
    "Large companies": "1877",
    "NFPOs": "1873",
    "Professional firms": "1871",
    "Public corporations": "1879",
    "Retailers": "1876",
    "Self-employed workers": "1880",
    "SMEs": "1874",
    "Start-ups": "1872",
    "Syndicates of co-owners": "1875",
}

class CpaQuebecUpdatedParser:
    """
    Обновленный HTTP-парсер для CPA Quebec Directory.
    Обнаруживает наличие капчи и сообщает о необходимости использовать
    версию с Selenium/Playwright для её обхода.
    """
    def __init__(self, debug: bool = False, output_dir: str = OUTPUT_DIR):
        """
        Инициализирует парсер.
        
        Args:
            debug: Включить ли режим отладки.
            output_dir: Директория для сохранения результатов.
        """
        self.debug = debug
        self.output_dir = output_dir
        self.categories = CLIENT_CATEGORIES_TO_PARSE
        
        # Создаем логгер
        self.logger = logging.getLogger(__name__)
        
        # Создаем директорию для отладочных файлов
        if debug:
            self.debug_dir = os.path.join(output_dir, "debug")
            os.makedirs(self.debug_dir, exist_ok=True)
        
        # Создаем HTTP-сессию с случайным User-Agent
        ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': ua.random,
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'en-US,en;q=0.5',
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': BASE_URL,
            'Referer': DIRECTORY_URL,
            'Connection': 'keep-alive',
        })
        
        # Инициализируем сессию, получая начальную страницу и cookies
        self._initialize_session()
        
    def _initialize_session(self):
        """Инициализирует HTTP-сессию, получая начальную страницу и cookies."""
        self.logger.info(f"Инициализация сессии, получение страницы {DIRECTORY_URL}...")
        try:
            response = self.session.get(DIRECTORY_URL, timeout=30)
            response.raise_for_status()
            self.logger.info(f"Получена страница, статус: {response.status_code}")
            self.logger.debug(f"Cookies: {self.session.cookies.get_dict()}")
            
            # Сохраняем HTML для отладки
            if self.debug:
                debug_path = os.path.join(self.debug_dir, 'initial_page.html')
                with open(debug_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                self.logger.debug(f"Сохранена начальная страница в {debug_path}")
                
            # Проверяем наличие капчи на начальной странице
            if self._has_captcha(response.text):
                self.logger.warning("На начальной странице обнаружена капча!")
                self.logger.warning("Для обхода капчи рекомендуется использовать версию парсера с Selenium/Playwright.")
                
            # Извлекаем скрытые поля формы
            self.form_fields = self._extract_form_fields(response.text)
            self.logger.info(f"Извлечено {len(self.form_fields)} полей формы")
            
        except Exception as e:
            self.logger.error(f"Ошибка при инициализации сессии: {e}")
            raise
    
    def _has_captcha(self, html: str) -> bool:
        """
        Проверяет наличие капчи на странице.
        
        Args:
            html: HTML-код страницы.
            
        Returns:
            True, если на странице есть капча, иначе False.
        """
        soup = BeautifulSoup(html, 'html.parser')
        
        # Ищем различные признаки наличия капчи
        captcha_indicators = [
            soup.find('div', class_='g-recaptcha'),
            soup.find('iframe', attrs={'src': lambda s: s and 'recaptcha' in str(s).lower()}),
            soup.find(string=lambda s: s and 'captcha' in str(s).lower()),
            soup.find('form', id=lambda i: i and 'captcha' in str(i).lower())
        ]
        
        return any(captcha_indicators)
    
    def _extract_form_fields(self, html: str) -> Dict[str, str]:
        """
        Извлекает поля формы из HTML-страницы.
        
        Args:
            html: HTML-код страницы.
            
        Returns:
            Словарь с полями формы.
        """
        soup = BeautifulSoup(html, 'html.parser')
        form = soup.find('form', id='FindACPABottinForm')
        
        if not form:
            self.logger.warning("Форма поиска не найдена на странице!")
            return {}
        
        # Получаем все скрытые поля формы
        fields = {}
        for hidden in form.find_all('input', type='hidden'):
            name = hidden.get('name')
            value = hidden.get('value', '')
            if name:
                fields[name] = value
                
        # Получаем все чекбоксы категорий (они нужны даже если не выбраны)
        for checkbox in form.find_all('input', type='checkbox'):
            name = checkbox.get('name')
            if name and 'Selected' in name:
                # По умолчанию все чекбоксы не выбраны
                fields[name] = 'false'
                
                # Получаем связанные поля Desc и NoRepertoire
                if 'LeftColumn' in name:
                    index = name.split('[')[1].split(']')[0]
                    desc_name = f"ListeClienteleDesserviesLeftColumn[{index}].Desc"
                    no_rep_name = f"ListeClienteleDesserviesLeftColumn[{index}].NoRepertoire"
                    
                    # Ищем соответствующие скрытые поля
                    desc_field = form.find('input', attrs={'name': desc_name})
                    no_rep_field = form.find('input', attrs={'name': no_rep_name})
                    
                    if desc_field:
                        fields[desc_name] = desc_field.get('value', '')
                    if no_rep_field:
                        fields[no_rep_name] = no_rep_field.get('value', '')
                        
                elif 'RightColumn' in name:
                    index = name.split('[')[1].split(']')[0]
                    desc_name = f"ListeClienteleDesserviesRightColumn[{index}].Desc"
                    no_rep_name = f"ListeClienteleDesserviesRightColumn[{index}].NoRepertoire"
                    
                    # Ищем соответствующие скрытые поля
                    desc_field = form.find('input', attrs={'name': desc_name})
                    no_rep_field = form.find('input', attrs={'name': no_rep_name})
                    
                    if desc_field:
                        fields[desc_name] = desc_field.get('value', '')
                    if no_rep_field:
                        fields[no_rep_name] = no_rep_field.get('value', '')
        
        # Добавляем поле для "Accepting new clients"
        fields['ChckAcceptClients'] = 'false'
        
        return fields
    
    def _build_payload(self, category: Optional[str] = None) -> Dict[str, str]:
        """
        Строит данные для POST-запроса.
        
        Args:
            category: Категория клиентов для фильтрации (или None для поиска без фильтра).
            
        Returns:
            Словарь с данными для POST-запроса.
        """
        # Базовые параметры запроса
        payload = {
            'Action': 'Rechercher',
            'ActionParams': '',
            'PageNumber': '0',
            'AfficherResultatMap': 'False',
        }
        
        # Добавляем все поля формы
        payload.update(self.form_fields)
        
        # Если указана категория, устанавливаем соответствующий чекбокс
        if category:
            # Сначала сбрасываем все чекбоксы категорий
            for key in payload:
                if 'Selected' in key:
                    payload[key] = 'false'
            
            # Затем устанавливаем выбранную категорию
            for key in payload:
                if 'Selected' in key and category in payload.get(key.replace('Selected', 'Desc'), ''):
                    payload[key] = 'true'
                    break
        
        # Добавляем оригинальные критерии поиска в JSON
        criteria = {k: v for k, v in payload.items() 
                   if k not in ('Action', 'ActionParams', 'PageNumber', 'AfficherResultatMap')}
        payload['CriteresRechercheOriginal'] = json.dumps(criteria)
        
        return payload
    
    def _extract_profiles(self, html: str) -> List[Dict[str, Any]]:
        """
        Извлекает профили CPA из HTML-страницы результатов.
        
        Args:
            html: HTML-код страницы результатов.
            
        Returns:
            Список словарей с данными профилей.
        """
        profiles = []
        soup = BeautifulSoup(html, 'html.parser')
        
        # Проверяем наличие капчи
        if self._has_captcha(html):
            self.logger.warning("На странице результатов обнаружена капча!")
            self.logger.warning("Для обхода капчи рекомендуется использовать версию парсера с Selenium/Playwright.")
            return profiles
        
        # Ищем контейнер с результатами
        results_container = soup.find(id='SectionResultats')
        
        if not results_container:
            self.logger.warning("Контейнер с результатами не найден на странице!")
            
            # Ищем другие потенциальные контейнеры
            potential_containers = [
                soup.find(class_='results-list'),
                soup.find(class_='results-container'),
                soup.find(class_='search-results')
            ]
            
            for container in potential_containers:
                if container:
                    results_container = container
                    self.logger.info(f"Найден альтернативный контейнер: {container.name}.{container.get('class')}")
                    break
        
        if results_container:
            # Ищем все ссылки на профили CPA
            profile_links = results_container.find_all('a', href=lambda h: h and '/find-a-cpa/' in h and h != '/find-a-cpa/' and h != '/find-a-cpa/cpa-directory/')
            
            for link in profile_links:
                name = link.get_text(strip=True)
                href = link.get('href')
                profile_url = urljoin(BASE_URL, href)
                
                # Ищем дополнительную информацию рядом с ссылкой
                parent = link.parent
                info = {}
                
                # Пытаемся найти контактную информацию
                for _ in range(3):  # Проверяем до 3 уровней вверх
                    if parent:
                        # Ищем телефон
                        phone_elem = parent.find(string=lambda s: s and ('Phone' in s or 'Tel' in s))
                        if phone_elem:
                            phone = phone_elem.find_next(string=True)
                            if phone:
                                info['phone'] = phone.strip()
                        
                        # Ищем email
                        email_elem = parent.find('a', href=lambda h: h and 'mailto:' in h)
                        if email_elem:
                            info['email'] = email_elem.get('href').replace('mailto:', '')
                        
                        parent = parent.parent
                
                # Создаем запись профиля
                profile = {
                    'name': name,
                    'profile_url': profile_url,
                    **info
                }
                
                profiles.append(profile)
        
        # Если контейнер не найден, ищем все ссылки на профили на странице
        if not profiles:
            self.logger.info("Ищем все ссылки на профили на странице...")
            all_links = soup.find_all('a', href=lambda h: h and '/find-a-cpa/' in h and h != '/find-a-cpa/' and h != '/find-a-cpa/cpa-directory/' and 'orders-membership-roll' not in h)
            
            for link in all_links:
                name = link.get_text(strip=True)
                href = link.get('href')
                profile_url = urljoin(BASE_URL, href)
                
                profile = {
                    'name': name,
                    'profile_url': profile_url
                }
                
                profiles.append(profile)
        
        return profiles
    
    def parse_category(self, category: str) -> List[Dict[str, Any]]:
        """
        Парсит профили CPA для указанной категории клиентов.
        
        Args:
            category: Категория клиентов для фильтрации.
            
        Returns:
            Список словарей с данными профилей.
        """
        if category not in self.categories:
            self.logger.error(f"Категория '{category}' не найдена. Доступные категории: {self.categories}")
            return []
        
        self.logger.info(f"Парсинг категории '{category}'...")
        
        # Строим данные для запроса
        payload = self._build_payload(category)
        
        # Отправляем POST-запрос
        try:
            self.logger.info(f"Отправка POST-запроса к {API_URL}...")
            response = self.session.post(API_URL, data=payload, timeout=30)
            response.raise_for_status()
            self.logger.info(f"Получен ответ, статус: {response.status_code}")
            
            # Сохраняем ответ для отладки
            if self.debug:
                debug_path = os.path.join(self.debug_dir, f'response_{category.replace(" ", "_")}.html')
                with open(debug_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                self.logger.debug(f"Сохранен ответ в {debug_path}")
            
            # Проверяем на редирект
            if "window.location=" in response.text:
                import re
                match = re.search(r"window\.location='([^']+)'", response.text)
                if match:
                    redirect_url = match.group(1)
                    full_redirect_url = urljoin(BASE_URL, redirect_url)
                    self.logger.info(f"Обнаружен редирект на: {full_redirect_url}")
                    
                    # Следуем по редиректу
                    self.logger.info("Следуем по редиректу...")
                    redirect_response = self.session.get(full_redirect_url, timeout=30)
                    redirect_response.raise_for_status()
                    self.logger.info(f"Получен ответ редиректа, статус: {redirect_response.status_code}")
                    
                    # Сохраняем ответ редиректа для отладки
                    if self.debug:
                        debug_path = os.path.join(self.debug_dir, f'redirect_{category.replace(" ", "_")}.html')
                        with open(debug_path, 'w', encoding='utf-8') as f:
                            f.write(redirect_response.text)
                        self.logger.debug(f"Сохранен ответ редиректа в {debug_path}")
                    
                    # Извлекаем профили из ответа редиректа
                    profiles = self._extract_profiles(redirect_response.text)
                    self.logger.info(f"Найдено профилей: {len(profiles)}")
                    return profiles
            
            # Если нет редиректа, пробуем извлечь профили из исходного ответа
            profiles = self._extract_profiles(response.text)
            self.logger.info(f"Найдено профилей: {len(profiles)}")
            return profiles
            
        except Exception as e:
            self.logger.error(f"Ошибка при парсинге категории '{category}': {e}")
            return []
    
    def parse_all(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Парсит профили CPA для всех категорий клиентов.
        
        Returns:
            Словарь {категория: список профилей}.
        """
        results = {}
        
        for i, category in enumerate(self.categories):
            self.logger.info(f"Парсинг категории '{category}' ({i+1}/{len(self.categories)})...")
            profiles = self.parse_category(category)
            results[category] = profiles
            
            # Пауза между запросами, чтобы не перегружать сервер
            if category != self.categories[-1]:
                time.sleep(2)
        
        return results
