2025-04-04 21:13:06,646 - C<PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_211306.log
2025-04-04 21:13:06,650 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 21:13:06,651 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 21:13:06,651 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 21:13:06,651 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 21:13:06,651 - CpaQuebecParser - INFO - ==================================================
2025-04-04 21:13:06,651 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 21:13:19,927 - CpaQuebecParser - DEBUG - Пауза на 1.32 сек. (после driver.get)
2025-04-04 21:13:21,356 - CpaQuebecParser - DEBUG - Пауза на 0.46 сек. (после scroll к кнопке cookie)
2025-04-04 21:13:32,838 - CpaQuebecParser - DEBUG - Пауза на 0.59 сек. (после клика Reset (XPath))
2025-04-04 21:13:36,071 - CpaQuebecParser - DEBUG - Пауза на 1.00 сек. (после очистки формы)
2025-04-04 21:13:39,141 - CpaQuebecParser - DEBUG - Пауза на 4.25 сек. (между категориями, после Individuals)
2025-04-04 21:13:41,228 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
