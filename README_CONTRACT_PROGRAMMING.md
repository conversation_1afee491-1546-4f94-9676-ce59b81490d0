# Контрактное программирование в Quebec Parser

## Что такое контрактное программирование

Контрактное программирование - это методология разработки программного обеспечения, основанная на идее "контракта" между компонентами программы. Контракт определяет:

1. **Предусловия (Preconditions)** - условия, которые должны быть выполнены перед вызовом функции
2. **Постусловия (Postconditions)** - условия, которые должны быть выполнены после выполнения функции
3. **Инварианты (Invariants)** - условия, которые должны быть истинны на протяжении всего выполнения функции или метода класса

## Реализация в коде

В проекте Quebec Parser мы реализовали контрактное программирование с помощью декораторов Python:

### Базовые компоненты

1. **Исключения для нарушений контрактов**:
   - `ContractViolation` - базовое исключение
   - `PreconditionViolation` - нарушение предусловия
   - `PostconditionViolation` - нарушение постусловия
   - `InvariantViolation` - нарушение инварианта

2. **Декораторы для проверки контрактов**:
   - `@precondition` - проверяет предусловия перед выполнением функции
   - `@postcondition` - проверяет постусловия после выполнения функции
   - `@invariant` - проверяет инварианты класса

3. **Вспомогательные функции**:
   - `check_type` - проверяет соответствие типа
   - `check_not_none` - проверяет, что значение не None

### Примеры применения

#### Функция `timestamp_str`

```python
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, str) and len(result) == 19,
    "Результат timestamp_str должен быть строкой длиной 19 символов"
)
def timestamp_str() -> str:
    """Возвращает текущую дату и время в формате строки для использования в именах файлов.

    Returns:
        str: Строка в формате 'YYYY-MM-DD_HH-MM-SS'

    Постусловия:
        - Результат должен быть строкой длиной 19 символов
    """
    return datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
```

#### Функция `manage_debug_files`

```python
@precondition(
    lambda file_type: isinstance(file_type, str) and len(file_type) > 0,
    "file_type должен быть непустой строкой"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, str) and len(result) > 0 and Path(result).parent.exists(),
    "Результат должен быть непустой строкой и директория должна существовать"
)
def manage_debug_files(file_type: str) -> str:
    # Реализация функции...
```

#### Функция `close_cookies_banner`

```python
@precondition(
    lambda page: isinstance(page, Page) and not page.is_closed(),
    "page должен быть экземпляром Page и не должен быть закрыт"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, bool),
    "Результат должен быть булевым значением"
)
async def close_cookies_banner(page: Page) -> bool:
    # Реализация функции...
```

### Примеры применения к функциям разгадки капчи

#### Функция `solve_captcha`

```python
@precondition(
    lambda page: isinstance(page, Page) and not page.is_closed(),
    "page должен быть экземпляром Page и не должен быть закрыт"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, bool),
    "Результат должен быть булевым значением"
)
async def solve_captcha(page: Page) -> bool:
    """Решение reCAPTCHA v2 с помощью Anti‑Captcha, вставка токена."""
    # Реализация функции...
```

#### Функция `insert_captcha_token`

```python
@precondition(
    lambda page, token: isinstance(page, Page) and not page.is_closed() and isinstance(token, str) and len(token) > 0,
    "page должен быть экземпляром Page, не должен быть закрыт, token должен быть непустой строкой"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, bool),
    "Результат должен быть булевым значением"
)
async def insert_captcha_token(page: Page, token: str) -> bool:
    """Вставляет токен решения капчи и вызывает callback."""
    # Реализация функции...
```

#### Функция `create_captcha_task`

```python
@precondition(
    lambda site_key, page_url: isinstance(site_key, str) and len(site_key) > 0 and isinstance(page_url, str) and len(page_url) > 0,
    "site_key и page_url должны быть непустыми строками"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple) and len(result) == 2 and isinstance(result[0], bool) and (result[1] is None or isinstance(result[1], int)),
    "Результат должен быть кортежем (bool, Optional[int])"
)
async def create_captcha_task(site_key: str, page_url: str) -> Tuple[bool, Optional[int]]:
    """Создает задачу на решение капчи в Anti-Captcha."""
    # Реализация функции...
```

#### Функция `get_captcha_result`

```python
@precondition(
    lambda task_id, max_attempts=60, max_wait_time=10: isinstance(task_id, int) and task_id > 0 and isinstance(max_attempts, int) and max_attempts > 0 and isinstance(max_wait_time, int) and max_wait_time > 0,
    "task_id, max_attempts и max_wait_time должны быть положительными целыми числами"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple) and len(result) == 2 and isinstance(result[0], bool) and (result[1] is None or isinstance(result[1], str)),
    "Результат должен быть кортежем (bool, Optional[str])"
)
async def get_captcha_result(task_id: int, max_attempts: int = 60, max_wait_time: int = 10) -> Tuple[bool, Optional[str]]:
    """Ожидает и получает результат решения капчи."""
    # Реализация функции...
```

### Примеры применения к функциям обработки результатов парсинга

#### Функция `collect_result_cards`

```python
@precondition(
    lambda page: isinstance(page, Page) and not page.is_closed(),
    "page должен быть экземпляром Page и не должен быть закрыт"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple) and len(result) == 3 and
    isinstance(result[0], bool) and (result[1] is None or isinstance(result[1], Locator)) and
    isinstance(result[2], int) and result[2] >= 0,
    "Результат должен быть кортежем (bool, Optional[Locator], int), где int >= 0"
)
async def collect_result_cards(page: Page) -> Tuple[bool, Optional[Locator], int]:
    """Ищет карточки результатов поиска на странице, используя различные селекторы."""
    # Реализация функции...
```

#### Функция `process_category`

```python
@precondition(
    lambda page, category, get_details: isinstance(page, Page) and not page.is_closed() and
    isinstance(category, str) and len(category) > 0 and isinstance(get_details, bool),
    "page должен быть экземпляром Page и не должен быть закрыт, category должна быть непустой строкой, get_details должен быть булевым значением"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, list) and all(isinstance(item, dict) for item in result),
    "Результат должен быть списком словарей"
)
async def process_category(page: Page, category: str, get_details: bool) -> List[Dict[str, Any]]:
    """Обрабатывает одну категорию: выбирает чекбокс, решает капчу, выполняет поиск и собирает результаты."""
    # Реализация функции...
```

#### Функция `scrape_details`

```python
@precondition(
    lambda page, url: isinstance(page, Page) and not page.is_closed() and
    isinstance(url, str) and len(url) > 0 and (url.startswith("http") or url.startswith("/")),
    "page должен быть экземпляром Page и не должен быть закрыт, url должен быть непустой строкой и начинаться с 'http' или '/'"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, dict) and "email" in result and "phone" in result,
    "Результат должен быть словарем с ключами 'email' и 'phone'"
)
async def scrape_details(page: Page, url: str) -> Dict[str, Any]:
    """Получает детальную информацию о профиле."""
    # Реализация функции...
```

## Преимущества контрактного программирования

1. **Раннее обнаружение ошибок** - нарушения контрактов обнаруживаются сразу при их возникновении
2. **Самодокументирующийся код** - контракты явно описывают ожидания и гарантии функций
3. **Улучшение тестируемости** - контракты могут служить основой для автоматических тестов
4. **Повышение надежности** - контракты помогают обеспечить корректность работы программы
5. **Упрощение отладки** - при нарушении контракта сразу понятно, что пошло не так

## Как использовать

При разработке новых функций или модификации существующих:

1. Определите предусловия - что должно быть верно перед вызовом функции
2. Определите постусловия - что должно быть верно после выполнения функции
3. Для классов определите инварианты - что должно быть верно всегда
4. Используйте соответствующие декораторы для проверки этих условий
5. Обновите документацию функции, чтобы отразить контракты

## Ограничения

1. Проверки контрактов выполняются во время выполнения, что может влиять на производительность
2. В продакшн-окружении может потребоваться отключение проверок для оптимизации
3. Необходимо тщательно продумывать контракты, чтобы они не были слишком строгими или слишком слабыми
