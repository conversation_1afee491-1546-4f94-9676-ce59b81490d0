2025-04-04 19:47:56,744 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_194756.log
2025-04-04 19:47:56,747 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 19:47:56,747 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:47:56,748 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': False, 'accepting_new_clients': False}
2025-04-04 19:47:56,748 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:47:56,748 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-04 19:48:04,170 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 19:48:04,315 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
