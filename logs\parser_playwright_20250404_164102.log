2025-04-04 16:41:02,765 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_164102.log
2025-04-04 16:41:02,772 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 16:41:02,773 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 16:41:02,774 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:41:02,774 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:41:02,774 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:41:02,774 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:42:11,607 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 1
