2025-04-18 13:45:31,354 - C<PERSON>QuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_134531.log
2025-04-18 13:45:31,358 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 13:45:31,358 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-18 13:45:31,359 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 13:45:31,359 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-18 13:45:31,360 - CpaQuebecParser - INFO - ==================================================
2025-04-18 13:45:31,360 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-18 13:45:51,833 - CpaQuebecParser - DEBUG - Пауза на 2.14 сек. (после driver.get)
2025-04-18 13:45:54,107 - CpaQuebecParser - DEBUG - Пауза на 0.53 сек. (после scroll к кнопке cookie)
2025-04-18 13:46:05,777 - CpaQuebecParser - DEBUG - Пауза на 0.53 сек. (после клика Reset (XPath))
2025-04-18 13:46:08,648 - CpaQuebecParser - DEBUG - Пауза на 0.62 сек. (после очистки формы)
2025-04-18 13:46:18,847 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
