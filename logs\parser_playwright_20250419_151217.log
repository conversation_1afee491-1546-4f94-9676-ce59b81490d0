2025-04-19 15:12:17,174 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_151217.log
2025-04-19 15:12:17,177 - C<PERSON>Q<PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 15:12:17,177 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-19 15:12:17,177 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 15:12:17,177 - C<PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-19 15:12:17,177 - CpaQuebecParser - INFO - ==================================================
2025-04-19 15:12:17,178 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-19 15:12:27,612 - CpaQuebecParser - DEBUG - Пауза на 2.13 сек. (после driver.get)
2025-04-19 15:12:30,131 - CpaQuebecParser - DEBUG - Пауза на 0.70 сек. (после scroll к кнопке cookie)
2025-04-19 15:13:12,487 - CpaQuebecParser - DEBUG - Пауза на 0.70 сек. (после клика Reset (XPath))
2025-04-19 15:13:15,515 - CpaQuebecParser - DEBUG - Пауза на 0.69 сек. (после очистки формы)
2025-04-19 15:13:18,966 - CpaQuebecParser - DEBUG - Пауза на 3.90 сек. (между категориями, после Individuals)
2025-04-19 15:13:22,900 - CpaQuebecParser - DEBUG - Пауза на 2.16 сек. (перед retry 2 для категории Large companies)
2025-04-19 15:13:24,673 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
