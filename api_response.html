
<script type="application/javascript" src="https://code.jquery.com/jquery-3.5.0.js"></script>
<script type="application/javascript" src="/scripts/rxp-hpp.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.2/themes/smoothness/jquery-ui.css">

<form Id="FindACPABottinForm" action="/api/sitecore/FindACPA/FindACPABottinFormSubmit?Length=8" data-abide="" data-ajax="true" data-ajax-failure="cpa.forms.reflow();" data-ajax-method="POST" data-ajax-mode="replace" data-ajax-success="cpa.forms.reflow();" data-ajax-update="#AjaxFindACPABottinForm" method="post" role="form"><input data-webform-action="" id="Action" name="Action" type="hidden" value="" /><input data-webform-action-params="" id="ActionParams" name="ActionParams" type="hidden" value="" /><input id="CriteresRechercheOrinal" name="CriteresRechercheOrinal" type="hidden" value="" /><input id="PageNumber" name="PageNumber" type="hidden" value="0" /><input id="AfficherResultatMap" name="AfficherResultatMap" type="hidden" value="False" />    <div class="content-block editable-content">
        <div id="SectionRecherche">
            <div class="row">
                <div class="medium-12 column">
                    <p>
	Whether you need a hand sorting out your personal financial information, managing the challenges of your organization’s growth or rolling out a business strategy, our search engine can find a CPA to help you.
</p>

<p class="small">
	This directory is updated as CPAs who offer services to third parties agree to display their contact information in it. The references listed in the CPA Directory are not recommendations of the Quebec CPA Order. </p>

<p class="small">To check whether someone is authorized to practise the CPA profession, please consult the
	<a href = "/en/find-a-cpa/orders-membership-roll/">Order’s membership roll</a>.
</p>
                </div>
            </div>
            <br />
            <fieldset>
                <legend>Search criteria</legend>
                
                <div class="row">
                    <div class="large-12 column" data-component="form-path" data-priority-index="1">
                        <div class="row">
                            <div class="medium-3 column field">
		                        <label for="Nom">
			                        Last name of CPA
			                        <input id="Nom" name="Nom" type="text" value="" />
		                        </label>
	                        </div>
	                        <div class="medium-3 column field">
		                        <label for="Prenom">
			                        First name of CPA
			                        <input id="Prenom" name="Prenom" type="text" value="" />
		                        </label>
	                        </div>
                            <div class="medium-6 column field">
                                <label for="Ville">
                                    City
                                    <input id="AutoCompletionField" name="Ville" type="text" value="" />
                                </label>
                            </div>
                        </div>
                        <div class="row">
	                        <div class="medium-12 column field">
		                        <strong>Clients served</strong>

	                        </div>
                           
                            <div class="medium-6 column">
                                <ul style="list-style: none;">
                                    <li>
    <input checked="checked" id="ListeClienteleDesserviesLeftColumn_0__Selected" name="ListeClienteleDesserviesLeftColumn[0].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesLeftColumn[0].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesLeftColumn_0__Selected">Individuals</label>
    <input id="ListeClienteleDesserviesLeftColumn_0__Desc" name="ListeClienteleDesserviesLeftColumn[0].Desc" type="hidden" value="Individuals" />
    <input id="ListeClienteleDesserviesLeftColumn_0__NoRepertoire" name="ListeClienteleDesserviesLeftColumn[0].NoRepertoire" type="hidden" value="1878" />
</li><li>
    <input id="ListeClienteleDesserviesLeftColumn_1__Selected" name="ListeClienteleDesserviesLeftColumn[1].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesLeftColumn[1].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesLeftColumn_1__Selected">Large companies</label>
    <input id="ListeClienteleDesserviesLeftColumn_1__Desc" name="ListeClienteleDesserviesLeftColumn[1].Desc" type="hidden" value="Large companies" />
    <input id="ListeClienteleDesserviesLeftColumn_1__NoRepertoire" name="ListeClienteleDesserviesLeftColumn[1].NoRepertoire" type="hidden" value="1877" />
</li><li>
    <input id="ListeClienteleDesserviesLeftColumn_2__Selected" name="ListeClienteleDesserviesLeftColumn[2].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesLeftColumn[2].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesLeftColumn_2__Selected">NFPOs</label>
    <input id="ListeClienteleDesserviesLeftColumn_2__Desc" name="ListeClienteleDesserviesLeftColumn[2].Desc" type="hidden" value="NFPOs" />
    <input id="ListeClienteleDesserviesLeftColumn_2__NoRepertoire" name="ListeClienteleDesserviesLeftColumn[2].NoRepertoire" type="hidden" value="1873" />
</li><li>
    <input id="ListeClienteleDesserviesLeftColumn_3__Selected" name="ListeClienteleDesserviesLeftColumn[3].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesLeftColumn[3].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesLeftColumn_3__Selected">Professional firms</label>
    <input id="ListeClienteleDesserviesLeftColumn_3__Desc" name="ListeClienteleDesserviesLeftColumn[3].Desc" type="hidden" value="Professional firms" />
    <input id="ListeClienteleDesserviesLeftColumn_3__NoRepertoire" name="ListeClienteleDesserviesLeftColumn[3].NoRepertoire" type="hidden" value="1871" />
</li><li>
    <input id="ListeClienteleDesserviesLeftColumn_4__Selected" name="ListeClienteleDesserviesLeftColumn[4].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesLeftColumn[4].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesLeftColumn_4__Selected">Public corporations</label>
    <input id="ListeClienteleDesserviesLeftColumn_4__Desc" name="ListeClienteleDesserviesLeftColumn[4].Desc" type="hidden" value="Public corporations" />
    <input id="ListeClienteleDesserviesLeftColumn_4__NoRepertoire" name="ListeClienteleDesserviesLeftColumn[4].NoRepertoire" type="hidden" value="1879" />
</li>
                                    
	                                    <br />
<input id="acceptClients" name="ChckAcceptClients" type="checkbox" value="true" /><input name="ChckAcceptClients" type="hidden" value="false" />                                        <label for="acceptClients">
                                            Accepting new clients <span data-tooltip aria-haspopup="true" class="has-tip tip-right" title="Limit the search to CPAs who have confirmed that they are able to accept new clients."><i class="fa fa-question-circle"></i></span>
                                        </label>
                                </ul>
                            </div>
                            
                            <div class="medium-6 column">
                                <ul style="list-style: none;">
                                    <li>
    <input id="ListeClienteleDesserviesRightColumn_0__Selected" name="ListeClienteleDesserviesRightColumn[0].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesRightColumn[0].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesRightColumn_0__Selected">Retailers</label>
    <input id="ListeClienteleDesserviesRightColumn_0__Desc" name="ListeClienteleDesserviesRightColumn[0].Desc" type="hidden" value="Retailers" />
    <input id="ListeClienteleDesserviesRightColumn_0__NoRepertoire" name="ListeClienteleDesserviesRightColumn[0].NoRepertoire" type="hidden" value="1876" />
</li><li>
    <input id="ListeClienteleDesserviesRightColumn_1__Selected" name="ListeClienteleDesserviesRightColumn[1].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesRightColumn[1].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesRightColumn_1__Selected">Self-employed workers</label>
    <input id="ListeClienteleDesserviesRightColumn_1__Desc" name="ListeClienteleDesserviesRightColumn[1].Desc" type="hidden" value="Self-employed workers" />
    <input id="ListeClienteleDesserviesRightColumn_1__NoRepertoire" name="ListeClienteleDesserviesRightColumn[1].NoRepertoire" type="hidden" value="1880" />
</li><li>
    <input id="ListeClienteleDesserviesRightColumn_2__Selected" name="ListeClienteleDesserviesRightColumn[2].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesRightColumn[2].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesRightColumn_2__Selected">SMEs</label>
    <input id="ListeClienteleDesserviesRightColumn_2__Desc" name="ListeClienteleDesserviesRightColumn[2].Desc" type="hidden" value="SMEs" />
    <input id="ListeClienteleDesserviesRightColumn_2__NoRepertoire" name="ListeClienteleDesserviesRightColumn[2].NoRepertoire" type="hidden" value="1874" />
</li><li>
    <input id="ListeClienteleDesserviesRightColumn_3__Selected" name="ListeClienteleDesserviesRightColumn[3].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesRightColumn[3].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesRightColumn_3__Selected">Start-ups</label>
    <input id="ListeClienteleDesserviesRightColumn_3__Desc" name="ListeClienteleDesserviesRightColumn[3].Desc" type="hidden" value="Start-ups" />
    <input id="ListeClienteleDesserviesRightColumn_3__NoRepertoire" name="ListeClienteleDesserviesRightColumn[3].NoRepertoire" type="hidden" value="1872" />
</li><li>
    <input id="ListeClienteleDesserviesRightColumn_4__Selected" name="ListeClienteleDesserviesRightColumn[4].Selected" type="checkbox" value="true" /><input name="ListeClienteleDesserviesRightColumn[4].Selected" type="hidden" value="false" /> <label for="ListeClienteleDesserviesRightColumn_4__Selected">Syndicates of co-owners</label>
    <input id="ListeClienteleDesserviesRightColumn_4__Desc" name="ListeClienteleDesserviesRightColumn[4].Desc" type="hidden" value="Syndicates of co-owners" />
    <input id="ListeClienteleDesserviesRightColumn_4__NoRepertoire" name="ListeClienteleDesserviesRightColumn[4].NoRepertoire" type="hidden" value="1875" />
</li>
                                </ul>
                            </div>
                        </div>
                        <div class="row">
                            <div class="medium-12 column field">
                                <div class="g-recaptcha" data-sitekey="6LcDyCMTAAAAAITksa3BMYDdyMWwvK_phKQ15F0a"></div>
                                    <script src="https://www.google.com/recaptcha/api.js?hl=en" async defer></script>
                            </div>
                        </div>

                    </div>
                </div>
                
                <hr />

                <div class="column medium-6 small-text-center medium-text-left">
                    <a href="#" class="button radius secondary" data-webform-action="EffacerCriteresRecherche">Reset search</a>
                </div>
                <div class="column medium-6 small-text-center medium-text-right">
                    <a href="#" class="button radius" data-webform-action="Rechercher">Search</a>
                </div>
            </fieldset>
        </div>
    </div>
    <div class="content-block editable-content" id="SectionResultats">
            <div class="editable-content" data-component="editable-content">
                
            </div>

    </div>
</form>
<script type="text/javascript">

    $(function () {

        var afficherResultatsLoaded = false;
        if (document.getElementById('AfficherResultatsListContainer') !== null && document.getElementById('AfficherResultatsListStaticContainer') !== null
            && document.getElementById('AfficherResultatsMapContainer') !== null && document.getElementById('AfficherResultatsMapStaticContainer') !== null) {
            afficherResultatsLoaded = true;
        }

        if (document.getElementById('map') == null && afficherResultatsLoaded) {
            document.getElementById('AfficherResultatsListContainer').style.display = 'none';
            document.getElementById('AfficherResultatsListStaticContainer').style.display = 'contents';
            document.getElementById('AfficherResultatsMapContainer').style.display = 'contents';
            document.getElementById('AfficherResultatsMapStaticContainer').style.display = 'none';
        }
        else if (afficherResultatsLoaded) {
            document.getElementById('AfficherResultatsListContainer').style.display = 'contents';
            document.getElementById('AfficherResultatsListStaticContainer').style.display = 'none';
            document.getElementById('AfficherResultatsMapContainer').style.display = 'none';
            document.getElementById('AfficherResultatsMapStaticContainer').style.display = 'contents';
        }
        setTimeout(function () {
            if (document.getElementsByClassName('pagination').length > 0) {
                window.scrollTo(0, document.getElementById("SectionResultats").offsetTop);
            }
        }, 0);
    });

    $(function () {
        $("#AutoCompletionField").autocomplete({
            source: function (request, response) {
                $.ajax({
                    url: "/api/sitecore/FindACPA/AutoCompleteVille",
                    type: "POST",
                    dataType: "json",
                    data: {
                        keyword: request.term
                    },

                    success: function (data) {

                        response($.map(data, function (item) {

                            return { label: item.Name, value: item.Name };

                        }));
                    }
                });
            }
        });
    })
</script>
<script>
    jQuery(function ($) {
        // Load API Async
        var script = document.createElement('script');
        script.src = "https://maps.googleapis.com/maps/api/js?key="+ "AIzaSyBLu9PkA_ky9yneupVFsxajU_GLn5Hpmv8" + "&language=" + "en" +"&region=CA&callback=initMap&loading=async";
        document.body.appendChild(script);
    });

    //Geolocation multiple locations
    function initMap() {

        // Declarer la liste des positions
        var locations = [];

        var result = null;
        if (result == null) {
            return;
        }

        var i = 0;
        for (i = 0; i < result.length; i++) {
            var member = result[i];
            var memberInfo = member.PrenomMembre + ' ' + member.NomClient + ", " + member.TitreCompMembre;

            //Ajouter la position a la liste
            locations.push([memberInfo,
                member.DetailPageUrl,
                member.NomEmployeur,
                member.Latitude,
                member.Longitude,
                member.MemberSpecificMessages]);
        }

        if (document.getElementById('map') == null) {
            return;
        }

        var map = new google.maps.Map(document.getElementById('map'),
            {
                zoom: 4,
                center: new google.maps.LatLng(45.5087, -73.583333),
                mapTypeId: google.maps.MapTypeId.ROADMAP,
            });

        var infowindow = new google.maps.InfoWindow({});

        var j;
        var markers = [];
        var clusterer = new markerClusterer.MarkerClusterer({
            map,
            algorithmOptions: {
                maxZoom: 8
            }
        });

        // Creation d'un nouveau marker pour chaque position.
        for (j = 0; j < locations.length; j++) {
            if(locations[j][2] == 0.00 || locations[j][3] == 0.00) continue;

            var marker = new google.maps.Marker({
                position: new google.maps.LatLng(locations[j][3], locations[j][4]),
                map: map,
                title : locations[j][0]
            });

            // Ajouter le marker a la liste
            markers.push(marker);
            clusterer.addMarker(marker);

            // Ajouter l'evenement du click sur chaque position.
            google.maps.event.addListener(
                marker,
                'click',
                (function (marker, j) {
                    return function () {
                        var htmlPopUp = "<div class=\"panel\">";
                        htmlPopUp += "<a href=\"" + locations[j][1] + "\">" + locations[j][0] + "</a>";
                        if (locations[j][2] !== "undefined" && locations[j][2] !== null) {
                            htmlPopUp += "<p>" + locations[j][2] + "</p>";
                        }
                        if (locations[j][5] !== "undefined" && locations[j][5] !== null) {
                            htmlPopUp += "<span class=\"small\">" + locations[j][5] + "</span>";
                        }
                        htmlPopUp += "</div>";
                        infowindow.setContent(htmlPopUp);
                        infowindow.open(map, marker);
                    }
                })(marker, j));
        }

        map.fitBounds(bounds);
        var listener = google.maps.event.addListener(map, "bounds_changed", function() {
            if (map.getZoom() > 16) map.setZoom(16);
            google.maps.event.removeListener(listener);
        });
    }

</script>
<script src="https://unpkg.com/@googlemaps/markerclusterer/dist/index.min.js"></script>
<script>
    document.addEventListener("DOMContentLoaded", function (event) {
        if (kafe.dependencies.jQuery('#search-results').offset() !== undefined) {
            kafe.dependencies.jQuery(window).scrollTop(kafe.dependencies.jQuery('#search-results').offset().top);
        }

        kafe.dependencies.jQuery('a#backSearch').click(function () {
            kafe.dependencies.jQuery(window).scrollTop(0); return false;
        });
    });
</script>

