2025-04-19 14:58:07,587 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_145807.log
2025-04-19 14:58:07,590 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 14:58:07,590 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-19 14:58:07,590 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 14:58:07,590 - <PERSON><PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-19 14:58:07,590 - CpaQuebecParser - INFO - ==================================================
2025-04-19 14:58:07,590 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-19 14:58:19,230 - CpaQuebecParser - DEBUG - Пауза на 2.14 сек. (после driver.get)
2025-04-19 14:58:21,466 - CpaQuebecParser - DEBUG - Пауза на 0.49 сек. (после scroll к кнопке cookie)
2025-04-19 14:59:03,552 - CpaQuebecParser - DEBUG - Пауза на 0.97 сек. (после клика Reset (XPath))
2025-04-19 14:59:06,181 - CpaQuebecParser - DEBUG - Пауза на 0.79 сек. (после очистки формы)
2025-04-19 14:59:15,886 - CpaQuebecParser - DEBUG - Пауза на 4.26 сек. (между категориями, после Individuals)
2025-04-19 14:59:20,026 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
