# План миграции парсера CPA Quebec на Rust с Whitehole

## 1. Структура проекта
```
cpa_quebec_parser/
├── src/
│   ├── main.rs          # Точка входа
│   ├── browser/         # Работа с браузером
│   │   ├── mod.rs
│   │   ├── playwright.rs
│   │   └── captcha.rs
│   ├── parser/          # Парсинг HTML
│   │   ├── mod.rs
│   │   ├── cards.rs     # Правила для карточек
│   │   └── forms.rs     # Правила для форм
│   ├── models/          # Модели данных
│   └── config.rs        # Конфигурация
├── Cargo.toml
└── README.md
```

## 2. Основные этапы

### 2.1. Настройка окружения
- [ ] Инициализировать Rust проект
- [ ] Добавить зависимости:
  - `whitehole` для парсинга
  - `thirtyfour` или `playwright` для браузера
  - `serde` для работы с JSON
  - `tokio` для асинхронности

### 2.2. Реализация парсера
```rust
// Пример правила для карточки CPA
rule! {
    card(r: &str) -> Card {
        ws!()?
        >> div(class="card") 
        >> h3() >> text().collect()    // Название
        >> ws!()?
        >> a(href=link) >> text().collect()  // Ссылка
        => |(name, link)| Card { name, link }
    }
}
```

### 2.3. Интеграция с браузером
- Получение HTML через Playwright/Thirtyfour
- Обработка CAPTCHA (Anti-Captcha API)
- Управление сессией и куками

### 2.4. Тестирование
- Модульные тесты для правил парсинга
- Интеграционные тесты с реальными страницами
- Проверка обработки ошибок

## 3. График работ
1. Неделя 1: Настройка проекта и базовой инфраструктуры
2. Неделя 2: Реализация основных правил парсинга
3. Неделя 3: Интеграция с браузерным слоем
4. Неделя 4: Тестирование и отладка

## 4. Риски и решения
| Риск | Решение |
|------|---------|
| Сложность миграции CAPTCHA | Использовать готовые Rust библиотеки |
| Изменение структуры HTML | Гибкие правила Whitehole + мониторинг |
| Производительность | Оптимизация PEG правил |