2025-04-19 14:00:57,362 - C<PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_140057.log
2025-04-19 14:00:57,365 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 14:00:57,365 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-19 14:00:57,366 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 14:00:57,366 - <PERSON><PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-19 14:00:57,366 - CpaQuebecParser - INFO - ==================================================
2025-04-19 14:00:57,366 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-19 14:01:08,109 - CpaQuebecParser - DEBUG - Пауза на 1.97 сек. (после driver.get)
2025-04-19 14:01:10,177 - CpaQuebecParser - DEBUG - Пауза на 0.40 сек. (после scroll к кнопке cookie)
