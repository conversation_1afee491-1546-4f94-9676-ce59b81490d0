2025-04-04 20:32:43,713 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_203243.log
2025-04-04 20:32:43,716 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 20:32:43,717 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:32:43,717 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False}
2025-04-04 20:32:43,718 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:32:43,718 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-04 20:33:04,408 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
