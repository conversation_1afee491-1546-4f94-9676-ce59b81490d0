import logging
import re
import time
from typing import List, Dict, Optional
import os

# --- Selenium Imports ---
from selenium.common.exceptions import (
    NoSuchElementException, TimeoutException, ElementNotInteractableException,
    StaleElementReferenceException
)
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
# ------------------------

# Импортируем таймауты и функцию случайной паузы
from ..config import SHORT_TIMEOUT_SEC, MEDIUM_TIMEOUT_SEC, LONG_TIMEOUT_SEC # Используем секунды
from ..utils import random_sleep

# Утилита для выбора чекбоксов
from .checkbox_helper import CheckboxHelper

class FormHandler:
    """Отвечает за взаимодействие с формами поиска на сайте CPA Quebec (Selenium)."""

    def __init__(self, driver: WebDriver, logger: logging.Logger, debug: bool = False):
        """
        Инициализирует обработчик форм (Selenium).

        Args:
            driver: Экземпляр Selenium WebDriver.
            logger: Логгер для записи сообщений.
            debug: Флаг режима отладки.
        """
        self.driver = driver
        self.logger = logger
        self.debug = debug
        self.wait_short = WebDriverWait(self.driver, SHORT_TIMEOUT_SEC)
        self.wait_medium = WebDriverWait(self.driver, MEDIUM_TIMEOUT_SEC)
        self.wait_long = WebDriverWait(self.driver, LONG_TIMEOUT_SEC)

        # --- Селекторы Selenium (используем CSS по возможности) ---
        # Расширенные селекторы для полей формы
        self.LAST_NAME_SELECTOR = "input[placeholder*='Last name'], input[placeholder*='Nom'], input[id*='LastName'], input[name*='LastName'], input[id*='Nom'], input[name*='Nom']"
        self.FIRST_NAME_SELECTOR = "input[placeholder*='First name'], input[placeholder*='Prénom'], input[id*='FirstName'], input[name*='FirstName'], input[id*='Prenom'], input[name*='Prenom']"
        self.CITY_SELECTOR = "input[placeholder*='City'], input[placeholder*='Ville'], input[id*='City'], input[name*='City'], input[id*='Ville'], input[name*='Ville'], input#AutoCompletionField"

        # Расширенные селекторы для кнопки поиска
        self.SEARCH_BUTTON_SELECTOR_CSS = "a.button.radius[data-webform-action='Rechercher'], button[type='submit'], input[type='submit'], a.button:not(.secondary), button.search, input.search, a.button.primary, a.button.radius, button.primary, input.button.primary, input.button.radius, button.button.primary, button.button.radius"

        # Расширенный XPath для кнопки поиска
        self.SEARCH_BUTTON_SELECTOR_XPATH = (
            "//a[@data-webform-action='Rechercher'] | "
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'search')] | "
            "//input[@type='submit' and (contains(translate(@value, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'search') or contains(translate(@value, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'rechercher'))] | "
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'search') or contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'rechercher')] | "
            "//button[contains(@class, 'primary') or contains(@class, 'radius')] | "
            "//a[contains(@class, 'primary') or contains(@class, 'radius')] | "
            "//input[contains(@class, 'primary') or contains(@class, 'radius')] | "
            "//button[@type='submit'] | "
            "//input[@type='submit']"
        )

        # Расширенные селекторы для кнопки сброса
        self.RESET_BUTTON_SELECTOR_CSS = "a.button.radius.secondary[data-webform-action='EffacerCriteresRecherche'], button[type='reset'], input[type='reset'], a.button.secondary, button.reset, input.reset"

        # Расширенный XPath для кнопки сброса
        self.RESET_BUTTON_SELECTOR_XPATH = (
            "//a[@data-webform-action='EffacerCriteresRecherche'] | "
            "//button[@type='reset'] | "
            "//input[@type='reset'] | "
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'reset') or contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'clear')] | "
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'reset') or contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'clear')]"
        )

        # Категории (оставим пока пустым, будем искать по тексту)
        self.CLIENT_CATEGORIES = {}

        # Расширенные селекторы для чекбокса "Accepting new clients"
        self.ACCEPTING_CLIENTS_SELECTOR_CSS = "input[type='checkbox'][id*='Accepting'], input[type='checkbox'][id*='NewClients'], input[type='checkbox'][name*='Accepting'], input[type='checkbox'][name*='NewClients']"

        # Расширенный XPath для чекбокса "Accepting new clients"
        self.ACCEPTING_CLIENTS_SELECTOR_XPATH = (
            "//label[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accepting new clients') or "
            "contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'nouveaux clients')]//input[@type='checkbox'] | "
            "//input[@type='checkbox']/following-sibling::label[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accepting new clients') or "
            "contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'nouveaux clients')]/preceding-sibling::input[@type='checkbox']"
        )

        # Расширенные селекторы для reCAPTCHA
        self.RECAPTCHA_IFRAME_SELECTOR = (By.CSS_SELECTOR, "iframe[title*='reCAPTCHA'], iframe[src*='recaptcha'], iframe[src*='google.com/recaptcha']")
        self.RECAPTCHA_CHECKBOX_SELECTOR = (By.CSS_SELECTOR, "div.recaptcha-checkbox-border, span.recaptcha-checkbox-checkmark, #recaptcha-anchor")
        # ---------------------------------------------------------

        # Утилита для выбора чекбоксов
        self.checkbox_helper = CheckboxHelper(self.driver, self.wait_medium, self.logger, self.debug)

    def fill_search_criteria(self, last_name: str = "", first_name: str = "", city: str = "") -> None:
        """
        Заполняет критерии поиска в форме (Selenium).

        Args:
            last_name: Фамилия для поиска (по умолчанию пустая строка)
            first_name: Имя для поиска (по умолчанию пустая строка)
            city: Город для поиска (по умолчанию пустая строка)
        """
        self.logger.info(f"Заполнение критериев поиска (Selenium): last_name='{last_name}', first_name='{first_name}', city='{city}'")

        # Закрываем баннер с куки, если он есть
        self.close_cookie_banner()

        # Очищаем форму перед заполнением
        self.clear()

        # Заполняем поля, если они указаны
        if last_name:
            self._fill_field(By.CSS_SELECTOR, self.LAST_NAME_SELECTOR, last_name, "фамилия")

        if first_name:
            self._fill_field(By.CSS_SELECTOR, self.FIRST_NAME_SELECTOR, first_name, "имя")

        if city:
            self._fill_field(By.CSS_SELECTOR, self.CITY_SELECTOR, city, "город")

    def _fill_field(self, by: By, selector: str, value: str, field_name: str) -> None:
        """
        Вспомогательный метод для заполнения поля ввода (Selenium).

        Args:
            by: Стратегия поиска Selenium (By.ID, By.CSS_SELECTOR, etc.).
            selector: Строка селектора.
            value: Значение для ввода.
            field_name: Название поля для логирования.
        """
        try:
            # Ожидаем видимости поля
            field = self.wait_medium.until(EC.visibility_of_element_located((by, selector)))
            field.clear() # Сначала очищаем поле
            field.send_keys(value)
            self.logger.info(f"Поле '{field_name}' заполнено значением '{value}'")
        except TimeoutException:
            self.logger.error(f"Поле '{field_name}' не найдено или не стало видимым по селектору '{selector}' за {MEDIUM_TIMEOUT_SEC} сек.")
            # Попробуем сохранить скриншот
            self._save_debug_screenshot(f"field_not_found_{field_name}")
        except ElementNotInteractableException:
             self.logger.error(f"Поле '{field_name}' найдено, но не доступно для взаимодействия (селектор '{selector}').")
             self._save_debug_screenshot(f"field_not_interactable_{field_name}")
        except Exception as e:
            self.logger.error(f"Ошибка при заполнении поля '{field_name}' (селектор '{selector}'): {e}", exc_info=self.debug)
            self._save_debug_screenshot(f"field_fill_error_{field_name}")

    def _clear_field(self, by: By, selector: str, field_name: str) -> None:
        """
        Вспомогательный метод для очистки поля ввода (Selenium).
        Args:
            by: Стратегия поиска.
            selector: Селектор поля.
            field_name: Название поля для логирования.
        """
        try:
            # Не используем долгое ожидание, т.к. поле может быть уже очищено или не существует
            elements = self.driver.find_elements(by, selector)
            if elements:
                field = elements[0]
                if field.is_displayed(): # Проверяем видимость перед очисткой
                    field.clear()
                    self.logger.debug(f"Поле '{field_name}' очищено.")
        except Exception as e:
            # Игнорируем ошибки при очистке, но логируем на уровне debug
            self.logger.debug(f"Ошибка при попытке очистить поле '{field_name}' (селектор '{selector}'): {e}")

    # --- Вспомогательный метод для сохранения скриншотов ---
    def _save_debug_screenshot(self, name: str):
        if not self.debug: return
        try:
            # Убедимся, что директория debug существует
            debug_dir = "debug" # Можно вынести в config
            path = os.path.join(debug_dir, f"{name}.png")
            self.driver.save_screenshot(path)
            self.logger.debug(f"Скриншот сохранен: {path}")
        except Exception as e:
             self.logger.error(f"Не удалось сохранить debug скриншот '{name}': {e}")

    # --- Реализация поиска и клика по чекбоксу по тексту ---
    def _find_and_check_checkbox_by_text(self, text: str) -> bool:
        """
        Находит и кликает чекбокс, связанный с текстом (обычно в <label>).
        Приоритет отдается клику по самой метке <label>.
        Возвращает True при успехе, False при неудаче.
        """
        self.logger.debug(f"Поиск чекбокса/label для текста: '{text}'")
        # Логируем все доступные метки на странице для отладки
        try:
            labels = self.driver.execute_script(
                "return Array.from(document.querySelectorAll('label')).map(l => l.innerText.trim());"
            )
            self.logger.debug(f"Найденные метки на странице: {labels}")
        except Exception as log_e:
            self.logger.debug(f"Не удалось получить список меток для отладки: {log_e}")
        search_text_lower = text.lower()
        clickable_element = None
        found_by = ""

        # --- Основная стратегия: Клик по label, содержащему текст ---
        xpath_label_contains = f"//label[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{search_text_lower}')]"
        try:
            self.logger.debug(f"Пробуем основную стратегию XPath (клик по label): {xpath_label_contains}")
            # Используем средний таймаут для поиска основного элемента
            wait = WebDriverWait(self.driver, MEDIUM_TIMEOUT_SEC)
            potential_element = wait.until(EC.element_to_be_clickable((By.XPATH, xpath_label_contains)))
            if potential_element.is_displayed():
                clickable_element = potential_element
                found_by = "label_contains_click"
                self.logger.info(f"Найден кликабельный и видимый label для '{text}' по XPath.")
            else:
                 self.logger.debug(f"Label найден по XPath, но невидим.")
        except TimeoutException:
            self.logger.debug(f"Label для '{text}' не найден/не кликабелен по основному XPath.")
        except Exception as e:
             self.logger.warning(f"Неожиданная ошибка при поиске по основному XPath ({xpath_label_contains}): {e}")

        # --- Резервная стратегия: Selenium поиск через label-for-input ---
        if not clickable_element:
            self.logger.debug(f"Пробуем Selenium-стратегию для '{text}' через label@for")
            try:
                checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
                for cb in checkboxes:
                    cb_id = cb.get_attribute('id')
                    if not cb_id:
                        continue
                    try:
                        lbl = self.driver.find_element(By.XPATH, f"//label[@for='{cb_id}']")
                    except NoSuchElementException:
                        continue
                    if text.lower() in lbl.text.lower():
                        self.logger.info(f"Найден чекбокс для '{text}' через Selenium-стратегию. Кликаем...")
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", cb)
                        time.sleep(0.3)
                        cb.click()
                        return True
                self.logger.debug(f"Selenium-стратегия не нашла чекбокс для '{text}'")
            except Exception as e:
                self.logger.warning(f"Ошибка в Selenium-стратегии для '{text}': {e}")

        # --- Третья стратегия: JavaScript (если XPath и Selenium не сработали) ---
        if not clickable_element:
            self.logger.info(f"XPath и Selenium-стратегии не сработали для '{text}', пробуем JavaScript...")
            # Вызываем JS метод, который теперь является запасным
            if self._select_checkbox_with_js(text):
                self.logger.info(f"Чекбокс для '{text}' успешно выбран с помощью JavaScript.")
                return True
            else:
                self.logger.error(f"Ни XPath, ни JavaScript не смогли найти/выбрать чекбокс для '{text}'.")
                self._save_debug_screenshot(f"checkbox_not_found_fallback_{text.replace(' ','_')}")
                return False # Все стратегии провалились

        # --- Выполнение клика, если элемент найден по XPath ---
        if clickable_element:
            try:
                self.logger.info(f"Кликаем по элементу для '{text}' (найден по '{found_by}')...")
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", clickable_element)
                random_sleep(0.4, 0.8, f"после scroll к элементу '{text}'")
                clickable_element.click()
                self.logger.info(f"Клик по элементу для '{text}' выполнен.")
                return True
            except StaleElementReferenceException:
                self.logger.warning(f"Элемент для '{text}' ('{found_by}') устарел после нахождения/скролла.")
                self._save_debug_screenshot(f"checkbox_stale_{text.replace(' ','_')}")
                return False
            except ElementNotInteractableException:
                 self.logger.error(f"Элемент для '{text}' ('{found_by}') найден, но не доступен для клика.")
                 self._save_debug_screenshot(f"checkbox_not_interactable_{text.replace(' ','_')}")
                 return False
            except Exception as e:
                self.logger.error(f"Непредвиденная ошибка при клике на элемент для '{text}' ('{found_by}'): {e}", exc_info=self.debug)
                self._save_debug_screenshot(f"checkbox_click_error_{text.replace(' ','_')}")
                return False
        else:
            # Сюда мы не должны попасть, так как JS был последней стратегией
            self.logger.error(f"Непредвиденная ситуация: элемент для '{text}' не найден после всех проверок.")
            return False

    def _select_checkbox_with_js(self, text: str) -> bool:
        """
        JavaScript-стратегия: ищем <label> по вхождению текста и кликаем связанный checkbox.
        """
        self.logger.debug(f"JS-стратегия: выбор чекбокса '{text}' через label.htmlFor")
        script = '''
        (function(txt) {
            txt = txt.toLowerCase();
            var labels = document.querySelectorAll('label');
            for (var i = 0; i < labels.length; i++) {
                var lbl = labels[i];
                var lblTxt = lbl.innerText.toLowerCase();
                if (lblTxt.indexOf(txt) !== -1) {
                    var fid = lbl.htmlFor;
                    var cb = fid ? document.getElementById(fid) : lbl.querySelector('input[type="checkbox"]');
                    if (cb) { cb.scrollIntoView(); cb.click(); return true; }
                    lbl.scrollIntoView(); lbl.click();
                    return true;
                }
            }
            return false;
        })(arguments[0]);
        '''
        try:
            result = self.driver.execute_script(script, text)
            if result:
                self.logger.info(f"Чекбокс '{text}' успешно отмечен через JS")
                return True
            self.logger.warning(f"JS-стратегия не нашла чекбокс '{text}'")
            return False
        except Exception as e:
            self.logger.error(f"Ошибка JS-стратегии для '{text}': {e}", exc_info=self.debug)
            return False

    # TODO: Адаптировать select_accepting_new_clients, clear,
    #       click_search_button, click_captcha, _check_checkbox, _uncheck_checkbox,
    #       _attempt_check

    # Обновленный метод select_category с улучшенным поиском чекбоксов категорий
    def select_category(self, category_name: str) -> bool:
        """
        Выбирает чекбокс категории клиентов через CheckboxHelper: сначала по ID, затем по тексту.
        """
        self.logger.info(f"Выбор категории клиентов (Selenium): '{category_name}'")

        # Закрываем баннер с куки, если он есть
        self.close_cookie_banner()

        # Логируем все доступные метки на странице для отладки
        try:
            labels = self.driver.execute_script(
                "return Array.from(document.querySelectorAll('label')).map(l => l.innerText.trim());"
            )
            self.logger.debug(f"Доступные метки на странице: {labels}")

            # Логируем все чекбоксы на странице
            checkboxes = self.driver.execute_script(
                "return Array.from(document.querySelectorAll('input[type=\"checkbox\"]')).map(cb => ({id: cb.id, name: cb.name, checked: cb.checked}));"
            )
            self.logger.debug(f"Доступные чекбоксы на странице: {checkboxes}")
        except Exception as e:
            self.logger.debug(f"Не удалось получить список меток/чекбоксов для отладки: {e}")

        # Расширенная карта ID для категорий (включая вариации написания)
        id_map = {
            # Английские названия
            "individuals": "ListeClienteleDesserviesLeftColumn_0__Selected",
            "large companies": "ListeClienteleDesserviesLeftColumn_1__Selected",
            "nfpos": "ListeClienteleDesserviesLeftColumn_2__Selected",
            "professional firms": "ListeClienteleDesserviesLeftColumn_3__Selected",
            "public corporations": "ListeClienteleDesserviesLeftColumn_4__Selected",
            "retailers": "ListeClienteleDesserviesRightColumn_0__Selected",
            "self-employed workers": "ListeClienteleDesserviesRightColumn_1__Selected",
            "smes": "ListeClienteleDesserviesRightColumn_2__Selected",
            "start-ups": "ListeClienteleDesserviesRightColumn_3__Selected",
            "syndicates of co-owners": "ListeClienteleDesserviesRightColumn_4__Selected",
            # Сокращения и альтернативные написания
            "individual": "ListeClienteleDesserviesLeftColumn_0__Selected",
            "large company": "ListeClienteleDesserviesLeftColumn_1__Selected",
            "nfpo": "ListeClienteleDesserviesLeftColumn_2__Selected",
            "professional firm": "ListeClienteleDesserviesLeftColumn_3__Selected",
            "public corporation": "ListeClienteleDesserviesLeftColumn_4__Selected",
            "retailer": "ListeClienteleDesserviesRightColumn_0__Selected",
            "self-employed worker": "ListeClienteleDesserviesRightColumn_1__Selected",
            "sme": "ListeClienteleDesserviesRightColumn_2__Selected",
            "start-up": "ListeClienteleDesserviesRightColumn_3__Selected",
            "syndicate of co-owners": "ListeClienteleDesserviesRightColumn_4__Selected",
            # Французские названия
            "particuliers": "ListeClienteleDesserviesLeftColumn_0__Selected",
            "grandes entreprises": "ListeClienteleDesserviesLeftColumn_1__Selected",
            "osbl": "ListeClienteleDesserviesLeftColumn_2__Selected",
            "cabinets professionnels": "ListeClienteleDesserviesLeftColumn_3__Selected",
            "sociétés publiques": "ListeClienteleDesserviesLeftColumn_4__Selected",
            "détaillants": "ListeClienteleDesserviesRightColumn_0__Selected",
            "travailleurs autonomes": "ListeClienteleDesserviesRightColumn_1__Selected",
            "pme": "ListeClienteleDesserviesRightColumn_2__Selected",
            "entreprises en démarrage": "ListeClienteleDesserviesRightColumn_3__Selected",
            "syndicats de copropriété": "ListeClienteleDesserviesRightColumn_4__Selected"
        }

        # Стратегия 1: Быстрый выбор по ID
        key = category_name.lower()
        if key in id_map:
            checkbox_id = id_map[key]
            self.logger.info(f"Попытка выбора чекбокса по ID: {checkbox_id}")
            if self.checkbox_helper.select_checkbox_by_id(checkbox_id):
                self.logger.info(f"Стратегия ID для '{category_name}' сработала.")
                return True
            else:
                self.logger.warning(f"Стратегия ID для '{category_name}' не сработала.")

        # Стратегия 2: Прямой поиск всех чекбоксов с ID, содержащим 'Selected'
        try:
            self.logger.info("Попытка найти все чекбоксы с ID, содержащим 'Selected'")
            checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox'][id*='Selected']")
            self.logger.debug(f"Найдено чекбоксов с ID *Selected*: {len(checkboxes)}")

            for checkbox in checkboxes:
                checkbox_id = checkbox.get_attribute('id')
                self.logger.debug(f"Проверка чекбокса с ID: {checkbox_id}")

                # Пытаемся найти связанную метку
                try:
                    label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{checkbox_id}']")
                    label_text = label.text.strip().lower()
                    self.logger.debug(f"Найдена метка для чекбокса {checkbox_id}: '{label_text}'")

                    # Проверяем, содержит ли метка текст категории
                    if category_name.lower() in label_text:
                        self.logger.info(f"Найден подходящий чекбокс с ID {checkbox_id} и меткой '{label_text}'")
                        if not checkbox.is_selected():
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                            checkbox.click()
                            self.logger.info(f"Чекбокс с ID {checkbox_id} успешно выбран")
                        return True
                except Exception as e:
                    self.logger.debug(f"Не удалось найти метку для чекбокса {checkbox_id}: {e}")
        except Exception as e:
            self.logger.warning(f"Ошибка при поиске чекбоксов с ID *Selected*: {e}")

        # Стратегия 3: Выбор по тексту метки или через JS
        self.logger.info(f"Попытка выбора чекбокса по тексту метки: '{category_name}'")
        if self.checkbox_helper.select_checkbox_by_text(category_name):
            self.logger.info(f"Стратегия Text/JS для '{category_name}' сработала.")
            return True
        else:
            self.logger.warning(f"Стратегия Text/JS для '{category_name}' не сработала.")

        # Стратегия 4: Фоллбек - поиск и клик по любому совпадающему тексту
        self.logger.info(f"Попытка выбора чекбокса через fallback-стратегию: '{category_name}'")
        if self._find_and_check_checkbox_by_text(category_name):
             self.logger.info(f"Стратегия Fallback (_find_and_check...) для '{category_name}' сработала.")
             return True

        # Стратегия 5: Последняя попытка - прямой JavaScript для поиска и клика
        try:
            self.logger.info("Последняя попытка - прямой JavaScript для поиска и клика по чекбоксу")
            script = f"""
            (function() {{
                // Ищем все чекбоксы
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                console.log('Найдено чекбоксов:', checkboxes.length);

                // Проверяем каждый чекбокс
                for (var i = 0; i < checkboxes.length; i++) {{
                    var checkbox = checkboxes[i];
                    console.log('Проверка чекбокса:', checkbox.id || 'без ID');

                    // Если ID содержит 'Selected', это может быть чекбокс категории
                    if (checkbox.id && checkbox.id.includes('Selected')) {{
                        console.log('Найден чекбокс категории:', checkbox.id);
                        checkbox.scrollIntoView({{block: 'center'}});
                        checkbox.click();
                        return true;
                    }}
                }}

                return false;
            }})();
            """
            result = self.driver.execute_script(script)
            if result:
                self.logger.info("Успешно выбран чекбокс через прямой JavaScript")
                return True
            else:
                self.logger.warning("Не удалось найти подходящий чекбокс через прямой JavaScript")
        except Exception as e:
            self.logger.error(f"Ошибка при выполнении прямого JavaScript: {e}")

        self.logger.error(f"Не удалось выбрать категорию '{category_name}' ни одной из стратегий")
        self._save_debug_screenshot(f"checkbox_not_found_fallback_{category_name.replace(' ','_')}")
        return False

    def select_accepting_new_clients(self) -> bool:
        """
        Выбирает чекбокс 'Accepting new clients' с помощью CheckboxHelper.
        """
        self.logger.info("Выбор чекбокса 'Accepting new clients'")
        # Попытка выбора по ID
        if self.checkbox_helper.select_checkbox_by_id("acceptClients"):
            return True
        # Попытка выбора по тексту метки или через JS
        if self.checkbox_helper.select_checkbox_by_text("Accepting new clients"):
            return True
        # Фоллбек: попытка через общий поиск по тексту
        if self._find_and_check_checkbox_by_text("Accepting new clients"):
            return True
        self.logger.error("Не удалось выбрать чекбокс 'Accepting new clients'")
        return False

    def clear(self) -> None:
        """ Очищает форму поиска (Selenium). """
        self.logger.info("Очистка формы поиска (Selenium)")

        # Закрываем баннер с куки, если он есть
        self.close_cookie_banner()
        reset_clicked = False
        # Сначала пытаемся нажать кнопку Reset
        try:
            # Пробуем найти по XPath
            reset_button = self.wait_short.until(EC.element_to_be_clickable((By.XPATH, self.RESET_BUTTON_SELECTOR_XPATH)))
            self.logger.debug("Найдена кнопка Reset по XPath")
            reset_button.click()
            random_sleep(0.5, 1, "после клика Reset (XPath)")
            reset_clicked = True
            self.logger.info("Форма очищена с помощью кнопки RESET SEARCH (XPath).")
        except TimeoutException:
            self.logger.debug("Кнопка Reset не найдена по XPath, пробуем CSS...")
            try:
                reset_button = self.wait_short.until(EC.element_to_be_clickable((By.CSS_SELECTOR, self.RESET_BUTTON_SELECTOR_CSS)))
                self.logger.debug("Найдена кнопка Reset по CSS")
                reset_button.click()
                random_sleep(0.5, 1, "после клика Reset (CSS)")
                reset_clicked = True
                self.logger.info("Форма очищена с помощью кнопки RESET SEARCH (CSS).")
            except TimeoutException:
                 self.logger.warning("Кнопка RESET SEARCH не найдена или не кликабельна.")
            except Exception as e:
                 self.logger.warning(f"Ошибка при клике на кнопку Reset (CSS): {e}")
        except Exception as e:
             self.logger.warning(f"Ошибка при клике на кнопку Reset (XPath): {e}")

        # Если кнопка не сработала или не найдена, очищаем вручную
        if not reset_clicked:
            self.logger.info("Кнопка Reset не сработала, очищаем поля вручную...")
            self._clear_field(By.CSS_SELECTOR, self.LAST_NAME_SELECTOR, "фамилия")
            self._clear_field(By.CSS_SELECTOR, self.FIRST_NAME_SELECTOR, "имя")
            self._clear_field(By.CSS_SELECTOR, self.CITY_SELECTOR, "город")
            # TODO: Добавить снятие чекбоксов, когда _uncheck_checkbox будет готов
            # self._uncheck_checkbox(By.XPATH, self.ACCEPTING_CLIENTS_SELECTOR_XPATH, "Accepting new clients")
            # ... и для категорий ...
            self.logger.info("Поля формы очищены вручную.")

    def click_search_button(self) -> bool: # Изменяем на возврат bool
        """
        Нажимает кнопку поиска (Selenium).
        Возвращает True при успехе, False при неудаче.
        """
        self.logger.info("[click_search_button] START: Attempt to click SEARCH button")

        # Закрываем баннер с куки, если он есть
        self.close_cookie_banner()
        search_clicked = False
        # Сначала пробуем по CSS
        try:
            self.logger.debug("[click_search_button] Waiting for SEARCH button (CSS)")
            search_button = self.wait_medium.until(EC.element_to_be_clickable((By.CSS_SELECTOR, self.SEARCH_BUTTON_SELECTOR_CSS)))
            self.logger.debug("[click_search_button] Found SEARCH button via CSS selector")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", search_button)
            self.logger.debug("[click_search_button] Scrolled to SEARCH button (CSS)")
            random_sleep(0.3, 0.8, "после scroll к кнопке Search (CSS)")
            search_button.click()
            search_clicked = True
            self.logger.info("[click_search_button] SEARCH button clicked (CSS)")
        except TimeoutException:
            self.logger.debug("[click_search_button] SEARCH button not clickable via CSS, trying XPath")
            try:
                self.logger.debug("[click_search_button] Waiting for SEARCH button (XPath)")
                search_button = self.wait_medium.until(EC.element_to_be_clickable((By.XPATH, self.SEARCH_BUTTON_SELECTOR_XPATH)))
                self.logger.debug("[click_search_button] Found SEARCH button via XPath selector")
                self.driver.execute_script("arguments[0].scrollIntoView(true);", search_button)
                self.logger.debug("[click_search_button] Scrolled to SEARCH button (XPath)")
                random_sleep(0.3, 0.8, "после scroll к кнопке Search (XPath)")
                search_button.click()
                search_clicked = True
                self.logger.info("[click_search_button] SEARCH button clicked (XPath)")
            except TimeoutException:
                self.logger.error("[click_search_button] SEARCH button not found/clickable via CSS or XPath")
            except Exception as e_xpath:
                self.logger.error(f"[click_search_button] Exception when clicking SEARCH button (XPath): {e_xpath}")
        except Exception as e_css:
            self.logger.error(f"[click_search_button] Exception when clicking SEARCH button (CSS): {e_css}")

        # Если не удалось найти кнопку по CSS или XPath, пробуем JavaScript
        if not search_clicked:
            self.logger.info("[click_search_button] Trying to find and click SEARCH button using JavaScript")
            try:
                js_script = """
                function findAndClickSearchButton() {
                    // Попытка 1: По атрибуту data-webform-action
                    var searchBtn = document.querySelector('a[data-webform-action="Rechercher"]');
                    if (searchBtn) {
                        searchBtn.click();
                        return true;
                    }

                    // Попытка 2: По тексту кнопки
                    var buttons = document.querySelectorAll('button, a, input[type="submit"]');
                    for (var i = 0; i < buttons.length; i++) {
                        var btn = buttons[i];
                        var btnText = btn.innerText ? btn.innerText.toLowerCase() : '';
                        var btnValue = btn.value ? btn.value.toLowerCase() : '';

                        if (btnText.includes('search') || btnText.includes('rechercher') ||
                            btnValue.includes('search') || btnValue.includes('rechercher')) {
                            btn.click();
                            return true;
                        }
                    }

                    // Попытка 3: По классу кнопки
                    var primaryButtons = document.querySelectorAll('.button.primary, .button.radius, button[type="submit"], input[type="submit"]');
                    if (primaryButtons.length > 0) {
                        primaryButtons[0].click();
                        return true;
                    }

                    // Попытка 4: Первая кнопка типа submit
                    var submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                    if (submitButtons.length > 0) {
                        submitButtons[0].click();
                        return true;
                    }

                    // Попытка 5: Первая кнопка с классом button
                    var anyButtons = document.querySelectorAll('.button');
                    if (anyButtons.length > 0) {
                        // Исключаем кнопки с классом secondary
                        for (var j = 0; j < anyButtons.length; j++) {
                            if (!anyButtons[j].classList.contains('secondary')) {
                                anyButtons[j].click();
                                return true;
                            }
                        }
                    }

                    // Попытка 6: Симулируем отправку формы
                    var form = document.querySelector('form');
                    if (form) {
                        form.submit();
                        return true;
                    }

                    return false;
                }

                return findAndClickSearchButton();
                """

                result = self.driver.execute_script(js_script)
                if result:
                    self.logger.info("[click_search_button] Successfully clicked SEARCH button using JavaScript")
                    search_clicked = True
                else:
                    self.logger.warning("[click_search_button] JavaScript could not find and click SEARCH button")
            except Exception as e_js:
                self.logger.error(f"[click_search_button] Exception when using JavaScript to click SEARCH button: {e_js}")

        if not search_clicked:
            self.logger.warning("[click_search_button] SEARCH click failed, saving debug screenshot and returning False")
            self._save_debug_screenshot("search_button_click_failed")
            return False

        self.logger.info("[click_search_button] END: SEARCH click successful, returning True")
        return True

    def click_captcha(self) -> bool: # Изменяем на возврат bool
        """
        Кликает по чекбоксу reCAPTCHA.
        Возвращает True, если чекбокс найден и кликнут (или не найден, т.к. может отсутствовать).
        Возвращает False при явной ошибке.
        """
        self.logger.info("[click_captcha] START: Attempt to click reCAPTCHA checkbox")

        # Закрываем баннер с куки, если он есть
        self.close_cookie_banner()

        try:
            # Ожидаем iframe и переключаемся в него
            self.logger.debug("[click_captcha] Waiting for reCAPTCHA iframe and switching to it")
            self.wait_medium.until(EC.frame_to_be_available_and_switch_to_it(self.RECAPTCHA_IFRAME_SELECTOR))

            # Ожидаем чекбокс и кликаем по нему
            self.logger.debug("[click_captcha] Waiting for reCAPTCHA checkbox to be clickable")
            captcha_checkbox = self.wait_medium.until(EC.element_to_be_clickable(self.RECAPTCHA_CHECKBOX_SELECTOR))

            # Клик по чекбоксу
            captcha_checkbox.click()
            self.logger.info("[click_captcha] Clicked reCAPTCHA checkbox successfully")

            # Переключаемся обратно из iframe
            self.driver.switch_to.default_content()
            self.logger.debug("[click_captcha] Switched back to default content from iframe")

            self.logger.info("[click_captcha] END: reCAPTCHA click flow completed successfully")
            return True

        except TimeoutException:
            # Если iframe или чекбокс не найдены за таймаут - это нормально, капчи может и не быть
            self.logger.info("[click_captcha] Timeout waiting for iframe/checkbox: treating as no captcha present or already passed")
            # Важно вернуться из iframe, если мы успели в него переключиться, но чекбокс не нашли
            try:
                self.driver.switch_to.default_content()
            except Exception:
                pass # Игнорируем ошибку, если мы не были в iframe
            self.logger.info("[click_captcha] END: No captcha to click, returning True")
            return True
        except Exception as e:
            self.logger.error(f"[click_captcha] Exception during reCAPTCHA click: {e}", exc_info=self.debug)
            # Важно вернуться из iframe при любой ошибке
            try:
                 self.driver.switch_to.default_content()
            except Exception:
                 pass
            self.logger.warning("[click_captcha] Saving debug screenshot for captcha click error and returning False")
            self._save_debug_screenshot("captcha_click_error")
            return False

    def close_cookie_banner(self) -> bool:
        """
        Закрывает баннер с куки, если он присутствует на странице.

        Returns:
            bool: True, если баннер был закрыт или его не было, False в случае ошибки.
        """
        self.logger.info("Попытка закрыть баннер с куки")

        try:
            # Попытка 1: Кнопка "Accept All Cookies"
            cookie_buttons = [
                (By.ID, "onetrust-accept-btn-handler"),  # Стандартная кнопка OneTrust
                (By.XPATH, "//button[contains(., 'Accept')]"),  # Кнопка с текстом "Accept"
                (By.XPATH, "//button[contains(., 'Accepter')]"),  # Французский вариант
                (By.XPATH, "//button[contains(@class, 'cookie')]"),  # Кнопка с классом, содержащим "cookie"
                (By.XPATH, "//a[contains(@class, 'cookie')]"),  # Ссылка с классом, содержащим "cookie"
                (By.CSS_SELECTOR, ".cookie-banner .close"),  # Кнопка закрытия в баннере куки
            ]

            for locator in cookie_buttons:
                try:
                    elements = self.driver.find_elements(*locator)
                    for element in elements:
                        if element.is_displayed():
                            self.logger.info(f"Найдена кнопка закрытия баннера с куки: {locator}")
                            element.click()
                            time.sleep(0.5)  # Пауза для завершения анимации закрытия
                            self.logger.info("Баннер с куки успешно закрыт")
                            return True
                except Exception as e:
                    self.logger.debug(f"Ошибка при попытке закрыть баннер с куки по локатору {locator}: {e}")

            # Попытка 2: Использование JavaScript для закрытия баннера
            js_script = """
            // Попытка закрыть баннер OneTrust
            var onetrustBtn = document.getElementById('onetrust-accept-btn-handler');
            if (onetrustBtn) {
                onetrustBtn.click();
                return true;
            }

            // Попытка скрыть баннер с куки через CSS
            var cookieBanners = [
                document.getElementById('onetrust-banner-sdk'),
                document.getElementById('onetrust-consent-sdk'),
                document.querySelector('.cookie-banner'),
                document.querySelector('[class*="cookie"]'),
                document.querySelector('[id*="cookie"]')
            ];

            for (var i = 0; i < cookieBanners.length; i++) {
                var banner = cookieBanners[i];
                if (banner && banner.style) {
                    banner.style.display = 'none';
                    return true;
                }
            }

            return false; // Баннер не найден или уже закрыт
            """

            result = self.driver.execute_script(js_script)
            if result:
                self.logger.info("Баннер с куки успешно закрыт через JavaScript")
                return True

            # Если баннер не найден, считаем это успехом (возможно, его нет на странице)
            self.logger.info("Баннер с куки не найден или уже закрыт")
            return True

        except Exception as e:
            self.logger.error(f"Ошибка при попытке закрыть баннер с куки: {e}")
            return False

    def _uncheck_all_categories_except(self, category_to_keep: str) -> None:
        """
        Снимает выбор со всех категорий, кроме указанной.

        Args:
            category_to_keep: Название категории, которую нужно оставить выбранной (если она уже выбрана)
        """
        self.logger.debug(f"Снимаем выбор со всех категорий, кроме '{category_to_keep}'")

        # Список ID всех чекбоксов категорий
        all_category_ids = [
            "ListeClienteleDesserviesLeftColumn_0__Selected",  # Individuals / Particuliers
            "ListeClienteleDesserviesLeftColumn_1__Selected",  # Large companies / Grandes entreprises
            "ListeClienteleDesserviesLeftColumn_2__Selected",  # NFPOs / OSBL
            "ListeClienteleDesserviesLeftColumn_3__Selected",  # Professional firms / Cabinets professionnels
            "ListeClienteleDesserviesLeftColumn_4__Selected",  # Public corporations / Sociétés publiques
            "ListeClienteleDesserviesRightColumn_0__Selected", # Retailers / Détaillants
            "ListeClienteleDesserviesRightColumn_1__Selected", # Self-employed workers / Travailleurs autonomes
            "ListeClienteleDesserviesRightColumn_2__Selected", # SMEs / PME
            "ListeClienteleDesserviesRightColumn_3__Selected", # Start-ups / Entreprises en démarrage
            "ListeClienteleDesserviesRightColumn_4__Selected", # Syndicates of co-owners / Syndicats de copropriété
        ]

        # Словарь соответствия категорий и их ID
        category_ids = {
            "individuals": "ListeClienteleDesserviesLeftColumn_0__Selected",
            "large companies": "ListeClienteleDesserviesLeftColumn_1__Selected",
            "nfpos": "ListeClienteleDesserviesLeftColumn_2__Selected",
            "professional firms": "ListeClienteleDesserviesLeftColumn_3__Selected",
            "public corporations": "ListeClienteleDesserviesLeftColumn_4__Selected",
            "retailers": "ListeClienteleDesserviesRightColumn_0__Selected",
            "self-employed workers": "ListeClienteleDesserviesRightColumn_1__Selected",
            "smes": "ListeClienteleDesserviesRightColumn_2__Selected",
            "start-ups": "ListeClienteleDesserviesRightColumn_3__Selected",
            "syndicates of co-owners": "ListeClienteleDesserviesRightColumn_4__Selected",
            # Добавляем французские варианты
            "particuliers": "ListeClienteleDesserviesLeftColumn_0__Selected",
            "grandes entreprises": "ListeClienteleDesserviesLeftColumn_1__Selected",
            "osbl": "ListeClienteleDesserviesLeftColumn_2__Selected",
            "cabinets professionnels": "ListeClienteleDesserviesLeftColumn_3__Selected",
            "sociétés publiques": "ListeClienteleDesserviesLeftColumn_4__Selected",
            "détaillants": "ListeClienteleDesserviesRightColumn_0__Selected",
            "travailleurs autonomes": "ListeClienteleDesserviesRightColumn_1__Selected",
            "pme": "ListeClienteleDesserviesRightColumn_2__Selected",
            "entreprises en démarrage": "ListeClienteleDesserviesRightColumn_3__Selected",
            "syndicats de copropriété": "ListeClienteleDesserviesRightColumn_4__Selected"
        }

        # ID чекбокса, который нужно оставить выбранным
        category_to_keep_id = category_ids.get(category_to_keep.lower())

        # Снимаем выбор со всех чекбоксов, кроме указанного
        for checkbox_id in all_category_ids:
            if checkbox_id != category_to_keep_id:
                try:
                    checkbox = self.driver.find_element(By.ID, checkbox_id)
                    if checkbox.is_selected():
                        # Скролл к чекбоксу
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", checkbox)
                        time.sleep(0.3)  # Короткая пауза для завершения скролла

                        # Клик по чекбоксу, чтобы снять выбор
                        checkbox.click()
                        self.logger.debug(f"Снят выбор с чекбокса категории с ID: {checkbox_id}")
                except Exception as e:
                    self.logger.debug(f"Ошибка при попытке снять выбор с чекбокса с ID {checkbox_id}: {e}")

    # Остальные методы _check_checkbox, _uncheck_checkbox, _attempt_check пока не реализуем
