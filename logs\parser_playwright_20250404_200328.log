2025-04-04 20:03:28,222 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_200328.log
2025-04-04 20:03:28,225 - <PERSON><PERSON><PERSON><PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:03:28,226 - <PERSON><PERSON>QuebecParser - INFO - ==================================================
2025-04-04 20:03:28,226 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 20:03:28,226 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 20:03:28,226 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:03:28,226 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:03:36,321 - CpaQuebecParser - DEBUG - Пауза на 1.01 сек. (после навигации)
2025-04-04 20:04:03,250 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 20:04:03,476 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
