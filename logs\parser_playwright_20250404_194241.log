2025-04-04 19:42:41,028 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_194241.log
2025-04-04 19:42:41,031 - <PERSON><PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:42:41,031 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 19:42:41,031 - C<PERSON>Q<PERSON>becParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:42:41,031 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:42:41,032 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:42:41,032 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:43:02,561 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 19:43:02,703 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
