import cloudscraper
import requests  # для fallback, если понадобится
from fake_useragent import UserAgent
from typing import Dict, Any, Optional
import logging
import json
import re
from bs4 import BeautifulSoup


class CpaQuebecClient:
    """
    HTTP-клиент для поиска CPA в каталоге CPA Quebec без использования Selenium.
    Использует скрытый API по адресу /api/sitecore/FindACPA/FindACPABottinFormSubmit?Length=8.
    """
    def __init__(self,
                 base_url: str = "https://cpaquebec.ca",
                 default_headers: Optional[Dict[str, str]] = None):
        # Базовый URL без завершающего слэша
        self.base_url = base_url.rstrip('/')
        # Логгер для подробного вывода
        self.logger = logging.getLogger(__name__)
        self.logger.debug(f"Initializing CpaQuebecClient with base_url={self.base_url}")
        # Используем cloudscraper для обхода защиты Cloudflare
        self.session = cloudscraper.create_scraper()
        # Генерируем User-Agent и устанавливаем перед первоначальным GET
        ua = UserAgent()
        self.session.headers.update({'User-Agent': ua.random})
        # Первоначальный GET для установки cookies и контекста
        init_url = f"{self.base_url}/en/find-a-cpa/cpa-directory/"
        try:
            resp = self.session.get(init_url, timeout=10)
            resp.raise_for_status()
            self.logger.debug(f"Initial GET to {init_url} succeeded; status={resp.status_code}; cookies={self.session.cookies.get_dict()}")
        except Exception as e:
            self.logger.warning(f"Initial page GET failed: {e}")
        # Устанавливаем AJAX-заголовки после получения cookies
        ajax_headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Referer': f'{self.base_url}/en/find-a-cpa/cpa-directory/',
            'Origin': self.base_url,
            'Accept-Language': 'en-US,en;q=0.9',
        }
        if default_headers:
            ajax_headers.update(default_headers)
        self.session.headers.update(ajax_headers)

    def search(self,
               criteria: Dict[str, Any],
               page_number: int = 0) -> Dict[str, Any]:
        """
        Выполняет POST-запрос к API поиска CPA.

        Args:
            criteria: Словарь с параметрами формы (Nom, Prenom, Ville, ListaClienteleDesserviesLeftColumn[i].Selected и т.д.).
            page_number: Номер страницы (0-based).

        Returns:
            Распарсенный JSON ответ от сервера.
        """
        url = f"{self.base_url}/api/sitecore/FindACPA/FindACPABottinFormSubmit?Length=8"
        # Базовые поля формы (без CriteresRechercheOrinal)
        payload = {
            'Action': 'Rechercher',  # указываем действие поиска
            'ActionParams': '',
            'PageNumber': page_number,
            'AfficherResultatMap': 'False',
        }
        # Добавляем критерии поиска (Desc/Selected/NoRepertoire для всех категорий и др.)
        payload.update(criteria)
        # Сериализуем исходные критерии в JSON для серверного парсинга
        try:
            orig_criteria = {k: v for k, v in payload.items()
                             if k not in ('Action', 'ActionParams', 'PageNumber', 'AfficherResultatMap')}
            # Правильное имя поля для отправки оригинальных критериев
            payload['CriteresRechercheOriginal'] = json.dumps(orig_criteria)
        except Exception as e:
            self.logger.warning(f"Не удалось сериализовать CriteresRechercheOriginal: {e}")

        # Логируем отправку запроса
        self.logger.info(f"Sending POST request to {url} with payload keys={list(payload.keys())} page_number={page_number}")
        try:
            response = self.session.post(url, data=payload)
            self.logger.debug(f"POST {url} responded with status {response.status_code}")
            response.raise_for_status()
        except Exception as e:
            self.logger.error(f"HTTP request failed: {e}", exc_info=True)
            raise

        # Пытаемся декодировать JSON и логируем ключи
        try:
            data = response.json()
            if isinstance(data, dict):
                self.logger.debug(f"Response JSON keys: {list(data.keys())}")
            return data
        except ValueError:
            text = response.text
            # Обработка JS-редиректа вида window.location='/path';
            match = re.search(r"window\.location='([^']+)'", text)
            if match:
                redirect_path = match.group(1)
                redirect_url = self.base_url.rstrip('/') + redirect_path
                self.logger.info(f"Following redirect to {redirect_url}")
                
                # Переходим по редиректу
                html_resp = self.session.get(redirect_url)
                html_resp.raise_for_status()
                html = html_resp.text
                self.logger.debug(f"Got redirected page: {len(html)} bytes")
                
                # После редиректа создаём новый поисковый запрос
                # Находим элемент формы поиска и повторно используем его с новой сессией
                soup = BeautifulSoup(html, 'html.parser')
                form = soup.find('form', attrs={'Id': 'FindACPABottinForm'})
                
                if form:
                    self.logger.info("Found search form on redirected page, submitting it directly")
                    form_action = form.get('action')
                    form_url = self.base_url.rstrip('/') + form_action if form_action else redirect_url
                    
                    # Добавляем оригинальные критерии поиска к форме и отправляем
                    new_response = self.session.post(form_url, data=payload)
                    new_response.raise_for_status()
                    self.logger.debug(f"Second form POST response: {new_response.status_code}")
                    
                    return new_response.text
                else:
                    self.logger.warning("Search form not found on redirected page")
                    return html
                
            # Возвращаем текст, если не JSON и нет редиректа
            self.logger.warning(f"Response is not valid JSON or redirect; status={response.status_code}")
            return text


if __name__ == '__main__':
    # Пример использования
    client = CpaQuebecClient()
    # Критерии для категории 'Individuals'
    criteria = {
        'ListeClienteleDesserviesLeftColumn[0].Selected': 'true',
        'ListeClienteleDesserviesLeftColumn[0].Desc': 'Individuals',
        'ListeClienteleDesserviesLeftColumn[0].NoRepertoire': '1878'
    }
    result = client.search(criteria, page_number=0)
    print(result) 