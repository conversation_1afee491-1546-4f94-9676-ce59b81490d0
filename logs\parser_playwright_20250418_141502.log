2025-04-18 14:15:02,841 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_141502.log
2025-04-18 14:15:02,848 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 14:15:02,848 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-18 14:15:02,849 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 14:15:02,849 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-18 14:15:02,849 - CpaQuebecParser - INFO - ==================================================
2025-04-18 14:15:02,850 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-18 14:15:19,112 - CpaQuebecParser - DEBUG - Пауза на 1.35 сек. (после driver.get)
2025-04-18 14:15:20,579 - CpaQuebecParser - DEBUG - Пауза на 0.66 сек. (после scroll к кнопке cookie)
2025-04-18 14:15:34,446 - CpaQuebecParser - DEBUG - Пауза на 0.61 сек. (после клика Reset (XPath))
2025-04-18 14:15:39,061 - CpaQuebecParser - DEBUG - Пауза на 0.59 сек. (после очистки формы)
2025-04-18 14:16:52,865 - CpaQuebecParser - DEBUG - Пауза на 2.54 сек. (между категориями, после Individuals)
2025-04-18 14:17:01,104 - CpaQuebecParser - DEBUG - Пауза на 2.13 сек. (после driver.get)
2025-04-18 14:17:33,813 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
