#!/usr/bin/env python3
from bs4 import BeautifulSoup

# Загружаем сохраненную страницу
with open('current_page.html', 'r', encoding='utf-8') as f:
    html = f.read()

soup = BeautifulSoup(html, 'html.parser')

# Анализируем форму поиска
print("=== Анализ формы поиска ===")
forms = soup.find_all('form')
print(f"Найдено форм на странице: {len(forms)}")

for i, form in enumerate(forms):
    print(f"\nФорма #{i+1}:")
    print(f"ID: {form.get('id')}")
    print(f"Action: {form.get('action')}")
    print(f"Method: {form.get('method')}")
    
    # Ищем чекбоксы категорий клиентов
    checkboxes = form.find_all('input', type='checkbox')
    print(f"Найдено чекбоксов: {len(checkboxes)}")
    
    for j, checkbox in enumerate(checkboxes[:5]):  # Показываем только первые 5
        print(f"  Чекбокс #{j+1}:")
        print(f"    ID: {checkbox.get('id')}")
        print(f"    Name: {checkbox.get('name')}")
        print(f"    Value: {checkbox.get('value')}")
        
        # Ищем связанный label
        if checkbox.get('id'):
            label = soup.find('label', attrs={'for': checkbox.get('id')})
            if label:
                print(f"    Label: {label.get_text(strip=True)}")
    
    # Ищем кнопку поиска
    buttons = form.find_all(['button', 'input'], type=['submit', 'button'])
    print(f"Найдено кнопок: {len(buttons)}")
    
    for j, button in enumerate(buttons):
        print(f"  Кнопка #{j+1}:")
        print(f"    Type: {button.get('type')}")
        print(f"    ID: {button.get('id')}")
        print(f"    Name: {button.get('name')}")
        print(f"    Value/Text: {button.get('value') or button.get_text(strip=True)}")

# Анализируем контейнеры результатов
print("\n=== Анализ контейнеров результатов ===")
result_containers = [
    soup.find(id='SectionResultats'),
    soup.find(id='AfficherResultatsListContainer'),
    soup.find(id='AfficherResultatsListStaticContainer')
]

for i, container in enumerate(result_containers):
    if container:
        print(f"Контейнер #{i+1} найден: {container.name}#{container.get('id')}")
        links = container.find_all('a')
        print(f"  Содержит ссылок: {len(links)}")
    else:
        print(f"Контейнер #{i+1} не найден")

# Ищем другие потенциальные контейнеры результатов
print("\n=== Поиск других контейнеров результатов ===")
sections = soup.find_all(['section', 'div'], class_=lambda c: c and ('result' in c.lower() or 'list' in c.lower()))
for i, section in enumerate(sections[:5]):  # Показываем только первые 5
    print(f"Потенциальный контейнер #{i+1}:")
    print(f"  Tag: {section.name}")
    print(f"  ID: {section.get('id')}")
    print(f"  Class: {section.get('class')}")
    links = section.find_all('a')
    print(f"  Содержит ссылок: {len(links)}")
    
    # Если есть ссылки, показываем первые 3
    if links and len(links) > 0:
        print("  Примеры ссылок:")
        for j, link in enumerate(links[:3]):
            print(f"    Ссылка #{j+1}: {link.get('href')} - {link.get_text(strip=True)}")
