2025-04-23 17:20:01,294 - C<PERSON>QuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250423_172001.log
2025-04-23 17:20:01,297 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-23 17:20:01,298 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-23 17:20:01,298 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals', 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-23 17:20:01,298 - CpaQuebecParser - INFO - ==================================================
2025-04-23 17:20:01,298 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-23 17:22:14,503 - CpaQuebecParser - INFO - Парсинг (Selenium) завершен. Найдено и сохранено записей: 0
2025-04-23 17:22:14,608 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 0
