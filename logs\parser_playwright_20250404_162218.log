2025-04-04 16:22:18,687 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_162218.log
2025-04-04 16:22:18,691 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 16:22:18,691 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:22:18,691 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:22:18,692 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:22:18,692 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:22:23,392 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): Navigator.__init__() missing 3 required positional arguments: 'debug_dir', 'screenshots_dir', and 'navigation_timeout'
2025-04-04 16:22:23,393 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 82, in run_parser
    with CpaQuebecParser(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 125, in __enter__
    self._initialize_components() # Инициализируем компоненты после запуска браузера
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 100, in _initialize_components
    self.navigator = Navigator(self.page, self.logger, self.debug)
TypeError: Navigator.__init__() missing 3 required positional arguments: 'debug_dir', 'screenshots_dir', and 'navigation_timeout'

2025-04-04 16:22:23,394 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
