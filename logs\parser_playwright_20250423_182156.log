2025-04-23 18:21:56,162 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250423_182156.log
2025-04-23 18:21:56,165 - C<PERSON>Q<PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-23 18:21:56,165 - CpaQuebecParser - INFO - ==================================================
2025-04-23 18:21:56,165 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-23 18:21:56,165 - C<PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals', 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-23 18:21:56,165 - CpaQuebecParser - INFO - ==================================================
2025-04-23 18:21:56,165 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-23 18:22:11,887 - CpaQuebecParser - DEBUG - Пауза на 1.90 сек. (после driver.get)
2025-04-23 18:22:13,895 - CpaQuebecParser - DEBUG - Пауза на 0.45 сек. (после scroll к кнопке cookie)
2025-04-23 18:22:56,292 - CpaQuebecParser - DEBUG - Пауза на 0.71 сек. (после клика Reset (XPath))
2025-04-23 18:22:59,816 - CpaQuebecParser - DEBUG - Пауза на 0.88 сек. (после очистки формы)
2025-04-23 18:25:05,381 - CpaQuebecParser - DEBUG - Пауза на 4.62 сек. (между категориями, после Individuals)
