#!/usr/bin/env python3
from bs4 import BeautifulSoup

# Загружаем сохраненную страницу
with open('direct_search_page.html', 'r', encoding='utf-8') as f:
    html = f.read()

soup = BeautifulSoup(html, 'html.parser')

# Ищем форму поиска
print("=== Анализ формы поиска ===")
form = soup.find('form', id='FindACPABottinForm')

if form:
    print(f"Форма найдена: {form.name}#{form.get('id')}")
    print(f"Action: {form.get('action')}")
    print(f"Method: {form.get('method')}")
    
    # Ищем все поля формы
    inputs = form.find_all(['input', 'select', 'textarea'])
    print(f"Найдено полей: {len(inputs)}")
    
    # Группируем поля по типу
    input_types = {}
    for input_field in inputs:
        field_type = input_field.get('type', input_field.name)
        if field_type not in input_types:
            input_types[field_type] = []
        input_types[field_type].append(input_field)
    
    # Выводим информацию по типам полей
    for field_type, fields in input_types.items():
        print(f"\nПоля типа '{field_type}': {len(fields)}")
        for i, field in enumerate(fields[:5]):  # Показываем только первые 5
            print(f"  Поле #{i+1}:")
            print(f"    Name: {field.get('name')}")
            print(f"    ID: {field.get('id')}")
            print(f"    Value: {field.get('value')}")
    
    # Ищем кнопку отправки формы
    submit_buttons = form.find_all(['button', 'input'], type=['submit', 'button'])
    print(f"\nНайдено кнопок отправки: {len(submit_buttons)}")
    for i, button in enumerate(submit_buttons):
        print(f"  Кнопка #{i+1}:")
        print(f"    Type: {button.get('type')}")
        print(f"    ID: {button.get('id')}")
        print(f"    Name: {button.get('name')}")
        print(f"    Value/Text: {button.get('value') or button.get_text(strip=True)}")
    
    # Ищем скрипты, связанные с формой
    scripts = soup.find_all('script')
    form_scripts = []
    for script in scripts:
        script_text = script.string
        if script_text and ('FindACPABottinForm' in script_text or 'find-a-cpa' in script_text.lower()):
            form_scripts.append(script)
    
    print(f"\nНайдено скриптов, связанных с формой: {len(form_scripts)}")
    for i, script in enumerate(form_scripts):
        script_text = script.string
        if script_text:
            print(f"  Скрипт #{i+1} (первые 100 символов):")
            print(f"    {script_text[:100].strip()}...")
else:
    print("Форма поиска не найдена на странице!")
