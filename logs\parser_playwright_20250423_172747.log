2025-04-23 17:27:47,283 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250423_172747.log
2025-04-23 17:27:47,289 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-23 17:27:47,290 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-23 17:27:47,290 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-23 17:27:47,290 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals', 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-23 17:27:47,290 - CpaQuebecParser - INFO - ==================================================
2025-04-23 17:27:47,290 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-23 17:28:04,778 - CpaQuebecParser - DEBUG - Пауза на 1.64 сек. (после driver.get)
2025-04-23 17:28:06,501 - CpaQuebecParser - DEBUG - Пауза на 0.53 сек. (после scroll к кнопке cookie)
2025-04-23 17:28:48,306 - CpaQuebecParser - DEBUG - Пауза на 0.87 сек. (после клика Reset (XPath))
2025-04-23 17:28:52,427 - CpaQuebecParser - DEBUG - Пауза на 0.87 сек. (после очистки формы)
2025-04-23 17:29:10,076 - CpaQuebecParser - DEBUG - Пауза на 2.78 сек. (между категориями, после Individuals)
2025-04-23 17:29:12,858 - CpaQuebecParser - INFO - Парсинг (Selenium) завершен. Найдено и сохранено записей: 0
2025-04-23 17:29:12,928 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 0
