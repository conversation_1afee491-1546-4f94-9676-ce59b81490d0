2025-04-04 20:28:47,369 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_202847.log
2025-04-04 20:28:47,373 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 20:28:47,374 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:28:47,374 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False}
2025-04-04 20:28:47,374 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:28:47,374 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-04 20:29:05,249 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
