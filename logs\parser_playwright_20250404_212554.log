2025-04-04 21:25:54,079 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_212554.log
2025-04-04 21:25:54,082 - <PERSON><PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 21:25:54,082 - CpaQuebecParser - INFO - ==================================================
2025-04-04 21:25:54,082 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 21:25:54,082 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 21:25:54,082 - CpaQuebecParser - INFO - ==================================================
2025-04-04 21:25:54,083 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 21:26:07,425 - CpaQuebecParser - DEBUG - Пауза на 1.72 сек. (после driver.get)
2025-04-04 21:26:09,296 - CpaQuebecParser - DEBUG - Пауза на 0.39 сек. (после scroll к кнопке cookie)
2025-04-04 21:26:20,425 - CpaQuebecParser - DEBUG - Пауза на 0.84 сек. (после клика Reset (XPath))
2025-04-04 21:26:23,294 - CpaQuebecParser - DEBUG - Пауза на 0.87 сек. (после очистки формы)
2025-04-04 21:26:25,165 - CpaQuebecParser - DEBUG - Пауза на 3.74 сек. (между категориями, после Individuals)
2025-04-04 21:26:28,930 - CpaQuebecParser - DEBUG - Пауза на 2.35 сек. (перед retry 2 для категории Large companies)
2025-04-04 21:26:31,294 - CpaQuebecParser - DEBUG - Пауза на 5.93 сек. (перед retry 3 для категории Large companies)
2025-04-04 21:26:33,196 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
