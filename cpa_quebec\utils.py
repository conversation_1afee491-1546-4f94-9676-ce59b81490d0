#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import logging
import logging.handlers
import random
import re
import requests
from fake_useragent import UserAgent, FakeUserAgentError
from .config import LOGS_DIR, DEFAULT_USER_AGENT, MAX_RETRY_ATTEMPTS, RETRY_DELAY
from typing import List, Optional

# --- Настройка логгирования ---
def setup_logging(log_level=logging.INFO, log_file_prefix="parser_playwright"):
    """Настраивает логгирование в файл и консоль."""
    logger = logging.getLogger('CpaQuebecParser')
    # Предотвращаем дублирование обработчиков, если функция вызывается несколько раз
    if logger.hasHandlers():
        logger.handlers.clear()

    logger.setLevel(log_level)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Обработчик для вывода в консоль
    ch = logging.StreamHandler()
    ch.setLevel(log_level)
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    # Обработчик для записи в файл
    os.makedirs(LOGS_DIR, exist_ok=True)
    log_filename = os.path.join(LOGS_DIR, f"{log_file_prefix}_{time.strftime('%Y%m%d_%H%M%S')}.log")
    # Используем RotatingFileHandler для ограничения размера файла (например, 10MB)
    fh = logging.handlers.RotatingFileHandler(log_filename, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8')
    fh.setLevel(log_level)
    fh.setFormatter(formatter)
    logger.addHandler(fh)

    logger.info(f"Логгирование настроено. Уровень: {logging.getLevelName(log_level)}. Файл: {log_filename}")
    return logger

# --- Вспомогательные функции ---
def random_sleep(min_seconds=1, max_seconds=3, reason=""):
    """Делает паузу на случайное количество секунд."""
    sleep_time = random.uniform(min_seconds, max_seconds)
    logger = logging.getLogger('CpaQuebecParser')
    log_message = f"Пауза на {sleep_time:.2f} сек."
    if reason:
        log_message += f" ({reason})"
    logger.debug(log_message)
    time.sleep(sleep_time)

def clean_text(text: str) -> str:
    """Очищает текст от лишних пробелов и символов новой строки."""
    if not text:
        return ""
    # Заменяем множественные пробелы/табуляции/переводы строк на один пробел
    cleaned = re.sub(r'\s+', ' ', text).strip()
    return cleaned

def extract_emails(text: str) -> List[str]:
    """Извлекает email-адреса из текста."""
    if not text:
        return []
    # Более надежный паттерн для email
    email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
    emails = re.findall(email_pattern, text)
    return list(set(emails)) # Возвращаем уникальные email

def extract_phones(text: str) -> List[str]:
    """Извлекает телефонные номера из текста (упрощенный вариант)."""
    if not text:
        return []
    # Паттерн для номеров вида (XXX) XXX-XXXX, XXX-XXX-XXXX, XXX.XXX.XXXX и с пробелами
    # Добавлен опциональный +1 в начале
    phone_pattern = r'(\+?1\s*[-\.\(]?\s*)?(\(?\d{3}\)?\s*[-\.\s]?\d{3}\s*[-\.\s]?\d{4})'
    phones = re.findall(phone_pattern, text)
    # re.findall возвращает кортежи из-за групп, собираем полный номер
    extracted_phones = []
    for match in phones:
        # Собираем номер из частей, убирая лишние пробелы по краям
        full_number = "".join(match).strip()
        # Дополнительная очистка от нецифровых символов, кроме + в начале
        cleaned_number = '+' + re.sub(r'\D', '', full_number[1:]) if full_number.startswith('+') else re.sub(r'\D', '', full_number)
        # Простая проверка на минимальную длину (например, 10 цифр)
        if len(cleaned_number.replace('+', '')) >= 10:
            extracted_phones.append(full_number) # Сохраняем исходный формат

    return list(set(extracted_phones)) # Возвращаем уникальные номера

def sanitize_filename(filename: str) -> str:
    """Удаляет или заменяет недопустимые символы в имени файла."""
    # Удаляем символы, недопустимые в большинстве файловых систем
    sanitized = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', filename)
    # Заменяем множественные подчеркивания на одно
    sanitized = re.sub(r'_+', '_', sanitized)
    # Удаляем подчеркивания в начале и конце имени
    sanitized = sanitized.strip('_')
    # Ограничиваем длину имени файла (например, 200 символов)
    max_len = 200
    if len(sanitized) > max_len:
        # Обрезаем и добавляем хэш для уникальности, если нужно
        # file_ext = os.path.splitext(sanitized)[1]
        # base_name = os.path.splitext(sanitized)[0]
        # sanitized = base_name[:max_len - len(file_ext) - 10] + "_" + str(hash(filename))[:8] + file_ext
        sanitized = sanitized[:max_len] # Просто обрезаем для простоты
    return sanitized if sanitized else "unnamed_file"

# --- Класс для решения reCAPTCHA v2 через Anti-Captcha.com ---
class CaptchaSolver:
    """Решает reCAPTCHA v2 с помощью API Anti-Captcha.com."""
    def __init__(self, api_key: str, logger: logging.Logger):
        self.api_key = api_key
        self.logger = logger
        self.in_url = "https://api.anti-captcha.com/createTask"
        self.res_url = "https://api.anti-captcha.com/getTaskResult"
        self.session = requests.Session() # Используем сессию для переиспользования соединения

    def solve_recaptcha_v2(self, site_key: str, page_url: str) -> Optional[str]:
        """Отправляет запрос на решение reCAPTCHA v2 и получает результат."""
        self.logger.info(f"Отправка запроса на решение reCAPTCHA v2 для сайта {page_url}")
        payload = {
            'clientKey': self.api_key,
            'task': {
                'type': 'NoCaptchaTaskProxyless',
                'websiteURL': page_url,
                'websiteKey': site_key
            }
        }
        try:
            response = self.session.post(self.in_url, json=payload, timeout=30)
            response.raise_for_status() # Проверка на HTTP ошибки
            result = response.json()
            self.logger.debug(f"Ответ от Anti-Captcha.com (createTask): {result}")

            if result.get('errorId') == 0:
                task_id = result.get('taskId')
                self.logger.info(f"Капча отправлена. ID задачи: {task_id}. Ожидание решения...")
                return self._get_captcha_result(task_id)
            else:
                error_code = result.get('errorCode', 'UNKNOWN_ERROR')
                error_description = result.get('errorDescription', 'Неизвестная ошибка от Anti-Captcha.com API')
                self.logger.error(f"Ошибка при отправке капчи в Anti-Captcha.com: {error_code} - {error_description}")
                # Возможные ошибки: ERROR_KEY_DOES_NOT_EXIST, ERROR_ZERO_BALANCE, etc.
                if "ERROR_ZERO_BALANCE" in error_code:
                    self.logger.error("Пожалуйста, пополните баланс вашего аккаунта Anti-Captcha.com.")
                return None

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Ошибка сети при отправке запроса в Anti-Captcha.com: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка при отправке капчи: {e}")
            return None

    def _get_captcha_result(self, task_id: str, max_wait_time: int = 180, poll_interval: int = 5) -> Optional[str]:
        """Опрашивает API Anti-Captcha.com для получения результата решения."""
        start_time = time.time()
        network_error_attempts = 0
        while time.time() - start_time < max_wait_time:
            payload = {
                'clientKey': self.api_key,
                'taskId': task_id
            }
            try:
                response = self.session.post(self.res_url, json=payload, timeout=30)
                response.raise_for_status()
                result = response.json()
                self.logger.debug(f"Ответ от Anti-Captcha.com (getTaskResult): {result}")

                network_error_attempts = 0

                if result.get('errorId') == 0:
                    if result.get('status') == 'ready':
                        captcha_response = result.get('solution', {}).get('gRecaptchaResponse')
                        if captcha_response:
                            self.logger.info(f"Капча успешно решена! Токен получен.")
                            return captcha_response
                        else:
                            self.logger.error("Получен пустой токен решения капчи.")
                            return None
                    elif result.get('status') == 'processing':
                        self.logger.debug("Капча еще не готова, ожидание...")
                        time.sleep(poll_interval)
                    else:
                        self.logger.error(f"Неизвестный статус задачи: {result.get('status')}")
                        return None
                else:
                    error_code = result.get('errorCode', 'UNKNOWN_ERROR')
                    error_description = result.get('errorDescription', 'Неизвестная ошибка при получении результата')
                    self.logger.error(f"Ошибка при получении результата капчи от Anti-Captcha.com: {error_code} - {error_description}")
                    return None

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Ошибка сети при получении результата капчи: {e}")
                network_error_attempts += 1
                if network_error_attempts >= MAX_RETRY_ATTEMPTS:
                    self.logger.error(f"Превышено количество попыток ({MAX_RETRY_ATTEMPTS}) при сетевых ошибках получения результата капчи.")
                    return None # Выход после нескольких неудачных попыток
                retry_after = RETRY_DELAY * (2 ** (network_error_attempts - 1)) # Экспоненциальная задержка
                self.logger.info(f"Повторная попытка через {retry_after} сек...")
                time.sleep(retry_after)
            except Exception as e:
                 self.logger.error(f"Неожиданная ошибка при получении результата капчи: {e}")
                 return None # Выход при неожиданной ошибке

        self.logger.warning(f"Превышено время ожидания ({max_wait_time} сек) для решения капчи ID: {task_id}")
        return None

# --- Генерация User-Agent ---
_user_agent_provider = None

def get_random_user_agent() -> str:
    """Возвращает случайный User-Agent, используя fake-useragent."""
    global _user_agent_provider
    if _user_agent_provider is None:
        try:
            # fallback - на случай проблем с загрузкой данных fake-useragent
            _user_agent_provider = UserAgent(fallback=DEFAULT_USER_AGENT)
        except FakeUserAgentError:
            logger = logging.getLogger('CpaQuebecParser')
            logger.warning("Не удалось инициализировать fake-useragent. Используется User-Agent по умолчанию.")
            return DEFAULT_USER_AGENT
        except Exception as e: # Ловим другие возможные ошибки инициализации
            logger = logging.getLogger('CpaQuebecParser')
            logger.warning(f"Ошибка при инициализации fake-useragent: {e}. Используется User-Agent по умолчанию.")
            return DEFAULT_USER_AGENT

    try:
        # Пытаемся получить User-Agent для Chrome
        return _user_agent_provider.chrome
    except Exception as e:
        logger = logging.getLogger('CpaQuebecParser')
        logger.warning(f"Не удалось получить Chrome User-Agent от fake-useragent: {e}. Используется случайный или fallback.")
        try:
            # Пытаемся получить любой случайный
            return _user_agent_provider.random
        except Exception as e2:
            logger.error(f"Не удалось получить случайный User-Agent от fake-useragent: {e2}. Используется User-Agent по умолчанию.")
            # Возвращаем значение по умолчанию в крайнем случае
            return DEFAULT_USER_AGENT

# Пример использования логгера внутри модуля, если нужно
# logger = setup_logging()
# logger.info("Модуль utils.py загружен.")

# Добавим импорт List и Optional из typing для аннотаций
from typing import List, Optional