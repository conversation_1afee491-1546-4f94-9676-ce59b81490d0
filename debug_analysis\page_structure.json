{"forms": [{"index": 0, "id": "", "class": "no-spinner main-search-form", "action": "https://cpaquebec.ca/en/search/", "method": "get", "elements": [{"index": 0, "tag": "input", "type": "text", "id": "k", "name": "k", "value": "", "placeholder": "Keywords", "className": "", "text": "", "isVisible": true, "position": {"x": -234, "y": 716, "width": 218, "height": 31}, "attributes": [{"name": "name", "value": "k"}, {"name": "type", "value": "text"}, {"name": "id", "value": "k"}, {"name": "maxlength", "value": "250"}, {"name": "placeholder", "value": "Keywords"}, {"name": "style", "value": ""}]}, {"index": 1, "tag": "button", "type": "submit", "id": "", "name": "", "value": "", "placeholder": "", "className": "", "text": "Search", "isVisible": true, "position": {"x": -187, "y": 763, "width": 124, "height": 50}, "attributes": [{"name": "type", "value": "submit"}]}]}, {"index": 1, "id": "", "class": "no-spinner main-search-form", "action": "https://cpaquebec.ca/en/search/", "method": "get", "elements": [{"index": 0, "tag": "input", "type": "text", "id": "k", "name": "k", "value": "", "placeholder": "Keywords", "className": "", "text": "", "isVisible": true, "position": {"x": 320, "y": 58, "width": 480, "height": 52}, "attributes": [{"name": "name", "value": "k"}, {"name": "type", "value": "text"}, {"name": "id", "value": "k"}, {"name": "maxlength", "value": "250"}, {"name": "placeholder", "value": "Keywords"}, {"name": "style", "value": ""}]}, {"index": 1, "tag": "button", "type": "submit", "id": "", "name": "", "value": "", "placeholder": "", "className": "", "text": "Search", "isVisible": true, "position": {"x": 800, "y": 58, "width": 160, "height": 52}, "attributes": [{"name": "type", "value": "submit"}]}]}, {"index": 2, "id": "FindACPABottinForm", "class": "", "action": "https://cpaquebec.ca/api/sitecore/FindACPA/FindACPABottinFormSubmit?Length=8", "method": "post", "elements": [{"index": 0, "tag": "input", "type": "hidden", "id": "Action", "name": "Action", "value": "", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "data-webform-action", "value": ""}, {"name": "id", "value": "Action"}, {"name": "name", "value": "Action"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": ""}, {"name": "style", "value": ""}]}, {"index": 1, "tag": "input", "type": "hidden", "id": "ActionParams", "name": "ActionParams", "value": "", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "data-webform-action-params", "value": ""}, {"name": "id", "value": "ActionParams"}, {"name": "name", "value": "ActionParams"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": ""}, {"name": "style", "value": ""}]}, {"index": 2, "tag": "input", "type": "hidden", "id": "CriteresRechercheOrinal", "name": "CriteresRechercheOrinal", "value": "", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "CriteresRechercheOrinal"}, {"name": "name", "value": "CriteresRechercheOrinal"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": ""}, {"name": "style", "value": ""}]}, {"index": 3, "tag": "input", "type": "hidden", "id": "PageNumber", "name": "PageNumber", "value": "0", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "PageNumber"}, {"name": "name", "value": "PageNumber"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "0"}, {"name": "style", "value": ""}]}, {"index": 4, "tag": "input", "type": "hidden", "id": "AfficherResultatMap", "name": "AfficherResultatMap", "value": "False", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "AfficherResultatMap"}, {"name": "name", "value": "AfficherResultatMap"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "False"}, {"name": "style", "value": ""}]}, {"index": 5, "tag": "input", "type": "text", "id": "Nom", "name": "Nom", "value": "", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 361, "y": 796, "width": 129, "height": 31}, "attributes": [{"name": "id", "value": "Nom"}, {"name": "name", "value": "Nom"}, {"name": "type", "value": "text"}, {"name": "value", "value": ""}, {"name": "style", "value": ""}]}, {"index": 6, "tag": "input", "type": "text", "id": "Prenom", "name": "Prenom", "value": "", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 502, "y": 796, "width": 129, "height": 31}, "attributes": [{"name": "id", "value": "Prenom"}, {"name": "name", "value": "Prenom"}, {"name": "type", "value": "text"}, {"name": "value", "value": ""}, {"name": "style", "value": ""}]}, {"index": 7, "tag": "input", "type": "text", "id": "AutoCompletionField", "name": "Ville", "value": "", "placeholder": "", "className": "ui-autocomplete-input", "text": "", "isVisible": true, "position": {"x": 642, "y": 780, "width": 269, "height": 31}, "attributes": [{"name": "id", "value": "AutoCompletionField"}, {"name": "name", "value": "Ville"}, {"name": "type", "value": "text"}, {"name": "value", "value": ""}, {"name": "class", "value": "ui-autocomplete-input"}, {"name": "autocomplete", "value": "off"}, {"name": "style", "value": ""}]}, {"index": 8, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_0__Selected", "name": "ListeClienteleDesserviesLeftColumn[0].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 362, "y": 884, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_0__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[0].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 9, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesLeftColumn[0].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesLeftColumn[0].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 10, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_0__Desc", "name": "ListeClienteleDesserviesLeftColumn[0].Desc", "value": "Individuals", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_0__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[0].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Individuals"}, {"name": "style", "value": ""}]}, {"index": 11, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_0__NoRepertoire", "name": "ListeClienteleDesserviesLeftColumn[0].NoRepertoire", "value": "1878", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_0__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[0].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1878"}, {"name": "style", "value": ""}]}, {"index": 12, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_1__Selected", "name": "ListeClienteleDesserviesLeftColumn[1].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 362, "y": 904, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_1__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[1].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 13, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesLeftColumn[1].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesLeftColumn[1].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 14, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_1__Desc", "name": "ListeClienteleDesserviesLeftColumn[1].Desc", "value": "Large companies", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_1__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[1].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Large companies"}, {"name": "style", "value": ""}]}, {"index": 15, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_1__NoRepertoire", "name": "ListeClienteleDesserviesLeftColumn[1].NoRepertoire", "value": "1877", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_1__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[1].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1877"}, {"name": "style", "value": ""}]}, {"index": 16, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_2__Selected", "name": "ListeClienteleDesserviesLeftColumn[2].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 362, "y": 924, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_2__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[2].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 17, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesLeftColumn[2].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesLeftColumn[2].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 18, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_2__Desc", "name": "ListeClienteleDesserviesLeftColumn[2].Desc", "value": "NFPOs", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_2__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[2].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "NFPOs"}, {"name": "style", "value": ""}]}, {"index": 19, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_2__NoRepertoire", "name": "ListeClienteleDesserviesLeftColumn[2].NoRepertoire", "value": "1873", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_2__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[2].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1873"}, {"name": "style", "value": ""}]}, {"index": 20, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_3__Selected", "name": "ListeClienteleDesserviesLeftColumn[3].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 362, "y": 944, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_3__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[3].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 21, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesLeftColumn[3].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesLeftColumn[3].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 22, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_3__Desc", "name": "ListeClienteleDesserviesLeftColumn[3].Desc", "value": "Professional firms", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_3__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[3].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Professional firms"}, {"name": "style", "value": ""}]}, {"index": 23, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_3__NoRepertoire", "name": "ListeClienteleDesserviesLeftColumn[3].NoRepertoire", "value": "1871", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_3__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[3].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1871"}, {"name": "style", "value": ""}]}, {"index": 24, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_4__Selected", "name": "ListeClienteleDesserviesLeftColumn[4].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 362, "y": 964, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_4__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[4].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 25, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesLeftColumn[4].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesLeftColumn[4].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 26, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_4__Desc", "name": "ListeClienteleDesserviesLeftColumn[4].Desc", "value": "Public corporations", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_4__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[4].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Public corporations"}, {"name": "style", "value": ""}]}, {"index": 27, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesLeftColumn_4__NoRepertoire", "name": "ListeClienteleDesserviesLeftColumn[4].NoRepertoire", "value": "1879", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_4__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[4].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1879"}, {"name": "style", "value": ""}]}, {"index": 28, "tag": "input", "type": "checkbox", "id": "acceptClients", "name": "ChckAcceptClients", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 362, "y": 1004, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "acceptClients"}, {"name": "name", "value": "ChckAcceptClients"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 29, "tag": "input", "type": "hidden", "id": "", "name": "ChckAcceptClients", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ChckAcceptClients"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 30, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_0__Selected", "name": "ListeClienteleDesserviesRightColumn[0].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 643, "y": 884, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_0__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[0].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 31, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesRightColumn[0].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesRightColumn[0].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 32, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_0__Desc", "name": "ListeClienteleDesserviesRightColumn[0].Desc", "value": "Retailers", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_0__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[0].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Retailers"}, {"name": "style", "value": ""}]}, {"index": 33, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_0__NoRepertoire", "name": "ListeClienteleDesserviesRightColumn[0].NoRepertoire", "value": "1876", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_0__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[0].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1876"}, {"name": "style", "value": ""}]}, {"index": 34, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_1__Selected", "name": "ListeClienteleDesserviesRightColumn[1].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 643, "y": 904, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_1__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[1].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 35, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesRightColumn[1].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesRightColumn[1].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 36, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_1__Desc", "name": "ListeClienteleDesserviesRightColumn[1].Desc", "value": "Self-employed workers", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_1__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[1].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Self-employed workers"}, {"name": "style", "value": ""}]}, {"index": 37, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_1__NoRepertoire", "name": "ListeClienteleDesserviesRightColumn[1].NoRepertoire", "value": "1880", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_1__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[1].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1880"}, {"name": "style", "value": ""}]}, {"index": 38, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_2__Selected", "name": "ListeClienteleDesserviesRightColumn[2].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 643, "y": 924, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_2__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[2].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 39, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesRightColumn[2].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesRightColumn[2].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 40, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_2__Desc", "name": "ListeClienteleDesserviesRightColumn[2].Desc", "value": "SMEs", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_2__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[2].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "SMEs"}, {"name": "style", "value": ""}]}, {"index": 41, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_2__NoRepertoire", "name": "ListeClienteleDesserviesRightColumn[2].NoRepertoire", "value": "1874", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_2__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[2].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1874"}, {"name": "style", "value": ""}]}, {"index": 42, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_3__Selected", "name": "ListeClienteleDesserviesRightColumn[3].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 643, "y": 944, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_3__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[3].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 43, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesRightColumn[3].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesRightColumn[3].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 44, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_3__Desc", "name": "ListeClienteleDesserviesRightColumn[3].Desc", "value": "Start-ups", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_3__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[3].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Start-ups"}, {"name": "style", "value": ""}]}, {"index": 45, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_3__NoRepertoire", "name": "ListeClienteleDesserviesRightColumn[3].NoRepertoire", "value": "1872", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_3__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[3].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1872"}, {"name": "style", "value": ""}]}, {"index": 46, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_4__Selected", "name": "ListeClienteleDesserviesRightColumn[4].Selected", "value": "true", "placeholder": "", "className": "", "text": "", "isVisible": true, "position": {"x": 643, "y": 964, "width": 13, "height": 13}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_4__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[4].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"index": 47, "tag": "input", "type": "hidden", "id": "", "name": "ListeClienteleDesserviesRightColumn[4].Selected", "value": "false", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "name", "value": "ListeClienteleDesserviesRightColumn[4].Selected"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "false"}, {"name": "style", "value": ""}]}, {"index": 48, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_4__Desc", "name": "ListeClienteleDesserviesRightColumn[4].Desc", "value": "Syndicates of co-owners", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_4__Desc"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[4].Desc"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "Syndicates of co-owners"}, {"name": "style", "value": ""}]}, {"index": 49, "tag": "input", "type": "hidden", "id": "ListeClienteleDesserviesRightColumn_4__NoRepertoire", "name": "ListeClienteleDesserviesRightColumn[4].NoRepertoire", "value": "1875", "placeholder": "", "className": "", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_4__NoRepertoire"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[4].NoRepertoire"}, {"name": "type", "value": "hidden"}, {"name": "value", "value": "1875"}, {"name": "style", "value": ""}]}, {"index": 50, "tag": "textarea", "type": "textarea", "id": "g-recaptcha-response", "name": "g-recaptcha-response", "value": "", "placeholder": "", "className": "g-recaptcha-response", "text": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}, "attributes": [{"name": "id", "value": "g-recaptcha-response"}, {"name": "name", "value": "g-recaptcha-response"}, {"name": "class", "value": "g-recaptcha-response"}, {"name": "style", "value": "width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"}]}]}], "category_elements": [{"selector": "input[type=\"checkbox\"]", "index": 0, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_0__Selected", "name": "ListeClienteleDesserviesLeftColumn[0].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 362, "y": 884, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Individuals"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_0__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[0].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 1, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_1__Selected", "name": "ListeClienteleDesserviesLeftColumn[1].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 362, "y": 904, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Large companies"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_1__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[1].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 2, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_2__Selected", "name": "ListeClienteleDesserviesLeftColumn[2].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 362, "y": 924, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "NFPOs"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_2__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[2].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 3, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_3__Selected", "name": "ListeClienteleDesserviesLeftColumn[3].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 362, "y": 944, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Professional firms"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_3__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[3].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 4, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesLeftColumn_4__Selected", "name": "ListeClienteleDesserviesLeftColumn[4].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 362, "y": 964, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Public corporations"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesLeftColumn_4__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesLeftColumn[4].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 5, "tag": "input", "type": "checkbox", "id": "acceptClients", "name": "ChckAcceptClients", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 362, "y": 1004, "width": 13, "height": 13}, "parent": {"tag": "ul", "id": "", "className": ""}, "siblings": [{"tag": "li", "text": "Individuals"}, {"tag": "li", "text": "Large companies"}, {"tag": "li", "text": "NFPOs"}, {"tag": "li", "text": "Professional firms"}, {"tag": "li", "text": "Public corporations"}, {"tag": "br", "text": ""}, {"tag": "input", "text": ""}, {"tag": "label", "text": "Accepting new clients"}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "acceptClients"}, {"name": "name", "value": "ChckAcceptClients"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 6, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_0__Selected", "name": "ListeClienteleDesserviesRightColumn[0].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 643, "y": 884, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Retailers"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_0__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[0].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 7, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_1__Selected", "name": "ListeClienteleDesserviesRightColumn[1].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 643, "y": 904, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Self-employed workers"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_1__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[1].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 8, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_2__Selected", "name": "ListeClienteleDesserviesRightColumn[2].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 643, "y": 924, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "SMEs"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_2__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[2].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 9, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_3__Selected", "name": "ListeClienteleDesserviesRightColumn[3].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 643, "y": 944, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Start-ups"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_3__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[3].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "input[type=\"checkbox\"]", "index": 10, "tag": "input", "type": "checkbox", "id": "ListeClienteleDesserviesRightColumn_4__Selected", "name": "ListeClienteleDesserviesRightColumn[4].Selected", "value": "true", "className": "", "text": "", "innerHTML": "", "isVisible": true, "position": {"x": 643, "y": 964, "width": 13, "height": 13}, "parent": {"tag": "li", "id": "", "className": ""}, "siblings": [{"tag": "input", "text": ""}, {"tag": "label", "text": "Syndicates of co-owners"}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}, {"tag": "input", "text": ""}], "attributes": [{"name": "id", "value": "ListeClienteleDesserviesRightColumn_4__Selected"}, {"name": "name", "value": "ListeClienteleDesserviesRightColumn[4].Selected"}, {"name": "type", "value": "checkbox"}, {"name": "value", "value": "true"}, {"name": "style", "value": ""}]}, {"selector": "fieldset", "index": 0, "tag": "fieldset", "type": "fieldset", "id": "", "name": "", "value": "", "className": "", "text": "Search criteria\n                \n                \n                    \n                        \n                            \n\t\t                       ", "innerHTML": "\n                <legend>Search criteria</legend>\n                \n                <div class=\"row\">\n                    <div class=\"large-12 column\" data-component=\"form-path\" data-priority-index=\"1\"", "isVisible": true, "position": {"x": 344, "y": 707, "width": 584, "height": 526}, "parent": {"tag": "div", "id": "SectionRecherche", "className": ""}, "siblings": [{"tag": "div", "text": "Whether you need a hand sorting out your personal "}, {"tag": "br", "text": ""}, {"tag": "fieldset", "text": "Search criteria\n                \n                \n"}], "attributes": []}, {"selector": "legend", "index": 0, "tag": "legend", "type": "", "id": "", "name": "", "value": "", "className": "", "text": "Search criteria", "innerHTML": "Search criteria", "isVisible": true, "position": {"x": 344, "y": 707, "width": 584, "height": 37}, "parent": {"tag": "fieldset", "id": "", "className": ""}, "siblings": [{"tag": "legend", "text": "Search criteria"}, {"tag": "div", "text": "Last name of CPA\n\t\t\t                        \n\t\t   "}, {"tag": "hr", "text": ""}, {"tag": "div", "text": "Reset search"}, {"tag": "div", "text": "Search"}], "attributes": []}], "text_analysis": [{"index": 75, "tag": "style", "text": ".gsc-control-cse{font-family:arial, sans-serif}.gsc-control-cse .gsc-table-result{font-family:arial, sans-serif}.gsc-refinementsGradient{background:linear-gradient(to left,rgba(255,255,255,1),rgba(255,255,255,0))}.gsc-control-cse{border-color:#FFFFFF;background-color:#FFFFFF}input.gsc-input,.gsc-input-box,.gsc-input-box-hover,.gsc-input-box-focus{border-color:#D9D9D9}.gsc-search-button-v2,.gsc-search-button-v2:hover,.gsc-search-button-v2:focus{border-color:#2F5BB7;background-color:#4D90FE;background-image:none;filter:none}.gsc-search-button-v2 svg{fill:#FFFFFF}.gsc-tabHeader.gsc-tabhActive,.gsc-refinementHeader.gsc-refinementhActive{color:#1A73E8;border-color:#1A73E8;background-color:#FFFFFF}.gsc-tabHeader.gsc-tabhInactive,.gsc-refinementHeader.gsc-refinementhInactive{color:#666666;border-color:#666666;background-color:#FFFFFF}.gsc-webResult.gsc-result,.gsc-results .gsc-imageResult{border-color:#FFFFFF;background-color:#FFFFFF}.gsc-webResult.gsc-result:hover{border-color:#FFFFFF;background-color:#FFFFFF}.gs-webResult.gs-result a.gs-title:link,.gs-webResult.gs-result a.gs-title:link b,.gs-imageResult a.gs-title:link,.gs-imageResult a.gs-title:link b{color:#1155CC}.gs-webResult.gs-result a.gs-title:visited,.gs-webResult.gs-result a.gs-title:visited b,.gs-imageResult a.gs-title:visited,.gs-imageResult a.gs-title:visited b{color:#1155CC}.gs-webResult.gs-result a.gs-title:hover,.gs-webResult.gs-result a.gs-title:hover b,.gs-imageResult a.gs-title:hover,.gs-imageResult a.gs-title:hover b{color:#1155CC}.gs-webResult.gs-result a.gs-title:active,.gs-webResult.gs-result a.gs-title:active b,.gs-imageResult a.gs-title:active,.gs-imageResult a.gs-title:active b{color:#1155CC}.gsc-cursor-page{color:#1155CC}a.gsc-trailing-more-results:link{color:#1155CC}.gs-webResult:not(.gs-no-results-result):not(.gs-error-result) .gs-snippet,.gs-fileFormatType{color:#333333}.gs-webResult div.gs-visibleUrl{color:#009933}.gs-webResult div.gs-visibleUrl-short{color:#009933}.gsc-cursor-box{border-color:#FFFFFF}.gsc-results .gsc-cursor-box .gsc-cursor-page{border-color:#666666;background-color:#FFFFFF;color:#666666}.gsc-results .gsc-cursor-box .gsc-cursor-current-page{border-color:#1A73E8;background-color:#FFFFFF;color:#1A73E8}.gsc-webResult.gsc-result.gsc-promotion{border-color:#F6F6F6;background-color:#F6F6F6}.gsc-completion-title{color:#1155CC}.gsc-completion-snippet{color:#333333}.gs-promotion a.gs-title:link,.gs-promotion a.gs-title:link *,.gs-promotion .gs-snippet a:link{color:#1155CC}.gs-promotion a.gs-title:visited,.gs-promotion a.gs-title:visited *,.gs-promotion .gs-snippet a:visited{color:#1155CC}.gs-promotion a.gs-title:hover,.gs-promotion a.gs-title:hover *,.gs-promotion .gs-snippet a:hover{color:#1155CC}.gs-promotion a.gs-title:active,.gs-promotion a.gs-title:active *,.gs-promotion .gs-snippet a:active{color:#1155CC}.gs-promotion .gs-snippet,.gs-promotion .gs-title .gs-promotion-title-right,.gs-promotion .gs-title .gs-promotion-title-right *{color:#333333}.gs-promotion .gs-visibleUrl,.gs-promotion .gs-visibleUrl-short{color:#009933}.gcsc-find-more-on-google{color:#1155CC}.gcsc-find-more-on-google-magnifier{fill:#1155CC}", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 78, "tag": "style", "text": "#onetrust-banner-sdk{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}#onetrust-banner-sdk .onetrust-vendors-list-handler{cursor:pointer;color:#1f96db;font-size:inherit;font-weight:700;text-decoration:none;margin-left:5px}#onetrust-banner-sdk .onetrust-vendors-list-handler:hover{color:#1f96db}#onetrust-banner-sdk:focus{outline:2px solid #000;outline-offset:-2px}#onetrust-banner-sdk a:focus{outline:2px solid #000}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{outline-offset:1px}#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo{height:64px;width:64px}#onetrust-banner-sdk .ot-tcf2-vendor-count.ot-text-bold{font-weight:700}#onetrust-banner-sdk .ot-close-icon,#onetrust-pc-sdk .ot-close-icon,#ot-sync-ntfy .ot-close-icon{background-size:contain;background-repeat:no-repeat;background-position:center;height:12px;width:12px}#onetrust-banner-sdk .powered-by-logo,#onetrust-banner-sdk .ot-pc-footer-logo a,#onetrust-pc-sdk .powered-by-logo,#onetrust-pc-sdk .ot-pc-footer-logo a,#ot-sync-ntfy .powered-by-logo,#ot-sync-ntfy .ot-pc-footer-logo a{background-size:contain;background-repeat:no-repeat;background-position:center;height:25px;width:152px;display:block;text-decoration:none;font-size:.75em}#onetrust-banner-sdk .powered-by-logo:hover,#onetrust-banner-sdk .ot-pc-footer-logo a:hover,#onetrust-pc-sdk .powered-by-logo:hover,#onetrust-pc-sdk .ot-pc-footer-logo a:hover,#ot-sync-ntfy .powered-by-logo:hover,#ot-sync-ntfy .ot-pc-footer-logo a:hover{color:#565656}#onetrust-banner-sdk h3 *,#onetrust-banner-sdk h4 *,#onetrust-banner-sdk h6 *,#onetrust-banner-sdk button *,#onetrust-banner-sdk a[data-parent-id] *,#onetrust-pc-sdk h3 *,#onetrust-pc-sdk h4 *,#onetrust-pc-sdk h6 *,#onetrust-pc-sdk button *,#onetrust-pc-sdk a[data-parent-id] *,#ot-sync-ntfy h3 *,#ot-sync-ntfy h4 *,#ot-sync-ntfy h6 *,#ot-sync-ntfy button *,#ot-sync-ntfy a[data-parent-id] *{font-size:inherit;font-weight:inherit;color:inherit}#onetrust-banner-sdk .ot-hide,#onetrust-pc-sdk .ot-hide,#ot-sync-ntfy .ot-hide{display:none!important}#onetrust-banner-sdk button.ot-link-btn:hover,#onetrust-pc-sdk button.ot-link-btn:hover,#ot-sync-ntfy button.ot-link-btn:hover{text-decoration:underline;opacity:1}#onetrust-pc-sdk .ot-sdk-row .ot-sdk-column{padding:0}#onetrust-pc-sdk .ot-sdk-container{padding-right:0}#onetrust-pc-sdk .ot-sdk-row{flex-direction:initial;width:100%}#onetrust-pc-sdk [type=checkbox]:checked,#onetrust-pc-sdk [type=checkbox]:not(:checked){pointer-events:initial}#onetrust-pc-sdk [type=checkbox]:disabled+label::before,#onetrust-pc-sdk [type=checkbox]:disabled+label:after,#onetrust-pc-sdk [type=checkbox]:disabled+label{pointer-events:none;opacity:.7}#onetrust-pc-sdk #vendor-list-content{transform:translate3d(0,0,0)}#onetrust-pc-sdk li input[type=checkbox]{z-index:1}#onetrust-pc-sdk li .ot-checkbox label{z-index:2}#onetrust-pc-sdk li .ot-checkbox input[type=checkbox]{height:auto;width:auto}#onetrust-pc-sdk li .host-title a,#onetrust-pc-sdk li .ot-host-name a,#onetrust-pc-sdk li .accordion-text,#onetrust-pc-sdk li .ot-acc-txt{z-index:2;position:relative}#onetrust-pc-sdk input{margin:3px .1ex}#onetrust-pc-sdk .pc-logo,#onetrust-pc-sdk .ot-pc-logo{height:60px;width:180px;background-position:center;background-size:contain;background-repeat:no-repeat;display:inline-flex;justify-content:center;align-items:center}#onetrust-pc-sdk .pc-logo img,#onetrust-pc-sdk .ot-pc-logo img{max-height:100%;max-width:100%}#onetrust-pc-sdk .screen-reader-only,#onetrust-pc-sdk .ot-scrn-rdr,.ot-sdk-cookie-policy .screen-reader-only,.ot-sdk-cookie-policy .ot-scrn-rdr{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}#onetrust-pc-sdk.ot-fade-in,.onetrust-pc-dark-filter.ot-fade-in,#onetrust-banner-sdk.ot-fade-in{animation-name:onetrust-fade-in;animation-duration:400ms;animation-timing-function:ease-in-out}#onetrust-pc-sdk.ot-hide{display:none!important}.onetrust-pc-dark-filter.ot-hide{display:none!important}#ot-sdk-btn.ot-sdk-show-settings,#ot-sdk-btn.optanon-show-settings{color:#68b631;border:1px solid #68b631;height:auto;white-space:normal;word-wrap:break-word;padding:.8em 2em;font-size:.8em;line-height:1.2;cursor:pointer;-moz-transition:.1s ease;-o-transition:.1s ease;-webkit-transition:1s ease;transition:.1s ease}#ot-sdk-btn.ot-sdk-show-settings:hover,#ot-sdk-btn.optanon-show-settings:hover{color:#fff;background-color:#68b631}.onetrust-pc-dark-filter{background:rgba(0,0,0,.5);z-index:2147483646;width:100%;height:100%;overflow:hidden;position:fixed;top:0;bottom:0;left:0}@keyframes onetrust-fade-in{0%{opacity:0}100%{opacity:1}}.ot-cookie-label{text-decoration:underline}@media only screen and (min-width:426px)and (max-width:896px)and (orientation:landscape){#onetrust-pc-sdk p{font-size:.75em}}#onetrust-banner-sdk .banner-option-input:focus+label{outline:1px solid #000;outline-style:auto}.category-vendors-list-handler+a:focus,.category-vendors-list-handler+a:focus-visible{outline:2px solid #000}#onetrust-pc-sdk .ot-userid-title{margin-top:10px}#onetrust-pc-sdk .ot-userid-title>span,#onetrust-pc-sdk .ot-userid-timestamp>span{font-weight:700}#onetrust-pc-sdk .ot-userid-desc{font-style:italic}#onetrust-pc-sdk .ot-host-desc a{pointer-events:initial}#onetrust-pc-sdk .ot-ven-hdr>p a{position:relative;z-index:2;pointer-events:initial}#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info a,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info a{margin-right:auto}#onetrust-pc-sdk .ot-pc-footer-logo img{width:136px;height:16px}#onetrust-pc-sdk .ot-pur-vdr-count{font-weight:400;font-size:.7rem;padding-top:3px;display:block}#onetrust-banner-sdk .ot-optout-signal,#onetrust-pc-sdk .ot-optout-signal{border:1px solid #32ae88;border-radius:3px;padding:5px;margin-bottom:10px;background-color:#f9fffa;font-size:.85rem;line-height:2}#onetrust-banner-sdk .ot-optout-signal .ot-optout-icon,#onetrust-pc-sdk .ot-optout-signal .ot-optout-icon{display:inline;margin-right:5px}#onetrust-banner-sdk .ot-optout-signal svg,#onetrust-pc-sdk .ot-optout-signal svg{height:20px;width:30px;transform:scale(.5)}#onetrust-banner-sdk .ot-optout-signal svg path,#onetrust-pc-sdk .ot-optout-signal svg path{fill:#32ae88}#onetrust-banner-sdk,#onetrust-pc-sdk,#ot-sdk-cookie-policy,#ot-sync-ntfy{font-size:16px}#onetrust-banner-sdk *,#onetrust-banner-sdk ::after,#onetrust-banner-sdk ::before,#onetrust-pc-sdk *,#onetrust-pc-sdk ::after,#onetrust-pc-sdk ::before,#ot-sdk-cookie-policy *,#ot-sdk-cookie-policy ::after,#ot-sdk-cookie-policy ::before,#ot-sync-ntfy *,#ot-sync-ntfy ::after,#ot-sync-ntfy ::before{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}#onetrust-banner-sdk div,#onetrust-banner-sdk span,#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-banner-sdk p,#onetrust-banner-sdk img,#onetrust-banner-sdk svg,#onetrust-banner-sdk button,#onetrust-banner-sdk section,#onetrust-banner-sdk a,#onetrust-banner-sdk label,#onetrust-banner-sdk input,#onetrust-banner-sdk ul,#onetrust-banner-sdk li,#onetrust-banner-sdk nav,#onetrust-banner-sdk table,#onetrust-banner-sdk thead,#onetrust-banner-sdk tr,#onetrust-banner-sdk td,#onetrust-banner-sdk tbody,#onetrust-banner-sdk .ot-main-content,#onetrust-banner-sdk .ot-toggle,#onetrust-banner-sdk #ot-content,#onetrust-banner-sdk #ot-pc-content,#onetrust-banner-sdk .checkbox,#onetrust-pc-sdk div,#onetrust-pc-sdk span,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#onetrust-pc-sdk p,#onetrust-pc-sdk img,#onetrust-pc-sdk svg,#onetrust-pc-sdk button,#onetrust-pc-sdk section,#onetrust-pc-sdk a,#onetrust-pc-sdk label,#onetrust-pc-sdk input,#onetrust-pc-sdk ul,#onetrust-pc-sdk li,#onetrust-pc-sdk nav,#onetrust-pc-sdk table,#onetrust-pc-sdk thead,#onetrust-pc-sdk tr,#onetrust-pc-sdk td,#onetrust-pc-sdk tbody,#onetrust-pc-sdk .ot-main-content,#onetrust-pc-sdk .ot-toggle,#onetrust-pc-sdk #ot-content,#onetrust-pc-sdk #ot-pc-content,#onetrust-pc-sdk .checkbox,#ot-sdk-cookie-policy div,#ot-sdk-cookie-policy span,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6,#ot-sdk-cookie-policy p,#ot-sdk-cookie-policy img,#ot-sdk-cookie-policy svg,#ot-sdk-cookie-policy button,#ot-sdk-cookie-policy section,#ot-sdk-cookie-policy a,#ot-sdk-cookie-policy label,#ot-sdk-cookie-policy input,#ot-sdk-cookie-policy ul,#ot-sdk-cookie-policy li,#ot-sdk-cookie-policy nav,#ot-sdk-cookie-policy table,#ot-sdk-cookie-policy thead,#ot-sdk-cookie-policy tr,#ot-sdk-cookie-policy td,#ot-sdk-cookie-policy tbody,#ot-sdk-cookie-policy .ot-main-content,#ot-sdk-cookie-policy .ot-toggle,#ot-sdk-cookie-policy #ot-content,#ot-sdk-cookie-policy #ot-pc-content,#ot-sdk-cookie-policy .checkbox,#ot-sync-ntfy div,#ot-sync-ntfy span,#ot-sync-ntfy h1,#ot-sync-ntfy h2,#ot-sync-ntfy h3,#ot-sync-ntfy h4,#ot-sync-ntfy h5,#ot-sync-ntfy h6,#ot-sync-ntfy p,#ot-sync-ntfy img,#ot-sync-ntfy svg,#ot-sync-ntfy button,#ot-sync-ntfy section,#ot-sync-ntfy a,#ot-sync-ntfy label,#ot-sync-ntfy input,#ot-sync-ntfy ul,#ot-sync-ntfy li,#ot-sync-ntfy nav,#ot-sync-ntfy table,#ot-sync-ntfy thead,#ot-sync-ntfy tr,#ot-sync-ntfy td,#ot-sync-ntfy tbody,#ot-sync-ntfy .ot-main-content,#ot-sync-ntfy .ot-toggle,#ot-sync-ntfy #ot-content,#ot-sync-ntfy #ot-pc-content,#ot-sync-ntfy .checkbox{font-family:inherit;font-weight:400;-webkit-font-smoothing:auto;letter-spacing:normal;line-height:normal;padding:0;margin:0;height:auto;min-height:0;max-height:none;width:auto;min-width:0;max-width:none;border-radius:0;border:none;clear:none;float:none;position:static;bottom:auto;left:auto;right:auto;top:auto;text-align:left;text-decoration:none;text-indent:0;text-shadow:none;text-transform:none;white-space:normal;background:0 0;overflow:visible;vertical-align:baseline;visibility:visible;z-index:auto;box-shadow:none}#onetrust-banner-sdk label:before,#onetrust-banner-sdk label:after,#onetrust-banner-sdk .checkbox:after,#onetrust-banner-sdk .checkbox:before,#onetrust-pc-sdk label:before,#onetrust-pc-sdk label:after,#onetrust-pc-sdk .checkbox:after,#onetrust-pc-sdk .checkbox:before,#ot-sdk-cookie-policy label:before,#ot-sdk-cookie-policy label:after,#ot-sdk-cookie-policy .checkbox:after,#ot-sdk-cookie-policy .checkbox:before,#ot-sync-ntfy label:before,#ot-sync-ntfy label:after,#ot-sync-ntfy .checkbox:after,#ot-sync-ntfy .checkbox:before{content:\"\";content:none}#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{position:relative;width:100%;max-width:100%;margin:0 auto;padding:0 20px;box-sizing:border-box}#onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns{width:100%;float:left;box-sizing:border-box;padding:0;display:initial}@media(min-width:400px){#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{width:90%;padding:0}}@media(min-width:550px){#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{width:100%}#onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns{margin-left:4%}#onetrust-banner-sdk .ot-sdk-column:first-child,#onetrust-banner-sdk .ot-sdk-columns:first-child,#onetrust-pc-sdk .ot-sdk-column:first-child,#onetrust-pc-sdk .ot-sdk-columns:first-child,#ot-sdk-cookie-policy .ot-sdk-column:first-child,#ot-sdk-cookie-policy .ot-sdk-columns:first-child{margin-left:0}#onetrust-banner-sdk .ot-sdk-two.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-two.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-two.ot-sdk-columns{width:13.3333333333%}#onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-three.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-three.ot-sdk-columns{width:22%}#onetrust-banner-sdk .ot-sdk-four.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-four.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-four.ot-sdk-columns{width:30.6666666667%}#onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eight.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eight.ot-sdk-columns{width:65.3333333333%}#onetrust-banner-sdk .ot-sdk-nine.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-nine.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-nine.ot-sdk-columns{width:74%}#onetrust-banner-sdk .ot-sdk-ten.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-ten.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-ten.ot-sdk-columns{width:82.6666666667%}#onetrust-banner-sdk .ot-sdk-eleven.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eleven.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eleven.ot-sdk-columns{width:91.3333333333%}#onetrust-banner-sdk .ot-sdk-twelve.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-twelve.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-twelve.ot-sdk-columns{width:100%;margin-left:0}}#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6{margin-top:0;font-weight:600;font-family:inherit}#onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1{font-size:1.5rem;line-height:1.2}#onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2{font-size:1.5rem;line-height:1.25}#onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3{font-size:1.5rem;line-height:1.3}#onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4{font-size:1.5rem;line-height:1.35}#onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5{font-size:1.5rem;line-height:1.5}#onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6{font-size:1.5rem;line-height:1.6}@media(min-width:550px){#onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1{font-size:1.5rem}#onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2{font-size:1.5rem}#onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3{font-size:1.5rem}#onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4{font-size:1.5rem}#onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5{font-size:1.5rem}#onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6{font-size:1.5rem}}#onetrust-banner-sdk p,#onetrust-pc-sdk p,#ot-sdk-cookie-policy p{margin:0 0 1em;font-family:inherit;line-height:normal}#onetrust-banner-sdk a,#onetrust-pc-sdk a,#ot-sdk-cookie-policy a{color:#565656;text-decoration:underline}#onetrust-banner-sdk a:hover,#onetrust-pc-sdk a:hover,#ot-sdk-cookie-policy a:hover{color:#565656;text-decoration:none}#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button{margin-bottom:1rem;font-family:inherit}#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button{display:inline-block;height:38px;padding:0 30px;color:#555;text-align:center;font-size:.9em;font-weight:400;line-height:38px;letter-spacing:.01em;text-decoration:none;white-space:nowrap;background-color:transparent;border-radius:2px;border:1px solid #bbb;cursor:pointer;box-sizing:border-box}#onetrust-banner-sdk .ot-sdk-button:hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#onetrust-pc-sdk .ot-sdk-button:hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#ot-sdk-cookie-policy .ot-sdk-button:hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus{color:#333;border-color:#888;opacity:.7}#onetrust-banner-sdk .ot-sdk-button:focus,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:focus,#onetrust-pc-sdk .ot-sdk-button:focus,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:focus,#ot-sdk-cookie-policy .ot-sdk-button:focus,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:focus{outline:2px solid #000}#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-banner-sdk button.ot-sdk-button-primary,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-pc-sdk button.ot-sdk-button-primary,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary,#ot-sdk-cookie-policy button.ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary{color:#fff;background-color:#33c3f0;border-color:#33c3f0}#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-banner-sdk button.ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-banner-sdk button.ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:focus,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-pc-sdk button.ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-pc-sdk button.ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:focus{color:#fff;background-color:#1eaedb;border-color:#1eaedb}#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text]{height:38px;padding:6px 10px;background-color:#fff;border:1px solid #d1d1d1;border-radius:4px;box-shadow:none;box-sizing:border-box}#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text]{-webkit-appearance:none;-moz-appearance:none;appearance:none}#onetrust-banner-sdk input[type=text]:focus,#onetrust-pc-sdk input[type=text]:focus,#ot-sdk-cookie-policy input[type=text]:focus{border:1px solid #000;outline:0}#onetrust-banner-sdk label,#onetrust-pc-sdk label,#ot-sdk-cookie-policy label{display:block;margin-bottom:.5rem;font-weight:600}#onetrust-banner-sdk input[type=checkbox],#onetrust-pc-sdk input[type=checkbox],#ot-sdk-cookie-policy input[type=checkbox]{display:inline}#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul{list-style:circle inside}#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul{padding-left:0;margin-top:0}#onetrust-banner-sdk ul ul,#onetrust-pc-sdk ul ul,#ot-sdk-cookie-policy ul ul{margin:1.5rem 0 1.5rem 3rem;font-size:90%}#onetrust-banner-sdk li,#onetrust-pc-sdk li,#ot-sdk-cookie-policy li{margin-bottom:1rem}#onetrust-banner-sdk th,#onetrust-banner-sdk td,#onetrust-pc-sdk th,#onetrust-pc-sdk td,#ot-sdk-cookie-policy th,#ot-sdk-cookie-policy td{padding:12px 15px;text-align:left;border-bottom:1px solid #e1e1e1}#onetrust-banner-sdk button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy button{margin-bottom:1rem;font-family:inherit}#onetrust-banner-sdk .ot-sdk-container:after,#onetrust-banner-sdk .ot-sdk-row:after,#onetrust-pc-sdk .ot-sdk-container:after,#onetrust-pc-sdk .ot-sdk-row:after,#ot-sdk-cookie-policy .ot-sdk-container:after,#ot-sdk-cookie-policy .ot-sdk-row:after{content:\"\";display:table;clear:both}#onetrust-banner-sdk .ot-sdk-row,#onetrust-pc-sdk .ot-sdk-row,#ot-sdk-cookie-policy .ot-sdk-row{margin:0;max-width:none;display:block}#onetrust-banner-sdk{box-shadow:0 0 18px rgba(0,0,0,.2)}#onetrust-banner-sdk.otFlat{position:fixed;z-index:2147483645;bottom:0;right:0;left:0;background-color:#fff;max-height:90%;overflow-x:hidden;overflow-y:auto}#onetrust-banner-sdk.otFlat.top{top:0px;bottom:auto}#onetrust-banner-sdk.otRelFont{font-size:1rem}#onetrust-banner-sdk>.ot-sdk-container{overflow:hidden}#onetrust-banner-sdk::-webkit-scrollbar{width:11px}#onetrust-banner-sdk::-webkit-scrollbar-thumb{border-radius:10px;background:#c1c1c1}#onetrust-banner-sdk{scrollbar-arrow-color:#c1c1c1;scrollbar-darkshadow-color:#c1c1c1;scrollbar-face-color:#c1c1c1;scrollbar-shadow-color:#c1c1c1}#onetrust-banner-sdk #onetrust-policy{margin:1.25em 0 .625em 2em;overflow:hidden}#onetrust-banner-sdk #onetrust-policy .ot-gv-list-handler{float:left;font-size:.82em;padding:0;margin-bottom:0;border:0;line-height:normal;height:auto;width:auto}#onetrust-banner-sdk #onetrust-policy-title{font-size:1.2em;line-height:1.3;margin-bottom:10px}#onetrust-banner-sdk #onetrust-policy-text{clear:both;text-align:left;font-size:.88em;line-height:1.4}#onetrust-banner-sdk #onetrust-policy-text *{font-size:inherit;line-height:inherit}#onetrust-banner-sdk #onetrust-policy-text a{font-weight:bold;margin-left:5px}#onetrust-banner-sdk #onetrust-policy-title,#onetrust-banner-sdk #onetrust-policy-text{color:dimgray;float:left}#onetrust-banner-sdk #onetrust-button-group-parent{min-height:1px;text-align:center}#onetrust-banner-sdk #onetrust-button-group{display:inline-block}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{background-color:#68b631;color:#fff;border-color:#68b631;margin-right:1em;min-width:125px;height:auto;white-space:normal;word-break:break-word;word-wrap:break-word;padding:12px 10px;line-height:1.2;font-size:.813em;font-weight:600}#onetrust-banner-sdk #onetrust-pc-btn-handler.cookie-setting-link{background-color:#fff;border:none;color:#68b631;text-decoration:underline;padding-left:0;padding-right:0}#onetrust-banner-sdk .onetrust-close-btn-ui{width:44px;height:44px;background-size:12px;border:none;position:relative;margin:auto;padding:0}#onetrust-banner-sdk .banner_logo{display:none}#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo{position:absolute;top:50%;transform:translateY(-50%);left:0px}#onetrust-banner-sdk.ot-bnr-w-logo #onetrust-policy{margin-left:65px}#onetrust-banner-sdk .ot-b-addl-desc{clear:both;float:left;display:block}#onetrust-banner-sdk #banner-options{float:left;display:table;margin-right:0;margin-left:1em;width:calc(100% - 1em)}#onetrust-banner-sdk .banner-option-input{cursor:pointer;width:auto;height:auto;border:none;padding:0;padding-right:3px;margin:0 0 10px;font-size:.82em;line-height:1.4}#onetrust-banner-sdk .banner-option-input *{pointer-events:none;font-size:inherit;line-height:inherit}#onetrust-banner-sdk .banner-option-input[aria-expanded=true]~.banner-option-details{display:block;height:auto}#onetrust-banner-sdk .banner-option-input[aria-expanded=true] .ot-arrow-container{transform:rotate(90deg)}#onetrust-banner-sdk .banner-option{margin-bottom:12px;margin-left:0;border:none;float:left;padding:0}#onetrust-banner-sdk .banner-option:first-child{padding-left:2px}#onetrust-banner-sdk .banner-option:not(:first-child){padding:0;border:none}#onetrust-banner-sdk .banner-option-header{cursor:pointer;display:inline-block}#onetrust-banner-sdk .banner-option-header :first-child{color:dimgray;font-weight:bold;float:left}#onetrust-banner-sdk .banner-option-header .ot-arrow-container{display:inline-block;border-top:6px solid rgba(0,0,0,0);border-bottom:6px solid rgba(0,0,0,0);border-left:6px solid dimgray;margin-left:10px;vertical-align:middle}#onetrust-banner-sdk .banner-option-details{display:none;font-size:.83em;line-height:1.5;padding:10px 0px 5px 10px;margin-right:10px;height:0px}#onetrust-banner-sdk .banner-option-details *{font-size:inherit;line-height:inherit;color:dimgray}#onetrust-banner-sdk .ot-arrow-container,#onetrust-banner-sdk .banner-option-details{transition:all 300ms ease-in 0s;-webkit-transition:all 300ms ease-in 0s;-moz-transition:all 300ms ease-in 0s;-o-transition:all 300ms ease-in 0s}#onetrust-banner-sdk .ot-dpd-container{float:left}#onetrust-banner-sdk .ot-dpd-title{margin-bottom:10px}#onetrust-banner-sdk .ot-dpd-title,#onetrust-banner-sdk .ot-dpd-desc{font-size:.88em;line-height:1.4;color:dimgray}#onetrust-banner-sdk .ot-dpd-title *,#onetrust-banner-sdk .ot-dpd-desc *{font-size:inherit;line-height:inherit}#onetrust-banner-sdk.ot-iab-2 #onetrust-policy-text *{margin-bottom:0}#onetrust-banner-sdk.ot-iab-2 .onetrust-vendors-list-handler{display:block;margin-left:0;margin-top:5px;clear:both;margin-bottom:0;padding:0;border:0;height:auto;width:auto}#onetrust-banner-sdk.ot-iab-2 #onetrust-button-group button{display:block}#onetrust-banner-sdk.ot-close-btn-link{padding-top:25px}#onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container{top:15px;transform:none;right:15px}#onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container button{padding:0;white-space:pre-wrap;border:none;height:auto;line-height:1.5;text-decoration:underline;font-size:.69em}#onetrust-banner-sdk #onetrust-policy-text,#onetrust-banner-sdk .ot-dpd-desc,#onetrust-banner-sdk .ot-b-addl-desc{font-size:.813em;line-height:1.5}#onetrust-banner-sdk .ot-dpd-desc{margin-bottom:10px}#onetrust-banner-sdk .ot-dpd-desc>.ot-b-addl-desc{margin-top:10px;margin-bottom:10px;font-size:1em}@media only screen and (max-width: 425px){#onetrust-banner-sdk #onetrust-close-btn-container{position:absolute;top:6px;right:2px}#onetrust-banner-sdk #onetrust-policy{margin-left:0;margin-top:3em}#onetrust-banner-sdk #onetrust-button-group{display:block}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{width:100%}#onetrust-banner-sdk .onetrust-close-btn-ui{top:auto;transform:none}#onetrust-banner-sdk #onetrust-policy-title{display:inline;float:none}#onetrust-banner-sdk #banner-options{margin:0;padding:0;width:100%}}@media only screen and (min-width: 426px)and (max-width: 896px){#onetrust-banner-sdk #onetrust-close-btn-container{position:absolute;top:0;right:0}#onetrust-banner-sdk #onetrust-policy{margin-left:1em;margin-right:1em}#onetrust-banner-sdk .onetrust-close-btn-ui{top:10px;right:10px}#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container{width:95%}#onetrust-banner-sdk.ot-iab-2 #onetrust-group-container{width:100%}#onetrust-banner-sdk.ot-bnr-w-logo #onetrust-button-group-parent{padding-left:50px}#onetrust-banner-sdk #onetrust-button-group-parent{width:100%;position:relative;margin-left:0}#onetrust-banner-sdk #onetrust-button-group button{display:inline-block}#onetrust-banner-sdk #onetrust-button-group{margin-right:0;text-align:center}#onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler{float:left}#onetrust-banner-sdk .has-reject-all-button #onetrust-reject-all-handler,#onetrust-banner-sdk .has-reject-all-button #onetrust-accept-btn-handler{float:right}#onetrust-banner-sdk .has-reject-all-button #onetrust-button-group{width:calc(100% - 2em);margin-right:0}#onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler.cookie-setting-link{padding-left:0px;text-align:left}#onetrust-banner-sdk.ot-buttons-fw .ot-sdk-three button{width:100%;text-align:center}#onetrust-banner-sdk.ot-buttons-fw #onetrust-button-group-parent button{float:none}#onetrust-banner-sdk.ot-buttons-fw #onetrust-pc-btn-handler.cookie-setting-link{text-align:center}}@media only screen and (min-width: 550px){#onetrust-banner-sdk .banner-option:not(:first-child){border-left:1px solid #d8d8d8;padding-left:25px}}@media only screen and (min-width: 425px)and (max-width: 550px){#onetrust-banner-sdk.ot-iab-2 #onetrust-button-group,#onetrust-banner-sdk.ot-iab-2 #onetrust-policy,#onetrust-banner-sdk.ot-iab-2 .banner-option{width:100%}}@media only screen and (min-width: 769px){#onetrust-banner-sdk #onetrust-button-group{margin-right:30%}#onetrust-banner-sdk #banner-options{margin-left:2em;margin-right:5em;margin-bottom:1.25em;width:calc(100% - 7em)}}@media only screen and (min-width: 897px)and (max-width: 1023px){#onetrust-banner-sdk.vertical-align-content #onetrust-button-group-parent{position:absolute;top:50%;left:75%;transform:translateY(-50%)}#onetrust-banner-sdk #onetrust-close-btn-container{top:50%;margin:auto;transform:translate(-50%, -50%);position:absolute;padding:0;right:0}#onetrust-banner-sdk #onetrust-close-btn-container button{position:relative;margin:0;right:-22px;top:2px}}@media only screen and (min-width: 1024px){#onetrust-banner-sdk #onetrust-close-btn-container{top:50%;margin:auto;transform:translate(-50%, -50%);position:absolute;right:0}#onetrust-banner-sdk #onetrust-close-btn-container button{right:-12px}#onetrust-banner-sdk #onetrust-policy{margin-left:2em}#onetrust-banner-sdk.vertical-align-content #onetrust-button-group-parent{position:absolute;top:50%;left:60%;transform:translateY(-50%)}#onetrust-banner-sdk .ot-optout-signal{width:50%}#onetrust-banner-sdk.ot-iab-2 #onetrust-policy-title{width:50%}#onetrust-banner-sdk.ot-iab-2 #onetrust-policy-text,#onetrust-banner-sdk.ot-iab-2 :not(.ot-dpd-desc)>.ot-b-addl-desc{margin-bottom:1em;width:50%;border-right:1px solid #d8d8d8;padding-right:1rem}#onetrust-banner-sdk.ot-iab-2 #onetrust-policy-text{margin-bottom:0;padding-bottom:1em}#onetrust-banner-sdk.ot-iab-2 :not(.ot-dpd-desc)>.ot-b-addl-desc{margin-bottom:0;padding-bottom:1em}#onetrust-banner-sdk.ot-iab-2 .ot-dpd-container{width:45%;padding-left:1rem;display:inline-block;float:none}#onetrust-banner-sdk.ot-iab-2 .ot-dpd-title{line-height:1.7}#onetrust-banner-sdk.ot-iab-2 #onetrust-button-group-parent{left:auto;right:4%;margin-left:0}#onetrust-banner-sdk.ot-iab-2 #onetrust-button-group button{display:block}#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-button-group-parent{margin:auto;width:30%}#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container{width:60%}#onetrust-banner-sdk #onetrust-button-group{margin-right:auto}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{margin-top:1em}}@media only screen and (min-width: 890px){#onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group-parent{padding-left:3%;padding-right:4%;margin-left:0}#onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group{margin-right:0;margin-top:1.25em;width:100%}#onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group button{width:100%;margin-bottom:5px;margin-top:5px}#onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group button:last-of-type{margin-bottom:20px}}@media only screen and (min-width: 1280px){#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container{width:55%}#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-button-group-parent{width:44%;padding-left:2%;padding-right:2%}#onetrust-banner-sdk:not(.ot-iab-2).vertical-align-content #onetrust-button-group-parent{position:absolute;left:55%}}\n        #onetrust-consent-sdk #onetrust-banner-sdk {background-color: #FFFFFF;}\n            #onetrust-consent-sdk #onetrust-policy-title,\n                    #onetrust-consent-sdk #onetrust-policy-text,\n                    #onetrust-consent-sdk .ot-b-addl-desc,\n                    #onetrust-consent-sdk .ot-dpd-desc,\n                    #onetrust-consent-sdk .ot-dpd-title,\n                    #onetrust-consent-sdk #onetrust-policy-text *:not(.onetrust-vendors-list-handler),\n                    #onetrust-consent-sdk .ot-dpd-desc *:not(.onetrust-vendors-list-handler),\n                    #onetrust-consent-sdk #onetrust-banner-sdk #banner-options *,\n                    #onetrust-banner-sdk .ot-cat-header,\n                    #onetrust-banner-sdk .ot-optout-signal\n                    {\n                        color: #555555;\n                    }\n            #onetrust-consent-sdk #onetrust-banner-sdk .banner-option-details {\n                    background-color: #FFFFFF;}\n             #onetrust-consent-sdk #onetrust-banner-sdk a[href],\n                    #onetrust-consent-sdk #onetrust-banner-sdk a[href] font,\n                    #onetrust-consent-sdk #onetrust-banner-sdk .ot-link-btn\n                        {\n                            color: #006fba;\n                        }#onetrust-consent-sdk #onetrust-accept-btn-handler,\n                         #onetrust-banner-sdk #onetrust-reject-all-handler {\n                            background-color: #005995;border-color: #005995;\n                color: #FFFFFF;\n            }\n            #onetrust-consent-sdk #onetrust-banner-sdk *:focus,\n            #onetrust-consent-sdk #onetrust-banner-sdk:focus {\n               outline-color: #000000;\n               outline-width: 1px;\n            }\n            #onetrust-consent-sdk #onetrust-pc-btn-handler,\n            #onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {\n                color: #555555; border-color: #555555;\n                background-color:\n                #ffffff;\n            }#onetrust-banner-sdk{\n    margin:10px !important\n    }\n    \n    #onetrust-policy{\n        margin-left:175px !important;\n        }\n        \n        #onetrust-button-group{\n            display: flex !important;\n            flex-flow: column !important;\n            max-width: 80% !important;\n            margin: auto !important;\n        }\n        #onetrust-reject-all-handler {\n        order: 3;\n            }\n            #onetrust-pc-btn-handler {\n        order: 4;\n            }\n            #onetrust-accept-btn-handler {\n        order: 2;\n        margin-bottom: 5px !important;\n            }\n        \n        #cookies-icon{\n        position:absolute !important;\n        left:20px !important;\n        top:60px !important;\n        }\n        \n        #cookies-icon img{\n        width:110px;\n        }\n        \n        .ot-bnr-logo{\n        display:none !important;\n        }\n        \n        #onetrust-group-container{\n        width:60%!important;\n        }\n        \n        #onetrust-button-group-parent{\n        left: 60% !important;\n        width: 40% !important;\n        }\n        \n        \n        @media (min-width: 891px) and (max-width: 1023px) {\n        \n            #onetrust-group-container {\n                width: 54%!important;\n            }\n\n        #onetrust-policy{\n        margin-left:25px !important;\n        }\n    \n        #cookies-icon{\n            position:absolute !important;\n            left:54% !important;\n            top:20px !important;\n            }\n        \n        #onetrust-button-group {\n        display: flex !important;\n            flex-flow: column !important;\n            }\n            #onetrust-reject-all-handler {\n        order: 3;\n            }\n            #onetrust-pc-btn-handler {\n        order: 4;\n            }\n            #onetrust-accept-btn-handler {\n        order: 2;\n            }\n        }\n        \n        \n        @media (max-width: 890px) {\n        \n#onetrust-policy-title{\npadding-right: 25% !important;\n    display: block !important;\n}\n\n#onetrust-policy-text{\npadding-right:10% !important;\n}\n    \n            #onetrust-banner-sdk.ot-bnr-w-logo #onetrust-button-group-parent {\n                padding-left: 0px !important;\n            }\n    \n            #cookies-icon{\n                position:absolute !important;\n                left:auto!important;\n    right:0px !important;\n                top:10px !important;\n                }\n    \n        #onetrust-policy{\n        margin-left:0px !important;\n        padding-top:30px !important;\n        }\n        \n        #onetrust-group-container{\n        width:100%!important;\n        }\n        \n        #onetrust-button-group-parent{\n        left: 0% !important;\n        width: 100% !important;\n        }\n        \n        #onetrust-policy{\n        margin-left:20px !important;\n        }\n        \n        #onetrust-button-group {\n        display: flex !important;\n        max-width:100% !important;\n            flex-flow: column !important;\n            }\n            #onetrust-reject-all-handler {\n        order: 3;\n            }\n            #onetrust-pc-btn-handler {\n        order: 4;\n            }\n            #onetrust-accept-btn-handler {\n        order: 2;\n            }\n        \n        }\n        .ot-sdk-cookie-policy{font-family:inherit;font-size:16px}.ot-sdk-cookie-policy.otRelFont{font-size:1rem}.ot-sdk-cookie-policy h3,.ot-sdk-cookie-policy h4,.ot-sdk-cookie-policy h6,.ot-sdk-cookie-policy p,.ot-sdk-cookie-policy li,.ot-sdk-cookie-policy a,.ot-sdk-cookie-policy th,.ot-sdk-cookie-policy #cookie-policy-description,.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,.ot-sdk-cookie-policy #cookie-policy-title{color:dimgray}.ot-sdk-cookie-policy #cookie-policy-description{margin-bottom:1em}.ot-sdk-cookie-policy h4{font-size:1.2em}.ot-sdk-cookie-policy h6{font-size:1em;margin-top:2em}.ot-sdk-cookie-policy th{min-width:75px}.ot-sdk-cookie-policy a,.ot-sdk-cookie-policy a:hover{background:#fff}.ot-sdk-cookie-policy thead{background-color:#f6f6f4;font-weight:bold}.ot-sdk-cookie-policy .ot-mobile-border{display:none}.ot-sdk-cookie-policy section{margin-bottom:2em}.ot-sdk-cookie-policy table{border-collapse:inherit}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy{font-family:inherit;font-size:1rem}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title{color:dimgray}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description{margin-bottom:1em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup{margin-left:1.5em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group-desc,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-table-header,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td{font-size:.9em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td span,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td a{font-size:inherit}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group{font-size:1em;margin-bottom:.6em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-title{margin-bottom:1.2em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy>section{margin-bottom:1em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th{min-width:75px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a:hover{background:#fff}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead{background-color:#f6f6f4;font-weight:bold}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-mobile-border{display:none}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy section{margin-bottom:2em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li{list-style:disc;margin-left:1.5em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li h4{display:inline-block}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table{border-collapse:inherit;margin:auto;border:1px solid #d7d7d7;border-radius:5px;border-spacing:initial;width:100%;overflow:hidden}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td{border-bottom:1px solid #d7d7d7;border-right:1px solid #d7d7d7}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td{border-bottom:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr th:last-child,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr td:last-child{border-right:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type{width:25%}.ot-sdk-cookie-policy[dir=rtl]{text-align:left}#ot-sdk-cookie-policy h3{font-size:1.5em}@media only screen and (max-width: 530px){.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) table,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tbody,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) th,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr{display:block}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead tr{position:absolute;top:-9999px;left:-9999px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr{margin:0 0 1em 0}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd),.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd) a{background:#f6f6f4}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td{border:none;border-bottom:1px solid #eee;position:relative;padding-left:50%}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before{position:absolute;height:100%;left:6px;width:40%;padding-right:10px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) .ot-mobile-border{display:inline-block;background-color:#e4e4e4;position:absolute;height:100%;top:0;left:45%;width:2px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before{content:attr(data-label);font-weight:bold}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) li{word-break:break-word;word-wrap:break-word}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table{overflow:hidden}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td{border:none;border-bottom:1px solid #d7d7d7}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tbody,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr{display:block}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type{width:auto}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr{margin:0 0 1em 0}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before{height:100%;width:40%;padding-right:10px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before{content:attr(data-label);font-weight:bold}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li{word-break:break-word;word-wrap:break-word}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead tr{position:absolute;top:-9999px;left:-9999px;z-index:-9999}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td{border-bottom:1px solid #d7d7d7;border-right:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td:last-child{border-bottom:0px}}\n                \n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h5,\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description {\n                        color: #808080;\n                    }\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th {\n                        color: #808080;\n                    }\n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group {\n                        color: #808080;\n                    }\n                    \n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title {\n                            color: #808080;\n                        }\n                    \n            \n                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th {\n                            background-color: #F4F4F4;\n                        }\n                    \n            .ot-floating-button__front{background-image:url('https://cdn.cookielaw.org/logos/static/ot_persistent_cookie_icon.png')}\n                      @keyframes slide-down-custom {\n                          0% {\n                              bottom: 800px !important;\n                          }\n                          100% {\n                              bottom: 0px;\n                          }\n                      }\n                      @-webkit-keyframes slide-down-custom {\n                          0% {\n                              bottom: 800px !important;\n                          }\n                          100% {\n                              bottom: 0px;\n                          }\n                      }\n                      @-moz-keyframes slide-down-custom {\n                          0% {\n                              bottom: 800px !important;\n                          }\n                          100% {\n                              bottom: 0px;\n                          }\n                      }\n                      #ot-sdk-btn-floating.ot-floating-button{position:fixed;bottom:10px;opacity:0;width:50px;height:50px;line-height:15px;cursor:pointer;background-color:rgba(0,0,0,0);transition:all 300ms ease;z-index:2147483646;animation:otFloatingBtnIntro 800ms ease 0ms 1 forwards}#ot-sdk-btn-floating.ot-floating-button.ot-hide{display:none}#ot-sdk-btn-floating.ot-floating-button::before,#ot-sdk-btn-floating.ot-floating-button::after{text-transform:none;line-height:1;user-select:none;pointer-events:none;position:absolute;transform:scale(0);opacity:0;transition:all 300ms ease;display:block;height:auto}#ot-sdk-btn-floating.ot-floating-button::before{content:\"\";border:5px solid rgba(0,0,0,0);z-index:1001;top:50%;border-left-width:0;border-right-color:#333;right:calc(0em - 5px);transform:translate(10px, -50%)}#ot-sdk-btn-floating.ot-floating-button::after{content:attr(title);position:absolute;text-align:center;top:50%;left:calc(100% + 5px);transform:translate(10px, -50%);font-size:.75rem;min-width:3em;max-width:21em;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;padding:5px;border-radius:.3ch;box-shadow:0 1em 2em -0.5em rgba(0,0,0,.35);background:#333;color:#fff;z-index:2147483645}#ot-sdk-btn-floating.ot-floating-button:hover::before,#ot-sdk-btn-floating.ot-floating-button:hover::after{opacity:1}#ot-sdk-btn-floating.ot-floating-button:hover::before{transform:translate(0.5em, -50%) scale(1)}#ot-sdk-btn-floating.ot-floating-button:hover::after{transform:translate(0.5em, -50%) scale(1)}#ot-sdk-btn-floating.ot-floating-button.ot-pc-open .ot-floating-button__front{transform:rotateY(-180deg)}#ot-sdk-btn-floating.ot-floating-button.ot-pc-open .ot-floating-button__back{transform:rotateY(0)}#ot-sdk-btn-floating .ot-floating-button__front,#ot-sdk-btn-floating .ot-floating-button__back{position:absolute;width:100%;height:100%;-webkit-backface-visibility:hidden;backface-visibility:hidden;background-color:#6aaae4;border-radius:10px;box-shadow:0 4px 8px 0 rgba(0,0,0,.2);transition:transform .6s;transform-style:preserve-3d}#ot-sdk-btn-floating .ot-floating-button__front{background-color:#6aaae4;transform:rotateY(0)}#ot-sdk-btn-floating .ot-floating-button__front.custom-persistent-icon{background-position:center center;background-repeat:no-repeat;background-size:100%;border-radius:100px}#ot-sdk-btn-floating .ot-floating-button__front svg{width:30px;height:37px}#ot-sdk-btn-floating .ot-floating-button__back{background-color:#69c;transform:rotateY(-180deg)}#ot-sdk-btn-floating .ot-floating-button__back.custom-persistent-icon{background-position:center center;background-repeat:no-repeat;background-size:100%;border-radius:100px}#ot-sdk-btn-floating .ot-floating-button__back svg{width:24px;height:24px}#ot-sdk-btn-floating.ot-floating-button button{padding:0;background-color:rgba(0,0,0,0);border:0;width:100%;height:100%;cursor:pointer}@keyframes otFloatingBtnIntro{0%{opacity:0;left:-75px}100%{opacity:1;left:1%}}@keyframes otFloatingBtnImageIntro{0%{opacity:0;transform:scale(0) rotate(-270deg)}100%{opacity:100%;transform:scale(0.95) rotate(0deg)}}", "className": "", "id": "onetrust-style", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 716, "tag": "script", "text": "(function() {\n                        var cx = '001554683051376136176:awc0yq1capc';\n                        var gcse = document.createElement('script');\n                        gcse.type = 'text/javascript';\n                        gcse.async = true;\n                        gcse.src = 'https://cse.google.com/cse.js?cx=' + cx;\n                        var s = document.getElementsByTagName('script')[0];\n                        s.parentNode.insertBefore(gcse, s);\n                    })();", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 1429, "tag": "strong", "text": "Clients served", "className": "", "id": "", "isVisible": true, "position": {"x": 361, "y": 845, "width": 106, "height": 16}}, {"index": 1434, "tag": "label", "text": "Individuals", "className": "", "id": "", "isVisible": true, "position": {"x": 380, "y": 882, "width": 79, "height": 16}}, {"index": 1440, "tag": "label", "text": "Large companies", "className": "", "id": "", "isVisible": true, "position": {"x": 380, "y": 902, "width": 126, "height": 16}}, {"index": 1446, "tag": "label", "text": "NFPOs", "className": "", "id": "", "isVisible": true, "position": {"x": 380, "y": 922, "width": 51, "height": 16}}, {"index": 1452, "tag": "label", "text": "Professional firms", "className": "", "id": "", "isVisible": true, "position": {"x": 380, "y": 942, "width": 131, "height": 16}}, {"index": 1458, "tag": "label", "text": "Public corporations", "className": "", "id": "", "isVisible": true, "position": {"x": 380, "y": 962, "width": 143, "height": 16}}, {"index": 1472, "tag": "label", "text": "Retailers", "className": "", "id": "", "isVisible": true, "position": {"x": 661, "y": 882, "width": 64, "height": 16}}, {"index": 1478, "tag": "label", "text": "Self-employed workers", "className": "", "id": "", "isVisible": true, "position": {"x": 661, "y": 902, "width": 169, "height": 16}}, {"index": 1484, "tag": "label", "text": "SMEs", "className": "", "id": "", "isVisible": true, "position": {"x": 661, "y": 922, "width": 39, "height": 16}}, {"index": 1490, "tag": "label", "text": "Start-ups", "className": "", "id": "", "isVisible": true, "position": {"x": 661, "y": 942, "width": 68, "height": 16}}, {"index": 1496, "tag": "label", "text": "Syndicates of co-owners", "className": "", "id": "", "isVisible": true, "position": {"x": 661, "y": 962, "width": 181, "height": 16}}, {"index": 1516, "tag": "script", "text": "$(function () {\n\n        var afficherResultatsLoaded = false;\n        if (document.getElementById('AfficherResultatsListContainer') !== null && document.getElementById('AfficherResultatsListStaticContainer') !== null\n            && document.getElementById('AfficherResultatsMapContainer') !== null && document.getElementById('AfficherResultatsMapStaticContainer') !== null) {\n            afficherResultatsLoaded = true;\n        }\n\n        if (document.getElementById('map') == null && afficherResultatsLoaded) {\n            document.getElementById('AfficherResultatsListContainer').style.display = 'none';\n            document.getElementById('AfficherResultatsListStaticContainer').style.display = 'contents';\n            document.getElementById('AfficherResultatsMapContainer').style.display = 'contents';\n            document.getElementById('AfficherResultatsMapStaticContainer').style.display = 'none';\n        }\n        else if (afficherResultatsLoaded) {\n            document.getElementById('AfficherResultatsListContainer').style.display = 'contents';\n            document.getElementById('AfficherResultatsListStaticContainer').style.display = 'none';\n            document.getElementById('AfficherResultatsMapContainer').style.display = 'none';\n            document.getElementById('AfficherResultatsMapStaticContainer').style.display = 'contents';\n        }\n        setTimeout(function () {\n            if (document.getElementsByClassName('pagination').length > 0) {\n                window.scrollTo(0, document.getElementById(\"SectionResultats\").offsetTop);\n            }\n        }, 0);\n    });\n\n    $(function () {\n        $(\"#AutoCompletionField\").autocomplete({\n            source: function (request, response) {\n                $.ajax({\n                    url: \"/api/sitecore/FindACPA/AutoCompleteVille\",\n                    type: \"POST\",\n                    dataType: \"json\",\n                    data: {\n                        keyword: request.term\n                    },\n\n                    success: function (data) {\n\n                        response($.map(data, function (item) {\n\n                            return { label: item.Name, value: item.Name };\n\n                        }));\n                    }\n                });\n            }\n        });\n    })", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 1517, "tag": "script", "text": "jQuery(function ($) {\n        // Load API Async\n        var script = document.createElement('script');\n        script.src = \"https://maps.googleapis.com/maps/api/js?key=\"+ \"AIzaSyBLu9PkA_ky9yneupVFsxajU_GLn5Hpmv8\" + \"&language=\" + \"en\" +\"&region=CA&callback=initMap&loading=async\";\n        document.body.appendChild(script);\n    });\n\n    //Geolocation multiple locations\n    function initMap() {\n\n        // Declarer la liste des positions\n        var locations = [];\n\n        var result = [];\n        if (result == null) {\n            return;\n        }\n\n        var i = 0;\n        for (i = 0; i < result.length; i++) {\n            var member = result[i];\n            var memberInfo = member.PrenomMembre + ' ' + member.NomClient + \", \" + member.TitreCompMembre;\n\n            //Ajouter la position a la liste\n            locations.push([memberInfo,\n                member.DetailPageUrl,\n                member.NomEmployeur,\n                member.Latitude,\n                member.Longitude,\n                member.MemberSpecificMessages]);\n        }\n\n        if (document.getElementById('map') == null) {\n            return;\n        }\n\n        var map = new google.maps.Map(document.getElementById('map'),\n            {\n                zoom: 4,\n                center: new google.maps.LatLng(45.5087, -73.583333),\n                mapTypeId: google.maps.MapTypeId.ROADMAP,\n            });\n\n        var infowindow = new google.maps.InfoWindow({});\n\n        var j;\n        var markers = [];\n        var clusterer = new markerClusterer.MarkerClusterer({\n            map,\n            algorithmOptions: {\n                maxZoom: 8\n            }\n        });\n\n        // Creation d'un nouveau marker pour chaque position.\n        for (j = 0; j < locations.length; j++) {\n            if(locations[j][2] == 0.00 || locations[j][3] == 0.00) continue;\n\n            var marker = new google.maps.Marker({\n                position: new google.maps.LatLng(locations[j][3], locations[j][4]),\n                map: map,\n                title : locations[j][0]\n            });\n\n            // Ajouter le marker a la liste\n            markers.push(marker);\n            clusterer.addMarker(marker);\n\n            // Ajouter l'evenement du click sur chaque position.\n            google.maps.event.addListener(\n                marker,\n                'click',\n                (function (marker, j) {\n                    return function () {\n                        var htmlPopUp = \"<div class=\\\"panel\\\">\";\n                        htmlPopUp += \"<a href=\\\"\" + locations[j][1] + \"\\\">\" + locations[j][0] + \"</a>\";\n                        if (locations[j][2] !== \"undefined\" && locations[j][2] !== null) {\n                            htmlPopUp += \"<p>\" + locations[j][2] + \"</p>\";\n                        }\n                        if (locations[j][5] !== \"undefined\" && locations[j][5] !== null) {\n                            htmlPopUp += \"<span class=\\\"small\\\">\" + locations[j][5] + \"</span>\";\n                        }\n                        htmlPopUp += \"</div>\";\n                        infowindow.setContent(htmlPopUp);\n                        infowindow.open(map, marker);\n                    }\n                })(marker, j));\n        }\n\n        map.fitBounds(bounds);\n        var listener = google.maps.event.addListener(map, \"bounds_changed\", function() {\n            if (map.getZoom() > 16) map.setZoom(16);\n            google.maps.event.removeListener(listener);\n        });\n    }", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 1564, "tag": "style", "text": ".nav-search-panel{\nz-index:9999 !important;\n}\n\n.nav-primary-panel {\nbox-shadow: 0px 15px 20px 0px #00000045;\n}\n\n.nav-primary-panel > ul:first-of-type{\npadding-top:20px !important;\n}\n\n.nav-primary-panel div:last-child ul li a{\nfont-size: .8625rem !important;\ntext-transform: none !important;\nline-height: 1.15rem !important;\n}\n\n/* .nav-primary-panel div:last-child ul:before\n{content:\"QUICK LINKS\";\ndisplay: block;\nmargin-bottom: 10pt;\nfont-weight:bold;\n} */", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 1593, "tag": "script", "text": "!function(d,g,e){d.TiktokAnalyticsObject=e;var a=d[e]=d[e]||[];a.methods=\"page track identify instances debug on off once ready alias group enableCookie disableCookie holdConsent revokeConsent grantConsent\".split(\" \");a.setAndDefer=function(b,c){b[c]=function(){b.push([c].concat(Array.prototype.slice.call(arguments,0)))}};for(d=0;d<a.methods.length;d++)a.setAndDefer(a,a.methods[d]);a.instance=function(b){b=a._i[b]||[];for(var c=0;c<a.methods.length;c++)a.setAndDefer(b,a.methods[c]);return b};a.load=\nfunction(b,c){var f=\"https://analytics.tiktok.com/i18n/pixel/events.js\";a._i=a._i||{};a._i[b]=[];a._i[b]._u=f;a._t=a._t||{};a._t[b]=+new Date;a._o=a._o||{};a._o[b]=c||{};c=document.createElement(\"script\");c.type=\"text/javascript\";c.async=!0;c.src=f+\"?sdkid\\x3d\"+b+\"\\x26lib\\x3d\"+e;b=document.getElementsByTagName(\"script\")[0];b.parentNode.insertBefore(c,b)};a.load(\"CU802ARC77UDT30C5VB0\");a.page()}(window,document,\"ttq\");", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 1647, "tag": "script", "text": "String.prototype.startsWith||(String.prototype.startsWith=function(b,a){a=a||0;return this.indexOf(b,a)===a});if(document.URL.startsWith(\"https://cpaquebec.ca/fr/recherche/?\")||document.URL.startsWith(\"https://cpaquebec.ca/en/search/?\")){var elementTriDate=document.querySelector(\"span \\x3e a\");elementTriDate.parentNode.removeChild(elementTriDate)};", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}, {"index": 1648, "tag": "script", "text": "String.prototype.startsWith||(String.prototype.startsWith=function(b,a){a=a||0;return this.indexOf(b,a)===a});if(document.URL.startsWith(\"https://cpaquebec.ca/fr/recherche/?\")||document.URL.startsWith(\"https://cpaquebec.ca/en/search/?\"))for(var elemDate=document.querySelectorAll(\"time\"),i=0;elemDate.length;i++)elemDate[i].parentNode.parentNode.parentNode.removeChild(elemDate[i].parentNode.parentNode);", "className": "", "id": "", "isVisible": false, "position": {"x": 0, "y": 0, "width": 0, "height": 0}}]}