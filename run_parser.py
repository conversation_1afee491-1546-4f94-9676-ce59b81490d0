#!/usr/bin/env python3
import sys
import os
from cpa_quebec.selenium_parser.orchestrator import CpaQuebecParser

def main():
    with CpaQuebecParser(
        debug=True,
        headless=False,
        output_dir="output",
        wait_timeout=20,
        page_load_timeout=90,
        element_timeout=20,
        solve_captcha=True
    ) as parser:
        parser.parse(
            by_category=True,
            category_to_parse="Individuals"
        )

if __name__ == "__main__":
    main()
