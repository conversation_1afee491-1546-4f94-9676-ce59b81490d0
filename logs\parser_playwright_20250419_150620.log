2025-04-19 15:06:20,208 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_150620.log
2025-04-19 15:06:20,211 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 15:06:20,211 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-19 15:06:20,212 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 15:06:20,212 - C<PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-19 15:06:20,212 - CpaQuebecParser - INFO - ==================================================
2025-04-19 15:06:20,212 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-19 15:06:31,626 - CpaQuebecParser - DEBUG - Пауза на 1.23 сек. (после driver.get)
2025-04-19 15:06:32,983 - CpaQuebecParser - DEBUG - Пауза на 0.36 сек. (после scroll к кнопке cookie)
2025-04-19 15:07:15,058 - CpaQuebecParser - DEBUG - Пауза на 0.94 сек. (после клика Reset (XPath))
2025-04-19 15:07:17,763 - CpaQuebecParser - DEBUG - Пауза на 0.55 сек. (после очистки формы)
2025-04-19 15:07:28,373 - CpaQuebecParser - DEBUG - Пауза на 3.10 сек. (между категориями, после Individuals)
2025-04-19 15:07:36,105 - CpaQuebecParser - DEBUG - Пауза на 1.67 сек. (после driver.get)
2025-04-19 15:10:19,396 - CpaQuebecParser - DEBUG - Пауза на 0.79 сек. (после очистки формы)
2025-04-19 15:10:20,208 - CpaQuebecParser - DEBUG - Пауза на 3.98 сек. (между категориями, после Large companies)
2025-04-19 15:10:24,203 - CpaQuebecParser - DEBUG - Пауза на 2.54 сек. (перед retry 2 для категории NFPOs)
2025-04-19 15:10:26,766 - CpaQuebecParser - DEBUG - Пауза на 5.21 сек. (перед retry 3 для категории NFPOs)
2025-04-19 15:10:31,996 - CpaQuebecParser - DEBUG - Пауза на 3.13 сек. (между категориями, после NFPOs)
2025-04-19 15:10:35,147 - CpaQuebecParser - DEBUG - Пауза на 2.02 сек. (перед retry 2 для категории Professional firms)
2025-04-19 15:10:37,180 - CpaQuebecParser - DEBUG - Пауза на 5.99 сек. (перед retry 3 для категории Professional firms)
2025-04-19 15:10:43,195 - CpaQuebecParser - DEBUG - Пауза на 3.66 сек. (между категориями, после Professional firms)
2025-04-19 15:10:46,881 - CpaQuebecParser - DEBUG - Пауза на 2.15 сек. (перед retry 2 для категории Public corporations)
2025-04-19 15:10:49,059 - CpaQuebecParser - DEBUG - Пауза на 4.29 сек. (перед retry 3 для категории Public corporations)
2025-04-19 15:10:53,371 - CpaQuebecParser - DEBUG - Пауза на 2.34 сек. (между категориями, после Public corporations)
2025-04-19 15:10:55,728 - CpaQuebecParser - DEBUG - Пауза на 2.05 сек. (перед retry 2 для категории Retailers)
2025-04-19 15:10:57,808 - CpaQuebecParser - DEBUG - Пауза на 5.98 сек. (перед retry 3 для категории Retailers)
2025-04-19 15:11:03,799 - CpaQuebecParser - DEBUG - Пауза на 2.12 сек. (между категориями, после Retailers)
2025-04-19 15:11:05,940 - CpaQuebecParser - DEBUG - Пауза на 2.84 сек. (перед retry 2 для категории Self-employed workers)
2025-04-19 15:11:08,800 - CpaQuebecParser - DEBUG - Пауза на 4.43 сек. (перед retry 3 для категории Self-employed workers)
2025-04-19 15:11:13,249 - CpaQuebecParser - DEBUG - Пауза на 3.59 сек. (между категориями, после Self-employed workers)
2025-04-19 15:11:16,854 - CpaQuebecParser - DEBUG - Пауза на 2.19 сек. (перед retry 2 для категории SMEs)
2025-04-19 15:11:19,055 - CpaQuebecParser - DEBUG - Пауза на 4.97 сек. (перед retry 3 для категории SMEs)
2025-04-19 15:11:24,038 - CpaQuebecParser - DEBUG - Пауза на 3.30 сек. (между категориями, после SMEs)
2025-04-19 15:11:27,356 - CpaQuebecParser - DEBUG - Пауза на 2.59 сек. (перед retry 2 для категории Start-ups)
2025-04-19 15:11:29,974 - CpaQuebecParser - DEBUG - Пауза на 4.50 сек. (перед retry 3 для категории Start-ups)
2025-04-19 15:11:34,488 - CpaQuebecParser - DEBUG - Пауза на 4.77 сек. (между категориями, после Start-ups)
2025-04-19 15:11:39,272 - CpaQuebecParser - DEBUG - Пауза на 2.58 сек. (перед retry 2 для категории Syndicates of co-owners)
2025-04-19 15:11:41,864 - CpaQuebecParser - DEBUG - Пауза на 4.59 сек. (перед retry 3 для категории Syndicates of co-owners)
2025-04-19 15:11:46,468 - CpaQuebecParser - DEBUG - Пауза на 4.25 сек. (между категориями, после Syndicates of co-owners)
2025-04-19 15:11:50,723 - CpaQuebecParser - INFO - Парсинг (Selenium) завершен. Найдено и сохранено записей: 0
2025-04-19 15:11:51,103 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 0
