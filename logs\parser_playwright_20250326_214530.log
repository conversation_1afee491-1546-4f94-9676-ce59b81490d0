2025-03-26 21:45:30,820 - C<PERSON>QuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_214530.log
2025-03-26 21:45:30,822 - C<PERSON>Q<PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-03-26 21:45:30,823 - CpaQuebecParser - INFO - ==================================================
2025-03-26 21:45:30,823 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-03-26 21:45:30,823 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 30, 'page_timeout': 60, 'get_details': True, 'by_category': True}
2025-03-26 21:45:30,823 - CpaQuebecParser - INFO - ==================================================
2025-03-26 21:45:30,823 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-03-26 21:45:30,824 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_214530.log
2025-03-26 21:45:30,824 - CpaQuebecParser - INFO - Инициализация парсера CPA Quebec (Playwright)
2025-03-26 21:45:30,825 - CpaQuebecParser - WARNING - API ключ для 2Captcha не найден. Решение капчи будет пропущено.
2025-03-26 21:45:30,825 - CpaQuebecParser - INFO - Запуск браузера Chromium (Headless: False) через Playwright...
2025-03-26 21:45:31,745 - CpaQuebecParser - INFO - Браузер Chromium запущен.
2025-03-26 21:45:31,747 - CpaQuebecParser - INFO - Используется User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-03-26 21:45:31,770 - CpaQuebecParser - INFO - Контекст браузера создан.
2025-03-26 21:45:32,010 - CpaQuebecParser - INFO - Новая страница создана.
2025-03-26 21:45:32,019 - CpaQuebecParser - INFO - Добавлен init script для скрытия navigator.webdriver.
2025-03-26 21:45:32,019 - CpaQuebecParser - INFO - ============================== ЗАПУСК ПАРСИНГА (Playwright) ==============================
2025-03-26 21:45:32,019 - CpaQuebecParser - INFO - Режим: Парсинг по категориям клиентов.
2025-03-26 21:45:32,020 - CpaQuebecParser - INFO - Запуск парсинга по категориям (Playwright): Arts and culture organizations, Co-ownership syndicates, Elderly individuals, Executives, Farmers, Indigenous communities, Individuals, Large businesses, Medium-sized businesses, Non-profit organizations, Professionals, Self-employed workers, Small businesses, Sports organizations, Start-ups
2025-03-26 21:45:32,020 - CpaQuebecParser - INFO - --- Обработка категории: Arts and culture organizations (1/15) ---
2025-03-26 21:45:32,020 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 21:45:41,385 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 21:45:41,385 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 21:45:43,469 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: '#onetrust-accept-btn-handler')
2025-03-26 21:45:43,619 - CpaQuebecParser - INFO - Клик по кнопке cookie выполнен.
2025-03-26 21:45:43,619 - CpaQuebecParser - INFO - Баннер cookie обработан.
2025-03-26 21:45:44,621 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 21:45:44,639 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 21:45:48,060 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Arts and culture organizations' через JS.
2025-03-26 21:45:48,063 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Arts and culture organizations' при проверке.
2025-03-26 21:45:48,064 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Arts and culture organizations'. Пропускаем.
2025-03-26 21:45:48,903 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Arts and culture organizations_select_failed_20250326_214548.png
2025-03-26 21:45:48,926 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Arts and culture organizations_select_failed_20250326_214548.html
2025-03-26 21:45:48,927 - CpaQuebecParser - INFO - --- Обработка категории: Co-ownership syndicates (2/15) ---
2025-03-26 21:45:48,927 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 21:45:51,056 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 21:45:51,057 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 21:45:53,326 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 21:45:59,243 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 21:45:59,249 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 21:45:59,250 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 21:45:59,250 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 21:45:59,256 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 21:46:01,450 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Co-ownership syndicates' через JS.
2025-03-26 21:46:01,454 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Co-ownership syndicates' при проверке.
2025-03-26 21:46:01,454 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Co-ownership syndicates'. Пропускаем.
2025-03-26 21:46:02,369 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Co-ownership syndicates_select_failed_20250326_214601.png
2025-03-26 21:46:02,390 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Co-ownership syndicates_select_failed_20250326_214601.html
2025-03-26 21:46:02,390 - CpaQuebecParser - INFO - --- Обработка категории: Elderly individuals (3/15) ---
2025-03-26 21:46:02,390 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 21:46:04,559 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 21:46:04,560 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 21:46:06,764 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 21:46:12,646 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 21:46:12,651 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 21:46:12,651 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 21:46:12,651 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 21:46:12,654 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 21:46:14,864 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Elderly individuals' через JS.
2025-03-26 21:46:14,869 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Elderly individuals' при проверке.
2025-03-26 21:46:14,870 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Elderly individuals'. Пропускаем.
2025-03-26 21:46:15,641 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Elderly individuals_select_failed_20250326_214614.png
2025-03-26 21:46:15,662 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Elderly individuals_select_failed_20250326_214614.html
2025-03-26 21:46:15,662 - CpaQuebecParser - INFO - --- Обработка категории: Executives (4/15) ---
2025-03-26 21:46:15,663 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 21:46:17,750 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 21:46:17,750 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 21:46:19,965 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 21:46:25,831 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 21:46:25,836 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 21:46:25,836 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 21:46:25,836 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 21:46:25,839 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 21:46:27,975 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Executives' через JS.
2025-03-26 21:46:27,978 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Executives' при проверке.
2025-03-26 21:46:27,979 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Executives'. Пропускаем.
2025-03-26 21:46:28,663 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Executives_select_failed_20250326_214627.png
2025-03-26 21:46:28,683 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Executives_select_failed_20250326_214627.html
2025-03-26 21:46:28,683 - CpaQuebecParser - INFO - --- Обработка категории: Farmers (5/15) ---
2025-03-26 21:46:28,683 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 21:46:30,476 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 21:46:30,477 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 21:46:32,669 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 21:46:38,518 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 21:46:38,523 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 21:46:38,523 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 21:46:38,523 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 21:46:38,526 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 21:46:40,675 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Farmers' через JS.
2025-03-26 21:46:40,681 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Farmers' при проверке.
2025-03-26 21:46:40,681 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Farmers'. Пропускаем.
2025-03-26 21:46:41,291 - CpaQuebecParser - INFO - Общее время парсинга (Playwright): 69.27 секунд.
2025-03-26 21:46:41,291 - CpaQuebecParser - INFO - ============================== ЗАВЕРШЕНИЕ ПАРСИНГА (Playwright) ==============================
2025-03-26 21:46:41,291 - CpaQuebecParser - INFO - Закрытие браузера и Playwright...
