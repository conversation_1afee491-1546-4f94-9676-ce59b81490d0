2025-04-04 16:35:58,930 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_163558.log
2025-04-04 16:35:58,933 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 16:35:58,934 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:35:58,934 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:35:58,934 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:35:58,934 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:38:25,224 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 1
