import requests
import cloudscraper
import time
from cpa_quebec.config import MAX_RETRY_ATTEMPTS, RETRY_DELAY
from bs4 import BeautifulSoup
import logging
import re
from fake_useragent import UserAgent
from urllib.parse import urljoin
from typing import Optional, Dict, List


class DetailsParser:
    """
    HTTP-парсер детальных страниц CPA Quebec без браузера.
    """
    def __init__(self, base_url: str = None, session=None):
        # Базовый URL
        self.base_url = base_url.rstrip('/') if base_url else 'https://cpaquebec.ca'
        # Сессия: используем переданную или создаём cloud scraper
        self.session = session if session else cloudscraper.create_scraper()
        # Логгер для подробного вывода
        self.logger = logging.getLogger(__name__)
        self.logger.debug(f"Initialized DetailsParser with base_url={self.base_url}")
        # Заголовки
        ua = UserAgent()
        self.session.headers.update({
            'User-Agent': ua.random,
            'Referer': self.base_url,
        })

    def parse(self, profile_url: str) -> Optional[Dict[str, any]]:
        """
        Парсит детальную страницу CPA по URL и возвращает словарь полей:
        name_detail, title, company, address, phone_detail, email_detail, website,
        languages, practice_areas, clients.
        """
        # Подготовка URL и повторные попытки запроса
        url = profile_url if profile_url.startswith('http') else self.base_url + profile_url
        for attempt in range(MAX_RETRY_ATTEMPTS):
            try:
                self.logger.info(f"Requesting profile URL ({attempt+1}/{MAX_RETRY_ATTEMPTS}): {url}")
                resp = self.session.get(url, timeout=10)
                self.logger.debug(f"GET {url} responded with status {resp.status_code}")
                resp.raise_for_status()
                soup = BeautifulSoup(resp.text, 'html.parser')
                break
            except Exception as e:
                self.logger.warning(f"Attempt {attempt+1} failed: {e}")
                if attempt < MAX_RETRY_ATTEMPTS - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    self.logger.error(f"All retries failed for {url}")
                    return None
        details: Dict[str, any] = {}

        # Имя
        el = soup.select_one('h1.profile-name')
        if el:
            details['name_detail'] = el.get_text(strip=True)
        # Должность
        el = soup.select_one('.profile-title')
        if el:
            details['title'] = el.get_text(strip=True)
        # Компания
        el = soup.select_one('.profile-company')
        if el:
            link = el.select_one('a')
            details['company'] = link.get_text(strip=True) if link else el.get_text(strip=True)
        # Адрес
        el = soup.select_one('.profile-address')
        if el:
            details['address'] = el.get_text(strip=True)
        # Телефон
        el = soup.select_one('.profile-phone')
        if el:
            details['phone_detail'] = re.findall(r'[\d\+\(\)\s\-]+', el.get_text())
        # Email
        el = soup.select_one('.profile-email a[href^="mailto:"]')
        if el:
            mail = el.get('href').replace('mailto:', '')
            details['email_detail'] = re.findall(r'[\w\.-]+@[\w\.-]+', mail)
        else:
            el2 = soup.select_one('.profile-email')
            if el2:
                details['email_detail'] = re.findall(r'[\w\.-]+@[\w\.-]+', el2.get_text())
        # Веб-сайт
        el = soup.select_one('.profile-website a')
        if el and el.get('href'):
            details['website'] = el.get('href')
        # Языки
        els = soup.select('.profile-languages li')
        if els:
            details['languages'] = [e.get_text(strip=True) for e in els]
        else:
            el = soup.select_one('.profile-languages')
            if el:
                details['languages'] = [el.get_text(strip=True)]
        # Области практики
        els = soup.select('.profile-practice-areas li')
        if els:
            details['practice_areas'] = [e.get_text(strip=True) for e in els]
        # Клиенты
        els = soup.select('.profile-clients li')
        if els:
            details['clients'] = [e.get_text(strip=True) for e in els]

        # Логирование результата парсинга
        self.logger.debug(f"Parsed details keys: {list(details.keys())}")
        return details 