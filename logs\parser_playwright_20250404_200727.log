2025-04-04 20:07:27,344 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_200727.log
2025-04-04 20:07:27,347 - <PERSON><PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:07:27,347 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 20:07:27,347 - C<PERSON>Q<PERSON>becParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 20:07:27,347 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 20:07:27,348 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:07:27,348 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:07:34,872 - CpaQuebecParser - DEBUG - Пауза на 1.26 сек. (после навигации)
2025-04-04 20:08:14,296 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 20:08:14,448 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
