import logging
import os
import time
import traceback
from datetime import datetime
from typing import Optional

from selenium.common.exceptions import (NoSuchElementException, TimeoutException,
                                      WebDriverException, StaleElementReferenceException,
                                      ElementNotInteractableException)
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from ..config import SHORT_TIMEOUT_SEC, MEDIUM_TIMEOUT_SEC # Используем секунды для WebDriverWait
from ..utils import random_sleep, sanitize_filename


class Navigator:
    """Отвечает за навигацию и базовые взаимодействия со страницей с использованием Selenium."""

    def __init__(
        self, driver: WebDriver, logger: logging.Logger, debug: bool,
        debug_dir: str, screenshots_dir: str, navigation_timeout: int):
        """
        Инициализирует Навигатор.

        Args:
            driver: Экземпляр Selenium WebDriver.
            logger: Логгер.
            debug: Флаг режима отладки.
            debug_dir: Директория для сохранения HTML.
            screenshots_dir: Директория для сохранения скриншотов.
            navigation_timeout: Таймаут загрузки страницы (из config, используется в WebDriverWait).
        """
        self.driver = driver
        self.logger = logger
        self.debug = debug
        self.debug_dir = debug_dir
        self.screenshots_dir = screenshots_dir
        # Сохраняем таймаут для использования в явных ожиданиях
        self.default_wait_timeout = navigation_timeout

    def goto(self, url: str, timeout: Optional[int] = None) -> bool:
        """Переходит по указанному URL с использованием Selenium."""
        effective_timeout = timeout if timeout is not None else self.default_wait_timeout
        self.logger.info(f"Переход на URL: {url} (таймаут загрузки: {effective_timeout} сек)")

        try:
            # Таймаут загрузки страницы уже установлен в BrowserManager
            # self.driver.set_page_load_timeout(effective_timeout) # Не обязательно здесь
            self.driver.get(url)
            self.logger.debug(f"Запрос на переход к {url} отправлен.")
            # Добавим небольшую паузу после get, т.к. page_load_timeout не всегда надежен
            random_sleep(1, 2.5, "после driver.get")
            # Проверим title как базовую проверку загрузки
            WebDriverWait(self.driver, SHORT_TIMEOUT_SEC).until(
                lambda d: d.title is not None and len(d.title) > 0
            )
            self.logger.info(f"Страница {url} загружена (Title: {self.driver.title[:50]}...)")
            # Добавляем вызов закрытия баннера cookie после успешной загрузки
            self.close_cookie_banner()
            return True
        except TimeoutException:
             self.logger.error(f"Таймаут ({effective_timeout} сек) при загрузке URL: {url} (возможно, страница не загрузилась полностью).", exc_info=self.debug)
             self.save_debug_info(f"timeout_navigating_to_{sanitize_filename(url)}")
             return False
        except WebDriverException as e:
            # Обработка специфичных ошибок Selenium
            if "net::ERR_" in str(e):
                 self.logger.error(f"Сетевая ошибка при загрузке URL: {url}. Ошибка: {e}")
            elif "invalid session id" in str(e):
                 self.logger.error(f"Сессия драйвера невалидна при попытке перейти на {url}. Ошибка: {e}")
            else:
                 self.logger.error(f"Ошибка WebDriver при загрузке URL: {url}. Ошибка: {e}")
            self.save_debug_info(f"webdriver_error_navigating_to_{sanitize_filename(url)}")
            return False
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка при переходе на URL: {url}. Ошибка: {e}")
            self.logger.debug(traceback.format_exc())
            self.save_debug_info(f"unexpected_error_navigating_to_{sanitize_filename(url)}")
            return False

    def close_cookie_banner(self, timeout: int = SHORT_TIMEOUT_SEC) -> bool:
        """Закрывает баннер cookie, если он есть (адаптировано под Selenium)."""
        self.logger.debug("Проверка наличия и закрытие баннера cookie (Selenium)...")

        # Расширенный список XPath селекторов для кнопок закрытия/принятия cookie
        # Ищем кнопки и ссылки на английском и французском языках
        common_button_xpaths = [
            # Кнопки с текстом на английском
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accept')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'agree')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'ok')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'got it')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'continue')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'close')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'allow')]",

            # Кнопки с текстом на французском
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accepter')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accepte')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'fermer')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'continuer')]",
            "//button[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'autoriser')]",

            # Кнопки с атрибутами
            "//button[@aria-label='close' or @aria-label='Close' or @aria-label='Fermer']",
            "//button[@id='onetrust-accept-btn-handler']",
            "//button[contains(@id, 'cookie') and (contains(@id, 'accept') or contains(@id, 'agree') or contains(@id, 'close'))]",
            "//button[contains(@class, 'cookie') and (contains(@class, 'accept') or contains(@class, 'agree') or contains(@class, 'close'))]",

            # Ссылки с текстом на английском
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accept')]",
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'agree')]",
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'close')]",
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'allow')]",

            # Ссылки с текстом на французском
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accepter')]",
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accepte')]",
            "//a[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'fermer')]",

            # Дивы с ролью кнопки
            "//div[@role='button' and (contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'accept') or contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'close'))]",

            # Специфичные селекторы для известных cookie-баннеров
            "//div[contains(@class, 'cookie-banner')]//button[contains(@class, 'accept') or contains(@class, 'close')]",
            "//div[contains(@id, 'cookie-banner')]//button[contains(@class, 'accept') or contains(@class, 'close')]",
            "//div[contains(@class, 'gdpr')]//button[contains(@class, 'accept') or contains(@class, 'close')]",
            "//div[contains(@id, 'gdpr')]//button[contains(@class, 'accept') or contains(@class, 'close')]"
        ]

        cookie_button: Optional[WebDriver] = None
        found_xpath = None

        wait = WebDriverWait(self.driver, timeout) # Используем переданный или короткий таймаут

        for xpath in common_button_xpaths:
            try:
                self.logger.debug(f"Попытка найти кнопку cookie по XPath: {xpath}")
                # Ищем элемент, который является кликабельным
                potential_button = wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
                # Дополнительная проверка на видимость, т.к. кликабельность не всегда гарантирует ее
                if potential_button.is_displayed():
                     cookie_button = potential_button
                     found_xpath = xpath
                     self.logger.info(f"Найден кликабельный и видимый баннер cookie по XPath: {found_xpath}")
                     break # Нашли подходящую кнопку, выходим из цикла
                else:
                     self.logger.debug(f"Элемент найден по XPath {xpath}, но он не видим.")
            except TimeoutException:
                self.logger.debug(f"Кнопка cookie не найдена по XPath: {xpath}")
                continue # Пробуем следующий XPath
            except Exception as e:
                 self.logger.warning(f"Ошибка при поиске кнопки cookie по XPath {xpath}: {e}")
                 continue # Пробуем следующий XPath

        # Если кнопку нашли
        if cookie_button and found_xpath:
            try:
                self.logger.info(f"Попытка закрыть баннер cookie (XPath: {found_xpath})...")
                # Прокрутка к элементу (на всякий случай)
                self.driver.execute_script("arguments[0].scrollIntoView(true);", cookie_button)
                random_sleep(0.3, 0.7, "после scroll к кнопке cookie")
                cookie_button.click()

                # Ожидаем, пока кнопка исчезнет (или станет некликабельной/невидимой)
                # Ожидание невидимости надежнее, но дольше. Ожидание устаревания быстрее.
                self.logger.debug(f"Ожидание исчезновения/устаревания кнопки cookie...")
                # WebDriverWait(self.driver, timeout).until(
                #     EC.invisibility_of_element_located((By.XPATH, found_xpath))
                # )
                # Используем ожидание устаревания, т.к. элемент может просто скрыться, а не удалиться
                WebDriverWait(self.driver, timeout).until(EC.staleness_of(cookie_button))
                self.logger.info("Баннер cookie успешно закрыт.")
                random_sleep(0.5, 1, "после закрытия cookie")
                return True
            except StaleElementReferenceException:
                 # Если элемент устарел сразу после клика - это тоже успех
                 self.logger.info("Баннер cookie закрыт (элемент устарел после клика).")
                 random_sleep(0.5, 1, "после закрытия cookie (stale)")
                 return True
            except TimeoutException:
                 # Если кнопка не исчезла/не устарела - возможно, клик не сработал
                 self.logger.warning(f"Клик по кнопке cookie (XPath: {found_xpath}) выполнен, но она не исчезла/устарела вовремя.")
                 # Все равно возвращаем True, т.к. попытка была, и возможно баннер закроется позже
                 return True
            except ElementNotInteractableException:
                 self.logger.warning(f"Найденная кнопка cookie (XPath: {found_xpath}) оказалась некликабельной в момент попытки клика.")
                 return False # Считаем неудачей
            except Exception as e:
                self.logger.warning(f"Ошибка при попытке кликнуть/ожидать закрытия баннера cookie (XPath: {found_xpath}): {e}")
                self.save_debug_info(f"error_clicking_cookie_banner")
                return False # Считаем неудачей
        else:
            # Если ни один XPath не сработал
            self.logger.debug(f"Баннер cookie не найден ни по одному из XPath за {timeout} сек.")
            return True # Считаем, что его нет, это не ошибка

    def save_debug_info(self, prefix: str):
        """Сохраняет скриншот и HTML для отладки (адаптировано под Selenium)."""
        if not self.debug:
            return
        # Добавим проверку драйвера
        if not self.driver:
             self.logger.warning(f"Попытка сохранить debug info ({prefix}), но драйвер неактивен.")
             return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            # Используем папку screenshots_selenium
            screenshot_path = os.path.join(self.screenshots_dir, f"{prefix}_{timestamp}.png")
            html_path = os.path.join(self.debug_dir, f"{prefix}_{timestamp}.html")

            os.makedirs(self.screenshots_dir, exist_ok=True)
            os.makedirs(self.debug_dir, exist_ok=True)

            # Сохранение скриншота Selenium
            self.logger.debug(f"Сохранение скриншота Selenium: {screenshot_path}")
            self.driver.save_screenshot(screenshot_path)

            # Сохранение HTML Selenium
            self.logger.debug(f"Сохранение HTML Selenium: {html_path}")
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
        except WebDriverException as e:
             # Часто бывает "invalid session id" если браузер закрылся
             self.logger.error(f"Ошибка WebDriver при сохранении debug info ({prefix}): {e}")
        except Exception as e:
            self.logger.error(f"Не удалось сохранить отладочную информацию ({prefix}): {e}")
