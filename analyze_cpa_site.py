#!/usr/bin/env python3
"""
Скрипт для анализа структуры сайта CPA Quebec
"""
import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("cpa_analyzer")

BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"

async def analyze_site():
    """Анализирует структуру сайта CPA Quebec"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(viewport={"width": 1280, "height": 800})
        page = await context.new_page()
        
        try:
            # Переходим на страницу
            logger.info(f"Переходим на {BASE_URL}")
            await page.goto(BASE_URL, timeout=60000)
            await page.wait_for_load_state("domcontentloaded")
            
            # Закрываем cookie banner если есть
            try:
                cookie_banner = page.locator("text=Tout refuser")
                if await cookie_banner.is_visible(timeout=5000):
                    await cookie_banner.click()
                    logger.info("Cookie banner закрыт")
                    await page.wait_for_timeout(2000)
            except:
                pass
            
            # Анализируем формы на странице
            logger.info("=== АНАЛИЗ ФОРМ ===")
            forms_info = await page.evaluate("""
                () => {
                    const forms = document.querySelectorAll('form');
                    const result = [];
                    
                    for (let i = 0; i < forms.length; i++) {
                        const form = forms[i];
                        const formInfo = {
                            id: form.id || `form_${i}`,
                            action: form.action,
                            method: form.method || 'GET',
                            elements: []
                        };
                        
                        // Анализируем элементы формы
                        const elements = form.elements;
                        for (let j = 0; j < elements.length; j++) {
                            const element = elements[j];
                            formInfo.elements.push({
                                name: element.name,
                                id: element.id,
                                type: element.type,
                                value: element.value,
                                placeholder: element.placeholder,
                                checked: element.checked,
                                tagName: element.tagName
                            });
                        }
                        
                        result.push(formInfo);
                    }
                    
                    return result;
                }
            """)
            
            for i, form in enumerate(forms_info):
                logger.info(f"Форма {i+1}: ID={form['id']}, Action={form['action']}, Method={form['method']}")
                for elem in form['elements']:
                    if elem['type'] in ['text', 'search', 'submit', 'button', 'checkbox', 'radio']:
                        logger.info(f"  - {elem['tagName']} {elem['type']}: name='{elem['name']}', id='{elem['id']}', value='{elem['value']}', checked={elem['checked']}")
            
            # Анализируем поля ввода
            logger.info("\n=== АНАЛИЗ ПОЛЕЙ ВВОДА ===")
            input_info = await page.evaluate("""
                () => {
                    const inputs = document.querySelectorAll('input[type="text"], input[type="search"], textarea');
                    const result = [];
                    
                    for (const input of inputs) {
                        result.push({
                            id: input.id,
                            name: input.name,
                            placeholder: input.placeholder || '',
                            value: input.value || '',
                            type: input.type,
                            tagName: input.tagName
                        });
                    }
                    
                    return result;
                }
            """)
            
            for inp in input_info:
                logger.info(f"Input: {inp['tagName']} {inp['type']} - name='{inp['name']}', id='{inp['id']}', placeholder='{inp['placeholder']}'")
            
            # Анализируем чекбоксы
            logger.info("\n=== АНАЛИЗ ЧЕКБОКСОВ ===")
            checkbox_info = await page.evaluate("""
                () => {
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    const result = [];
                    
                    for (const checkbox of checkboxes) {
                        // Ищем связанную метку
                        let labelText = '';
                        const label = document.querySelector(`label[for="${checkbox.id}"]`);
                        if (label) {
                            labelText = label.textContent.trim();
                        } else {
                            // Если нет for, ищем родительскую метку
                            const parentLabel = checkbox.closest('label');
                            if (parentLabel) {
                                labelText = parentLabel.textContent.trim();
                            }
                        }
                        
                        result.push({
                            id: checkbox.id,
                            name: checkbox.name,
                            value: checkbox.value,
                            checked: checkbox.checked,
                            label: labelText
                        });
                    }
                    
                    return result;
                }
            """)
            
            for cb in checkbox_info:
                logger.info(f"Checkbox: name='{cb['name']}', id='{cb['id']}', value='{cb['value']}', checked={cb['checked']}, label='{cb['label']}'")
            
            # Анализируем кнопки
            logger.info("\n=== АНАЛИЗ КНОПОК ===")
            button_info = await page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
                    const result = [];
                    
                    for (const button of buttons) {
                        result.push({
                            id: button.id,
                            name: button.name,
                            type: button.type,
                            value: button.value,
                            text: button.textContent ? button.textContent.trim() : '',
                            tagName: button.tagName
                        });
                    }
                    
                    return result;
                }
            """)
            
            for btn in button_info:
                logger.info(f"Button: {btn['tagName']} {btn['type']} - id='{btn['id']}', text='{btn['text']}', value='{btn['value']}'")
            
            # Делаем попытку поиска без фильтров
            logger.info("\n=== ТЕСТОВЫЙ ПОИСК БЕЗ ФИЛЬТРОВ ===")
            
            # Сначала решаем капчу если есть
            captcha_exists = await page.evaluate("""
                () => {
                    return document.querySelector('.g-recaptcha') !== null ||
                           document.querySelector('iframe[src*="recaptcha"]') !== null;
                }
            """)
            
            if captcha_exists:
                logger.info("Обнаружена reCAPTCHA, останавливаемся")
                input("Решите капчу вручную и нажмите Enter для продолжения...")
            
            # Пробуем нажать кнопку поиска
            search_clicked = await page.evaluate("""
                () => {
                    // Ищем форму поиска
                    const form = document.querySelector('#FindACPABottinForm');
                    if (form) {
                        const button = form.querySelector('button[type="submit"]');
                        if (button) {
                            button.click();
                            console.log('Clicked search button in form');
                            return true;
                        }
                    }
                    
                    // Пробуем найти любую кнопку поиска
                    const buttons = document.querySelectorAll('button[type="submit"]');
                    for (const btn of buttons) {
                        if (btn.textContent && btn.textContent.toLowerCase().includes('search')) {
                            btn.click();
                            console.log('Clicked search button');
                            return true;
                        }
                    }
                    
                    return false;
                }
            """)
            
            if search_clicked:
                logger.info("Кнопка поиска нажата")
                await page.wait_for_timeout(5000)
                
                # Анализируем результаты
                new_url = page.url
                logger.info(f"Новый URL: {new_url}")
                
                # Ищем результаты
                results_text = await page.evaluate("""
                    () => {
                        return document.body.innerText.substring(0, 1000);
                    }
                """)
                
                logger.info(f"Текст страницы (первые 1000 символов):\n{results_text}")
                
                # Ищем элементы с email или телефоном
                contact_elements = await page.evaluate("""
                    () => {
                        const emails = document.querySelectorAll('a[href^="mailto"]');
                        const phones = document.querySelectorAll('a[href^="tel"]');
                        
                        return {
                            emailCount: emails.length,
                            phoneCount: phones.length,
                            emails: Array.from(emails).slice(0, 5).map(e => e.href),
                            phones: Array.from(phones).slice(0, 5).map(p => p.href)
                        };
                    }
                """)
                
                logger.info(f"Найдено контактов: emails={contact_elements['emailCount']}, phones={contact_elements['phoneCount']}")
                if contact_elements['emails']:
                    logger.info(f"Примеры email: {contact_elements['emails']}")
                if contact_elements['phones']:
                    logger.info(f"Примеры телефонов: {contact_elements['phones']}")
            else:
                logger.error("Не удалось нажать кнопку поиска")
            
            # Ждем для ручного анализа
            input("\nНажмите Enter для завершения анализа...")
            
        except Exception as e:
            logger.error(f"Ошибка: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(analyze_site()) 