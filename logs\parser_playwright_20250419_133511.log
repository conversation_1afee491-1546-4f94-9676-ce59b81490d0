2025-04-19 13:35:11,482 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_133511.log
2025-04-19 13:35:11,485 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 13:35:11,487 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-19 13:35:11,487 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 13:35:11,488 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals'}
2025-04-19 13:35:11,489 - CpaQuebecParser - INFO - ==================================================
2025-04-19 13:35:11,489 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-19 13:35:22,999 - CpaQuebecParser - DEBUG - Пауза на 1.35 сек. (после driver.get)
2025-04-19 13:35:24,664 - CpaQuebecParser - DEBUG - Пауза на 0.43 сек. (после scroll к кнопке cookie)
2025-04-19 13:36:06,570 - CpaQuebecParser - DEBUG - Пауза на 0.95 сек. (после клика Reset (XPath))
2025-04-19 13:36:09,897 - CpaQuebecParser - DEBUG - Пауза на 0.78 сек. (после очистки формы)
2025-04-19 13:36:29,322 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
