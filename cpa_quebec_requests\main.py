#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import logging
import sys

# Импортируем парсеры
from .updated_parser import CpaQuebecUpdatedParser
from .hybrid_parser import HybridCpaQuebecParser, main as hybrid_main
from .saver import save_results
from .details import DetailsParser
from cpa_quebec.utils import setup_logging


def main():
    parser = argparse.ArgumentParser(
        description="Парсер CPA Quebec"
    )
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        '--by-category', action='store_true',
        help='Парсинг по всем категориям из config'
    )
    group.add_argument(
        '--category', type=str,
        help='Парсинг указанной категории (точное совпадение с config)'
    )
    parser.add_argument(
        '--debug', action='store_true',
        help='Включить подробное логирование'
    )
    parser.add_argument(
        '--get-details', action='store_true', default=False,
        help='Собирать детальную информацию профилей'
    )
    parser.add_argument(
        '--hybrid', action='store_true',
        help='Использовать гибридный парсер (Selenium + HTTP)'
    )
    parser.add_argument(
        '--headless', action='store_true', default=True,
        help='Запускать браузер в фоновом режиме (только для --hybrid)'
    )
    parser.add_argument(
        '--no-headless', action='store_false', dest='headless',
        help='Запускать браузер в видимом режиме (только для --hybrid)'
    )
    parser.add_argument(
        '--no-captcha', action='store_false', dest='solve_captcha', default=True,
        help='Не решать капчу (только для --hybrid)'
    )
    args = parser.parse_args()

    # Настройка логирования
    logger = setup_logging(
        log_level=logging.DEBUG if args.debug else logging.INFO,
        log_file_prefix="cpa_quebec_requests"
    )

    # Если выбран гибридный режим, используем гибридный парсер
    if args.hybrid:
        logger.info("Запуск в гибридном режиме (Selenium + HTTP)")

        # Создаем новый список аргументов для гибридного парсера
        hybrid_args = []

        # Добавляем аргументы категории
        if args.by_category:
            hybrid_args.append('--by-category')
        elif args.category:
            hybrid_args.extend(['--category', args.category])

        # Добавляем остальные аргументы
        if args.debug:
            hybrid_args.append('--debug')
        if args.get_details:
            hybrid_args.append('--get-details')
        if not args.headless:
            hybrid_args.append('--no-headless')
        if not args.solve_captcha:
            hybrid_args.append('--no-captcha')

        # Запускаем гибридный парсер с новыми аргументами
        try:
            with HybridCpaQuebecParser(
                debug=args.debug,
                headless=args.headless,
                solve_captcha=args.solve_captcha
            ) as parser:
                if args.by_category:
                    logger.info("\u0417\u0430\u043f\u0443\u0441\u043a \u043f\u0430\u0440\u0441\u0438\u043d\u0433\u0430 \u043f\u043e \u0432\u0441\u0435\u043c \u043a\u0430\u0442\u0435\u0433\u043e\u0440\u0438\u044f\u043c...")
                    results = parser.parse_all(get_details=args.get_details)
                    total_profiles = sum(len(profiles) for profiles in results.values())
                    logger.info(f"\u041f\u0430\u0440\u0441\u0438\u043d\u0433 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d. \u0412\u0441\u0435\u0433\u043e \u043d\u0430\u0439\u0434\u0435\u043d\u043e \u043f\u0440\u043e\u0444\u0438\u043b\u0435\u0439: {total_profiles}")
                else:
                    logger.info(f"\u0417\u0430\u043f\u0443\u0441\u043a \u043f\u0430\u0440\u0441\u0438\u043d\u0433\u0430 \u043a\u0430\u0442\u0435\u0433\u043e\u0440\u0438\u0438 '{args.category}'...")
                    profiles = parser.parse_category(args.category, get_details=args.get_details)
                    logger.info(f"\u041f\u0430\u0440\u0441\u0438\u043d\u0433 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d. \u041d\u0430\u0439\u0434\u0435\u043d\u043e \u043f\u0440\u043e\u0444\u0438\u043b\u0435\u0439: {len(profiles)}")
            return 0
        except Exception as e:
            logger.critical(f"\u041a\u0440\u0438\u0442\u0438\u0447\u0435\u0441\u043a\u0430\u044f \u043e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u0438 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u0438 \u043f\u0430\u0440\u0441\u0438\u043d\u0433\u0430: {e}", exc_info=True)
            return 1

    # Иначе используем HTTP-парсер
    logger.info("Запуск в HTTP-режиме (без браузера)")
    parser_obj = CpaQuebecUpdatedParser(debug=args.debug)

    # Выбор режима парсинга
    if args.by_category:
        logging.info('Запущен парсинг по всем категориям')
        data = parser_obj.parse_all()
    else:
        category = args.category
        logging.info(f"Запущен парсинг категории: '{category}'")
        if category not in parser_obj.categories:
            logging.error(f"Категория '{category}' не найдена. Доступные: {parser_obj.categories}")
            exit(1)
        data = {category: parser_obj.parse_category(category)}

    # Сбор деталей профилей, если включено
    if args.get_details:
        logging.info('Начинаем сбор деталей профилей...')
        details_parser = DetailsParser()
        for category, items in data.items():
            enriched = []
            for item in items:
                profile_url = item.get('profile_url') or item.get('ProfileUrl')
                details = None
                if profile_url:
                    details = details_parser.parse(profile_url)
                if details:
                    item.update(details)
                enriched.append(item)
            data[category] = enriched

    # Сохранение результатов
    logging.info('Сохранение результатов...')
    save_results(data)
    logging.info('Сохранение завершено.')

    # Выводим итоговую статистику
    total_profiles = sum(len(profiles) for profiles in data.values())
    logging.info(f"Парсинг завершен. Всего найдено профилей: {total_profiles}")

    # Если профили не найдены, выводим рекомендацию
    if total_profiles == 0:
        logging.warning("Профили не найдены. Возможно, сайт требует решения капчи.")
        logging.warning("Рекомендуется использовать версию парсера с Selenium/Playwright для обхода капчи.")


if __name__ == '__main__':
    main()