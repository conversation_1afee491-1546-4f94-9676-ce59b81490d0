2025-04-19 13:59:34,058 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_135934.log
2025-04-19 13:59:34,061 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Parser - INFO - ==================================================
2025-04-19 13:59:34,061 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 13:59:34,062 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-19 13:59:34,062 - CpaQuebecParser - INFO - ==================================================
2025-04-19 13:59:34,062 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-19 13:59:56,518 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
