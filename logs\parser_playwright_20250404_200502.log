2025-04-04 20:05:02,193 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_200502.log
2025-04-04 20:05:02,197 - <PERSON><PERSON>Q<PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:05:02,197 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 20:05:02,197 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 20:05:02,197 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Parser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 20:05:02,197 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:05:02,198 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:05:10,157 - CpaQuebecParser - DEBUG - Пауза на 0.80 сек. (после навигации)
2025-04-04 20:05:37,558 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 20:05:37,726 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
