#!/usr/bin/env python3
"""
Простой парсер CPA Quebec Directory - одна категория
"""
import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path

import aiohttp
from dotenv import load_dotenv
from playwright.async_api import async_playwright

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("simple_quebec_parser")

# Константы
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"
OUTPUT_DIR = Path("output")
ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")

# Глобальная сессия для Anti-Captcha
anticaptcha_session = None

# Категория для теста
CATEGORY_ID = "ListeClienteleDesserviesLeftColumn_0__Selected"  # Individuals

async def get_anticaptcha_session():
    """Получает или создает сессию для Anti-Captcha API"""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session

async def close_anticaptcha_session():
    """Закрывает сессию Anti-Captcha"""
    global anticaptcha_session
    if anticaptcha_session and not anticaptcha_session.closed:
        await anticaptcha_session.close()
        anticaptcha_session = None

async def anticaptcha_request(method: str, **payload):
    """Выполняет запрос к Anti-Captcha API"""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()
    
    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY
    
    try:
        async with session.post(url, json=payload, timeout=30) as resp:
            resp.raise_for_status()
            response_json = await resp.json()
            logger.debug(f"Anti-Captcha {method}: {response_json}")
            return response_json
    except Exception as e:
        logger.error(f"Ошибка Anti-Captcha API ({method}): {e}")
        return {"errorId": 1, "errorDescription": str(e)}

async def click_recaptcha_checkbox(page):
    """Кликает по галочке 'я не робот' в reCAPTCHA"""
    logger.info("Попытка клика по галочке 'я не робот'...")
    
    try:
        # Ждем появления iframe с reCAPTCHA
        await page.wait_for_selector("iframe[src*='recaptcha']", timeout=10000)
        logger.info("reCAPTCHA iframe найден")
        
        # Получаем все iframe с reCAPTCHA
        iframes = await page.query_selector_all("iframe[src*='recaptcha']")
        logger.info(f"Найдено {len(iframes)} iframe с reCAPTCHA")
        
        for i, iframe_element in enumerate(iframes):
            src = await iframe_element.get_attribute("src")
            logger.info(f"iframe #{i+1}: {src}")
            
            # Ищем iframe с anchor (чекбокс)
            if 'api2/anchor' in src:
                logger.info(f"Найден anchor iframe #{i+1}")
                
                # Получаем frame
                frame = await iframe_element.content_frame()
                if not frame:
                    logger.warning(f"Не удалось получить content_frame для iframe #{i+1}")
                    continue
                
                logger.info("Frame получен, ищем чекбокс...")
                
                # Ждем появления чекбокса в frame
                try:
                    await frame.wait_for_selector("#recaptcha-anchor", timeout=5000)
                    logger.info("Чекбокс найден в frame")
                    
                    # Получаем чекбокс
                    checkbox = frame.locator("#recaptcha-anchor")
                    
                    # Проверяем состояние чекбокса
                    is_checked = await checkbox.get_attribute("aria-checked")
                    logger.info(f"Состояние чекбокса aria-checked: {is_checked}")
                    
                    if is_checked == "true":
                        logger.info("Чекбокс уже отмечен")
                        return True
                    
                    # Прокручиваем к чекбоксу и кликаем
                    await checkbox.scroll_into_view_if_needed()
                    await page.wait_for_timeout(1000)
                    
                    # Пробуем разные методы клика
                    try:
                        await checkbox.click(timeout=5000)
                        logger.info("Клик по чекбоксу выполнен (обычный)")
                    except:
                        try:
                            await checkbox.click(force=True, timeout=5000)
                            logger.info("Клик по чекбоксу выполнен (принудительный)")
                        except:
                            # JavaScript клик как последняя попытка
                            await frame.evaluate("""
                                () => {
                                    const checkbox = document.querySelector('#recaptcha-anchor');
                                    if (checkbox) {
                                        checkbox.click();
                                        console.log('Клик по чекбоксу через JavaScript');
                                    }
                                }
                            """)
                            logger.info("Клик по чекбоксу выполнен (JavaScript)")
                    
                    # Ждем и проверяем результат
                    await page.wait_for_timeout(3000)
                    
                    # Проверяем состояние чекбокса после клика
                    is_checked_after = await checkbox.get_attribute("aria-checked")
                    logger.info(f"Состояние чекбокса после клика: {is_checked_after}")
                    
                    if is_checked_after == "true":
                        logger.info("Галочка 'я не робот' успешно установлена!")
                        return True
                    else:
                        logger.warning("Галочка не установилась после клика")
                
                except Exception as frame_error:
                    logger.error(f"Ошибка при работе с frame: {frame_error}")
                    continue
        
        logger.error("Не удалось найти anchor iframe или кликнуть по чекбоксу")
        return False
        
    except Exception as e:
        logger.error(f"Ошибка при клике по галочке reCAPTCHA: {e}")
        return False

async def solve_captcha(page):
    """Решает капчу автоматически через Anti-Captcha"""
    if not ANTICAPTCHA_KEY:
        logger.warning("ANTICAPTCHA_API_KEY не установлен, используем ручное решение")
        input("Решите капчу вручную и нажмите Enter для продолжения...")
        return True
    
    # Сначала кликаем по галочке "я не робот"
    logger.info("Кликаем по галочке 'я не робот'...")
    checkbox_clicked = await click_recaptcha_checkbox(page)
    
    if checkbox_clicked:
        # Ждем и проверяем, может капча решилась автоматически
        logger.info("Ждем возможного автоматического решения...")
        await page.wait_for_timeout(5000)
        
        # Проверяем состояние
        is_solved = await page.evaluate("""
            () => {
                const response = document.querySelector('#g-recaptcha-response');
                return response && response.value && response.value.length > 50;
            }
        """)
        
        if is_solved:
            logger.info("Капча решена автоматически после клика!")
            return True
        else:
            logger.info("Автоматическое решение не произошло, используем Anti-Captcha")
    else:
        logger.warning("Не удалось кликнуть по галочке, но продолжаем с Anti-Captcha")
    
    # Извлекаем sitekey
    sitekey = await page.evaluate("""
        () => {
            const recaptchaDiv = document.querySelector('.g-recaptcha');
            if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                return recaptchaDiv.getAttribute('data-sitekey');
            }
            
            const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
            for (const iframe of iframes) {
                const src = iframe.src;
                const match = src.match(/k=([^&]+)/);
                if (match) {
                    return match[1];
                }
            }
            
            return null;
        }
    """)
    
    if not sitekey:
        logger.error("Не удалось найти sitekey")
        return False
    
    logger.info(f"Найден sitekey: {sitekey[:10]}...")
    
    # Создаем задачу
    task_payload = {
        "task": {
            "type": "NoCaptchaTaskProxyless",
            "websiteURL": BASE_URL,
            "websiteKey": sitekey,
        }
    }
    
    response = await anticaptcha_request("createTask", **task_payload)
    task_id = response.get("taskId")
    
    if not task_id:
        logger.error("Не удалось создать задачу Anti-Captcha")
        return False
    
    logger.info(f"Задача Anti-Captcha создана, ID: {task_id}")
    
    # Ждем результат
    await asyncio.sleep(5)
    
    for attempt in range(60):
        response = await anticaptcha_request("getTaskResult", taskId=task_id)
        
        if response.get("status") == "ready":
            solution = response.get("solution")
            token = solution.get("gRecaptchaResponse") if solution else None
            
            if token and len(token) > 50:
                logger.info(f"Капча решена! Длина токена: {len(token)}")
                
                # Вставляем токен и вызываем callback
                await page.evaluate(f"""
                    (token) => {{
                        // Находим или создаем поле g-recaptcha-response
                        let response = document.querySelector('#g-recaptcha-response');
                        if (!response) {{
                            response = document.createElement('textarea');
                            response.id = 'g-recaptcha-response';
                            response.name = 'g-recaptcha-response';
                            response.className = 'g-recaptcha-response';
                            response.style.display = 'none';
                            document.body.appendChild(response);
                        }}
                        
                        // Вставляем токен
                        response.value = token;
                        response.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        
                        // Вызываем callback если есть
                        if (window.grecaptcha && window.___grecaptcha_cfg) {{
                            const widgets = window.___grecaptcha_cfg.clients;
                            for (const clientId in widgets) {{
                                const client = widgets[clientId];
                                if (client.callback) {{
                                    try {{
                                        client.callback(token);
                                        console.log('reCAPTCHA callback вызван');
                                    }} catch(e) {{
                                        console.error('Ошибка при вызове callback:', e);
                                    }}
                                }}
                            }}
                        }}
                        
                        // Активируем кнопки
                        const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                        buttons.forEach(btn => {{
                            btn.disabled = false;
                            btn.removeAttribute('disabled');
                        }});
                        
                        console.log('Токен капчи вставлен и callback вызван');
                    }}
                """, token)
                
                logger.info("Токен капчи вставлен и callback вызван")
                
                # Ждем немного для обработки
                await page.wait_for_timeout(3000)
                
                # Проверяем, что капча действительно решена
                final_check = await page.evaluate("""
                    () => {
                        const response = document.querySelector('#g-recaptcha-response');
                        const hasToken = response && response.value && response.value.length > 50;
                        
                        // Проверяем состояние кнопок
                        const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                        const buttonsEnabled = Array.from(buttons).some(btn => !btn.disabled);
                        
                        return {
                            hasToken: hasToken,
                            buttonsEnabled: buttonsEnabled,
                            tokenLength: response ? response.value.length : 0
                        };
                    }
                """)
                
                logger.info(f"Финальная проверка капчи: {final_check}")
                
                if final_check['hasToken']:
                    logger.info("Капча успешно решена и готова!")
                    return True
                else:
                    logger.warning("Токен не найден после вставки")
                    
        elif response.get("status") == "processing":
            logger.debug(f"Задача {task_id} обрабатывается... (попытка {attempt + 1})")
        
        await asyncio.sleep(3)
    
    logger.error("Превышено время ожидания решения капчи")
    return False

async def run_simple_parser():
    """Запускает простой парсер"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(viewport={"width": 1280, "height": 800})
        page = await context.new_page()
        
        try:
            logger.info("=== Простой парсер CPA Quebec ===")
            logger.info(f"Переходим на {BASE_URL}")
            
            # Переходим на страницу
            await page.goto(BASE_URL, timeout=60000)
            await page.wait_for_load_state("domcontentloaded")
            
            # Закрываем cookie banner
            try:
                cookie_banner = page.locator("text=Tout refuser")
                if await cookie_banner.is_visible(timeout=5000):
                    await cookie_banner.click()
                    logger.info("Cookie banner закрыт")
                    await page.wait_for_timeout(2000)
            except:
                pass
            
            # Выбираем одну категорию (Individuals)
            logger.info("Выбираем категорию: Individuals")
            
            # Сначала снимаем все чекбоксы
            await page.evaluate("""
                const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        checkbox.checked = false;
                        checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });
            """)
            
            # Устанавливаем нужный чекбокс
            try:
                checkbox = page.locator(f"#{CATEGORY_ID}")
                await checkbox.scroll_into_view_if_needed()
                await checkbox.check(force=True)
                
                is_checked = await checkbox.is_checked()
                if is_checked:
                    logger.info("Категория 'Individuals' успешно выбрана")
                else:
                    logger.error("Не удалось выбрать категорию")
                    return
            except Exception as e:
                logger.error(f"Ошибка при выборе категории: {e}")
                return
            
            # Обрабатываем капчу
            logger.info("Решаем капчу...")
            if not await solve_captcha(page):
                logger.error("Не удалось решить капчу")
                return
            
            # Нажимаем кнопку поиска
            logger.info("Нажимаем кнопку поиска...")
            
            # Ждем навигации после клика
            async with page.expect_navigation(timeout=30000):
                search_clicked = await page.evaluate("""
                    () => {
                        const form = document.querySelector('#FindACPABottinForm');
                        if (form) {
                            const button = form.querySelector('button[type="submit"]');
                            if (button) {
                                button.click();
                                console.log('Clicked search button in form');
                                return true;
                            }
                        }
                        
                        const buttons = document.querySelectorAll('button[type="submit"]');
                        for (const btn of buttons) {
                            if (btn.textContent && btn.textContent.toLowerCase().includes('search')) {
                                btn.click();
                                console.log('Clicked search button');
                                return true;
                            }
                        }
                        
                        return false;
                    }
                """)
            
            if not search_clicked:
                logger.error("Не удалось нажать кнопку поиска")
                return
            
            logger.info("Кнопка поиска нажата, ожидаем навигацию...")
            
            # Ждем загрузки страницы результатов
            await page.wait_for_load_state("domcontentloaded")
            await page.wait_for_timeout(5000)
            
            # Анализируем страницу результатов
            current_url = page.url
            logger.info(f"URL после поиска: {current_url}")
            
            # Проверяем, попали ли мы на страницу результатов
            is_results_page = await page.evaluate("""
                () => {
                    const url = window.location.href;
                    const text = document.body.innerText.toLowerCase();
                    
                    // Проверяем URL и содержимое страницы
                    const hasResultsUrl = url.includes('/search/') || url.includes('/results/');
                    const hasResultsText = text.includes('results') || text.includes('found') || 
                                          text.includes('directory') || text.includes('listing');
                    const hasNoResultsText = text.includes('0 of 0 result') || text.includes('no results found');
                    
                    return {
                        url: url,
                        hasResultsUrl: hasResultsUrl,
                        hasResultsText: hasResultsText,
                        hasNoResultsText: hasNoResultsText,
                        pageTitle: document.title,
                        firstParagraph: text.substring(0, 500)
                    };
                }
            """)
            
            logger.info(f"Анализ страницы: {is_results_page}")
            
            if is_results_page['hasNoResultsText']:
                logger.warning("Поиск не дал результатов (0 of 0 results)")
                logger.info("Возможные причины:")
                logger.info("1. Категория выбрана, но нет CPA в этой категории")
                logger.info("2. Капча не была полностью решена")
                logger.info("3. Форма не была правильно отправлена")
                return
            
            # Получаем текст страницы для анализа
            page_text = await page.evaluate("""
                () => document.body.innerText.substring(0, 2000)
            """)
            
            logger.info(f"Текст страницы результатов:\n{page_text}")
            
            # Ищем результаты
            results_info = await page.evaluate("""
                () => {
                    const results = [];
                    
                    // Ищем элементы с email
                    const emailElements = document.querySelectorAll('a[href^="mailto"]');
                    emailElements.forEach((elem, index) => {
                        results.push({
                            type: 'email',
                            text: elem.textContent,
                            href: elem.href,
                            index: index
                        });
                    });
                    
                    // Ищем элементы с телефоном
                    const phoneElements = document.querySelectorAll('a[href^="tel"]');
                    phoneElements.forEach((elem, index) => {
                        results.push({
                            type: 'phone',
                            text: elem.textContent,
                            href: elem.href,
                            index: index
                        });
                    });
                    
                    return {
                        emailCount: emailElements.length,
                        phoneCount: phoneElements.length,
                        totalResults: results.length,
                        results: results.slice(0, 10)
                    };
                }
            """)
            
            logger.info(f"Результаты поиска: {results_info}")
            
            if results_info['totalResults'] > 0:
                logger.info(f"Найдено контактов: {results_info['totalResults']}")
                
                # Сохраняем результаты
                OUTPUT_DIR.mkdir(exist_ok=True)
                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                filename = f"simple_cpa_results_{timestamp}.json"
                filepath = OUTPUT_DIR / filename
                
                with open(filepath, "w", encoding="utf-8") as f:
                    json.dump(results_info, f, indent=2, ensure_ascii=False)
                
                logger.info(f"Результаты сохранены: {filepath}")
            else:
                logger.warning("Контакты не найдены")
            
            # Ждем для ручного анализа
            input("\nПроанализируйте результаты и нажмите Enter для завершения...")
            
        except Exception as e:
            logger.error(f"Ошибка: {e}")
        finally:
            await close_anticaptcha_session()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(run_simple_parser()) 