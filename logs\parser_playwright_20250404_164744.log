2025-04-04 16:47:44,280 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_164744.log
2025-04-04 16:47:44,284 - <PERSON><PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 16:47:44,284 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 16:47:44,285 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:47:44,285 - C<PERSON>Q<PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:47:44,285 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:47:44,285 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:49:31,992 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 1
