# Логика работы парсера CPA Quebec (Selenium)

Данный документ описывает архитектуру и логику работы парсера для сайта CPA Quebec, реализованного с использованием Selenium и undetected_chromedriver.

## 1. Структура проекта

- **`main.py`**: Точка входа. Отвечает за парсинг аргументов командной строки и запуск основного класса парсера.
- **`requirements.txt`**: Список зависимостей Python.
- **`cpa_quebec/`**: Основной пакет парсера.
  - **`__init__.py`**: Инициализация пакета.
  - **`config.py`**: Конфигурационные параметры (URL, таймауты, селекторы, пути и т.д.).
  - **`utils.py`**: Вспомогательные функции (настройка логгирования, паузы, решение капчи, работа с User-Agent и т.д.).
  - **`selenium_parser/`**: Модули, специфичные для Selenium-версии парсера.
    - **`__init__.py`**: Инициализация подпакета.
    - **`browser.py` (`BrowserManager`)**: Управляет жизненным циклом `undetected_chromedriver` (запуск, настройка опций, таймаутов, закрытие).
    - **`navigation.py` (`Navigator`)**: Отвечает за базовую навигацию (`goto`), ожидание загрузки, закрытие cookie-баннеров и сохранение отладочной информации (HTML, скриншоты).
    - **`form_handler_new.py` (`FormHandler`)**: Взаимодействует с формой поиска (заполнение полей, выбор категорий, клики по кнопкам Reset/Search).
    - **`captcha.py` (`CaptchaHandler`)**: Инкапсулирует логику обнаружения и обработки reCAPTCHA (включая взаимодействие с `CaptchaSolver` из `utils.py`).
    - **`results.py` (`ResultsProcessor`)**: Обрабатывает страницы с результатами поиска (пагинация, извлечение ссылок на детальные страницы, вызов `DetailsParser`).
    - **`details.py` (`DetailsParser`)**: Извлекает детальную информацию со страницы профиля CPA.
    - **`saver.py` (`DataSaver`)**: Сохраняет собранные данные в файлы (Excel, JSON).
    - **`orchestrator.py` (`CpaQuebecParser`)**: Основной класс, координирующий работу всех компонентов. Реализует основную логику парсинга.
- **`output/`**: Директория для сохранения файлов с результатами.
- **`logs/`**: Директория для сохранения лог-файлов.
- **`debug/`**: Директория для сохранения отладочной информации (HTML, скриншоты), создается при использовании флага `--debug`.
- **`documentation/`**: Документация проекта (этот файл).

## 2. Логика работы

### 2.1. Инициализация (`main.py` -> `CpaQuebecParser.__init__` -> `__enter__`)

1.  **`main.py`**: Парсит аргументы командной строки (`argparse`).
2.  **`main.py`**: Создает экземпляр `CpaQuebecParser`, передавая ему настройки (режим debug, headless, таймауты, опцию решения капчи).
3.  **`CpaQuebecParser.__enter__`**: Создает и запускает `BrowserManager` (передавая ему таймауты).
4.  **`BrowserManager.__enter__`**: Запускает `undetected_chromedriver` с заданными опциями.
5.  **`CpaQuebecParser.__enter__`**: Вызывает `_initialize_components`.
6.  **`CpaQuebecParser._initialize_components`**: Создает экземпляры всех необходимых компонентов (`Navigator`, `FormHandler`, `CaptchaHandler`, `DetailsParser`, `ResultsProcessor`, `DataSaver`), передавая им драйвер и другие зависимости.

### 2.2. Выполнение парсинга (`main.py` -> `CpaQuebecParser.parse`)

Основной метод `parse` определяет режим работы на основе переданных аргументов:

- **Парсинг одной категории (`--category <название>`)**: 
    - Вызывается внутренний метод `_parse_single_category("<название>", ...)`. Этот метод:
        - Переходит на базовый URL.
        - Очищает форму.
        - Выбирает указанную категорию с помощью `FormHandler.select_category()`.
        - Обрабатывает reCAPTCHA с помощью `CaptchaHandler.handle()`.
        - Нажимает кнопку поиска с помощью `FormHandler.click_search_button()`.
        - Обрабатывает страницы результатов с помощью `ResultsProcessor.process_pages()`, собирая данные только для этой категории.
- **Парсинг всех категорий (`--by-category`)**: 
    - Вызывается внутренний метод `_parse_by_categories(...)`. Этот метод:
        - Получает список категорий из `config.CLIENT_CATEGORIES_TO_PARSE`.
        - **В цикле** проходит по списку категорий.
        - Для **каждой** категории вызывает `_parse_single_category(category, ...)`, выполняя все шаги, описанные выше.
        - Собирает результаты со всех категорий.
- **Поиск по критериям (`--last-name`, `--first-name`, `--city`, `--region`)**: 
    - Вызывается внутренний метод `_search_cpa(...)`. Этот метод:
        - Переходит на базовый URL.
        - Очищает форму.
        - Заполняет указанные поля поиска с помощью `FormHandler.fill_search_criteria()`.
        - Обрабатывает reCAPTCHA.
        - Нажимает кнопку поиска.
        - Обрабатывает страницы результатов.
- **Поиск без критериев (аргументы не указаны)**:
    - Вызывается `_search_cpa({}, ...)`, запуская поиск без фильтров.

#### 2.2.x Обработка reCAPTCHA перед поиском
Во всех режимах (поиск по критериям, парсинг одной или всех категорий) перед нажатием кнопки **Search** вызывается метод `CaptchaHandler.handle()`, реализующий следующие шаги:
1. Обнаружение iframe reCAPTCHA на странице (`iframe[src*="recaptcha"]`). Если iframe не найден или не виден — считается, что капча отсутствует.
2. Извлечение `sitekey` из `<div class="g-recaptcha" data-sitekey="...">` или из параметра `k=` в `src` атрибуте iframe.
3. Отправка `sitekey` и URL страницы в антикапча‑сервис через `CaptchaSolver.solve_recaptcha_v2()`.
4. Инъекция полученного токена в скрытое поле `<textarea id="g-recaptcha-response">` через JavaScript.
5. (Опционально) Клик по чекбоксу внутри iframe (`recaptcha-anchor`) для активации callback.
6. Возврат `True` при успешном решении капчи, `None` если капчи не было, `False` при ошибке решения.

### 2.3. Обработка результатов (`ResultsProcessor`, `DetailsParser`)

1.  **`ResultsProcessor.process_pages()`**: 
    - Определяет наличие пагинации.
    - В цикле проходит по страницам результатов.
    - На каждой странице извлекает ссылки на профили CPA (`extract_results_from_page`).
    - Для каждой уникальной ссылки (проверка по `processed_urls`): 
        - Если включен сбор деталей (`get_details=True`), вызывает `DetailsParser.parse_details(url)`.
        - `DetailsParser.parse_details()`: Переходит на страницу профиля, извлекает детальную информацию (имя, адрес, телефон и т.д.) и возвращает словарь.
    - Собирает список словарей с данными.

### 2.4. Сохранение данных (`DataSaver`)

1.  После завершения основного цикла парсинга (или при возникновении критической ошибки), `CpaQuebecParser` вызывает `DataSaver.save()`, передавая ему собранный список словарей.
2.  **`DataSaver.save()`**: Сохраняет данные в форматах Excel (`.xlsx`) и JSON (`.json`) в директорию `output/`, добавляя временную метку к имени файла.

### 2.5. Завершение (`CpaQuebecParser.__exit__`)

1.  После выхода из блока `with` в `main.py` вызывается `CpaQuebecParser.__exit__()`.
2.  **`CpaQuebecParser.__exit__()`**: Вызывает `BrowserManager.__exit__()`.
3.  **`BrowserManager.__exit__()`**: Корректно закрывает браузер и драйвер (`driver.quit()`).

## 3. Обработка ошибок и повторные попытки

- Основные операции (навигация, выбор категории, поиск, обработка страниц) обернуты в циклы `while retries < MAX_RETRY_ATTEMPTS`.
- При возникновении ожидаемых ошибок (сетевые, таймауты, устаревшие элементы - `ConnectionError`, `TimeoutException`, `StaleElementReferenceException`, `WebDriverException`) выполняется повторная попытка с экспоненциальной задержкой (`random_sleep`).
- При невозможности выполнить ключевое действие (например, выбрать категорию или решить капчу) парсинг этой категории/поиска может быть прерван.
- Непредвиденные ошибки логируются с `critical` уровнем.

## 4. Отладка (`--debug`)

При запуске с флагом `--debug`:
- Уровень логирования устанавливается на `DEBUG`.
- В ключевых точках выполнения (`goto`, `select_category`, `handle_captcha`, `process_pages` и т.д.) `Navigator` сохраняет:
    - Скриншоты (`.png`) в папку `debug/screenshots_selenium/`.
    - HTML-код страницы (`.html`) в папку `debug/html_selenium/`.
- Логируются более подробные сообщения об ошибках. 