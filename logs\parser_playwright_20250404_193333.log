2025-04-04 19:33:33,527 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_193333.log
2025-04-04 19:33:33,530 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:33:33,530 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 19:33:33,530 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:33:33,530 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:33:33,530 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:33:33,531 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:33:34,817 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): FormHandler.__init__() takes 3 positional arguments but 4 were given
2025-04-04 19:33:34,818 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 82, in run_parser
    with CpaQuebecParser(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 141, in __enter__
    self._initialize_components() # Инициализируем компоненты после запуска браузера
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 113, in _initialize_components
    self.form_handler = FormHandler(self.page, self.logger, self.navigator)
TypeError: FormHandler.__init__() takes 3 positional arguments but 4 were given

2025-04-04 19:33:34,818 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
