2025-04-04 19:55:58,793 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_195558.log
2025-04-04 19:55:58,796 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:55:58,796 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 19:55:58,796 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:55:58,796 - C<PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:55:58,796 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:55:58,796 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:56:08,119 - CpaQuebecParser - DEBUG - Пауза на 1.40 сек. (после навигации)
2025-04-04 19:56:26,982 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 19:56:27,373 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
