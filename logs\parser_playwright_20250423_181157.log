2025-04-23 18:11:57,676 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250423_181157.log
2025-04-23 18:11:57,678 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Parser - DEBUG - Режим отладки DEBUG включен.
2025-04-23 18:11:57,679 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-23 18:11:57,679 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-23 18:11:57,679 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals', 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-23 18:11:57,679 - CpaQuebecParser - INFO - ==================================================
2025-04-23 18:11:57,679 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-23 18:12:12,605 - CpaQuebecParser - DEBUG - Пауза на 2.15 сек. (после driver.get)
2025-04-23 18:12:14,850 - CpaQuebecParser - DEBUG - Пауза на 0.53 сек. (после scroll к кнопке cookie)
2025-04-23 18:12:22,061 - CpaQuebecParser - DEBUG - Пауза на 0.51 сек. (после очистки формы)
2025-04-23 18:12:22,664 - CpaQuebecParser - DEBUG - Пауза на 3.83 сек. (между категориями, после Individuals)
2025-04-23 18:12:26,502 - CpaQuebecParser - INFO - Парсинг (Selenium) завершен. Найдено и сохранено записей: 0
2025-04-23 18:12:26,572 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 0
