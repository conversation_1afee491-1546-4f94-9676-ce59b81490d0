2025-04-04 21:11:56,140 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_211156.log
2025-04-04 21:11:56,142 - C<PERSON>Q<PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 21:11:56,143 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 21:11:56,143 - C<PERSON>Q<PERSON>becParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 21:11:56,143 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 21:11:56,143 - CpaQuebecParser - INFO - ==================================================
2025-04-04 21:11:56,143 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 21:12:09,864 - CpaQuebecParser - DEBUG - Пауза на 1.09 сек. (после driver.get)
2025-04-04 21:12:11,480 - CpaQuebecParser - DEBUG - Пауза на 0.43 сек. (после scroll к кнопке cookie)
2025-04-04 21:12:24,162 - CpaQuebecParser - DEBUG - Пауза на 0.57 сек. (после клика Reset (XPath))
2025-04-04 21:12:31,354 - CpaQuebecParser - DEBUG - Пауза на 0.63 сек. (после очистки формы)
2025-04-04 21:12:50,336 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
