2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : Logging setup complete. Level: INFO. Log file: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/parser_log_20250505_185242.log
2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : Debug directory created: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_185242
2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION STARTED =====
2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : Arguments: {'by_category': False, 'category': 'Individuals', 'all': False, 'no_captcha': False, 'no_details': False, 'visible': True, 'debug': True, 'log_level': 'INFO', 'max_pages': 500, 'use_system_browser': True}
2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : Anti-Captcha API Key found (ending with ...cc18b). Automatic solving ENABLED.
2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : Attempting to find system Chrome/Chromium...
2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : Using system browser found at: /usr/bin/google-chrome
2025-05-05 18:52:42 [INFO    ] cpa_quebec_parser   : Initializing Playwright...
2025-05-05 18:52:43 [INFO    ] cpa_quebec_parser   : Launching browser (headed)...
2025-05-05 18:52:44 [INFO    ] cpa_quebec_parser   : Browser launched successfully. Version: 134.0.6998.165
2025-05-05 18:52:44 [INFO    ] cpa_quebec_parser   : Browser context created.
2025-05-05 18:52:45 [INFO    ] cpa_quebec_parser   : New page created.
2025-05-05 18:52:45 [INFO    ] cpa_quebec_parser   : Mode: Processing single category: Individuals
2025-05-05 18:52:45 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Individuals ---
2025-05-05 18:52:45 [INFO    ] cpa_quebec_parser   : ===== STARTING CATEGORY PROCESSING: Individuals =====
2025-05-05 18:52:46 [INFO    ] cpa_quebec_parser   : Navigating to base URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/
2025-05-05 18:53:18 [INFO    ] cpa_quebec_parser   : Base URL page loaded.
2025-05-05 18:53:20 [INFO    ] cpa_quebec_parser   : Found cookie banner button with selector: text=Tout refuser
2025-05-05 18:53:22 [INFO    ] cpa_quebec_parser   : Cookie banner closed.
2025-05-05 18:53:22 [INFO    ] cpa_quebec_parser   : Selecting category checkbox: 'Individuals'
2025-05-05 18:53:28 [INFO    ] cpa_quebec_parser   : Checkbox for 'Individuals' checked successfully.
2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Checking if CAPTCHA needs to be solved...
2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Search button is already enabled. CAPTCHA might not be required.
2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Clicking the search button...
2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Form-specific selector failed, trying role-based selector with nth
2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Found form with Search text
2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Found submit button in form
2025-05-05 18:53:30 [WARNING ] cpa_quebec_parser   : Error trying to click form-filtered button: Locator.click: Element is outside of the viewport
Call log:
  - waiting for locator("form").filter(has_text="Search").locator("button[type='submit']").first
  -     - locator resolved to <button type="submit">Search</button>
  -   - attempting click action
  -     - scrolling into view if needed
  -     - done scrolling

2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Found 2 search buttons by role
2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Trying to click search button #1
2025-05-05 18:53:30 [WARNING ] cpa_quebec_parser   : Failed to click search button #1: Locator.click: Element is outside of the viewport
Call log:
  - waiting for get_by_role("button", name="Search").first
  -     - locator resolved to <button type="submit">Search</button>
  -   - attempting click action
  -     - scrolling into view if needed
  -     - done scrolling

2025-05-05 18:53:30 [INFO    ] cpa_quebec_parser   : Trying to click search button #2
2025-05-05 18:53:31 [INFO    ] cpa_quebec_parser   : Search button #2 clicked successfully
2025-05-05 18:53:31 [INFO    ] cpa_quebec_parser   : Trying JavaScript click on first search button
2025-05-05 18:53:31 [INFO    ] cpa_quebec_parser   : JavaScript click executed
2025-05-05 18:53:31 [INFO    ] cpa_quebec_parser   : Search initiated successfully.
2025-05-05 18:53:31 [INFO    ] cpa_quebec_parser   : --- Processing results page 1 for category 'Individuals' ---
2025-05-05 18:53:39 [INFO    ] cpa_quebec_parser   : Using container: div.search-results
2025-05-05 18:53:40 [INFO    ] cpa_quebec_parser   : Found 1 cards with absolute selector: div[class*='result']
2025-05-05 18:53:40 [INFO    ] cpa_quebec_parser   : Successfully identified 1 result cards.
2025-05-05 18:53:40 [INFO    ] cpa_quebec_parser   : Found 1 result cards on page 1.
2025-05-05 18:53:40 [ERROR   ] cpa_quebec_parser   : Error processing card #1: Page.wait_for_timeout: Target page, context or browser has been closed
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 2905, in extract_card_data
    await card_locator.page.wait_for_timeout(100)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/async_api/_generated.py", line 11408, in wait_for_timeout
    await self._impl_obj.wait_for_timeout(timeout=timeout)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_page.py", line 1079, in wait_for_timeout
    await self._main_frame.wait_for_timeout(timeout)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_frame.py", line 756, in wait_for_timeout
    await self._channel.send("waitForTimeout", locals_to_params(locals()))
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TargetClosedError: Page.wait_for_timeout: Target page, context or browser has been closed
2025-05-05 18:53:40 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_185242/screenshot_card_processing_error_1_2025-05-05_18-53-40.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:53:40 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_185242/html_card_processing_error_1_2025-05-05_18-53-40.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:53:40 [WARNING ] cpa_quebec_parser   : Could not save HTML of problematic card: Locator.inner_html: Target page, context or browser has been closed
2025-05-05 18:53:40 [INFO    ] cpa_quebec_parser   : Successfully processed 0 cards out of 1 on page 1.
2025-05-05 18:53:40 [ERROR   ] cpa_quebec_parser   : Error handling pagination on page 1: Locator.count: Target page, context or browser has been closed
Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/newapp.py", line 3300, in process_category
    if await locator.count() > 0:
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/async_api/_generated.py", line 16637, in count
    return mapping.from_maybe_impl(await self._impl_obj.count())
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_locator.py", line 398, in count
    return await self._frame._query_count(self._selector)
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_frame.py", line 128, in _query_count
    return await self._channel.send("queryCount", {"selector": selector})
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/home/<USER>/.local/lib/python3.10/site-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TargetClosedError: Locator.count: Target page, context or browser has been closed
2025-05-05 18:53:40 [WARNING ] cpa_quebec_parser   : Failed to save screenshot /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_185242/screenshot_error_pagination_page_1_Individuals_2025-05-05_18-53-40.png: Page.screenshot: Target page, context or browser has been closed
2025-05-05 18:53:40 [WARNING ] cpa_quebec_parser   : Failed to save HTML /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/debug/20250505_185242/html_error_pagination_page_1_Individuals_2025-05-05_18-53-40.html: Page.content: Target page, context or browser has been closed
2025-05-05 18:53:40 [INFO    ] cpa_quebec_parser   : Finished collecting results for category 'Individuals'. Total items found: 0
2025-05-05 18:53:40 [INFO    ] cpa_quebec_parser   : ===== FINISHED CATEGORY PROCESSING: Individuals (Total: 0 items) =====
2025-05-05 18:53:40 [INFO    ] cpa_quebec_parser   : Category 'Individuals' processed. Found 0 items.
2025-05-05 18:53:42 [WARNING ] cpa_quebec_parser   : No results collected to save.
2025-05-05 18:53:42 [INFO    ] cpa_quebec_parser   : Cleaning up resources...
2025-05-05 18:53:42 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION FINISHED =====
