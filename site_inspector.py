#!/usr/bin/env python3
"""
Скрипт для изучения структуры сайта CPA Quebec Directory
Анализирует селекторы и элементы интерфейса
"""
import asyncio
import json
import logging
from pathlib import Path
from playwright.async_api import async_playwright, Page

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("site_inspector")

BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"

async def analyze_page_structure(page: Page) -> dict:
    """Анализирует структуру страницы и все элементы формы"""
    logger.info("Анализируем структуру страницы...")
    
    # Получаем информацию о всех формах
    forms_info = await page.evaluate("""
        () => {
            const forms = document.querySelectorAll('form');
            const result = [];
            
            for (let i = 0; i < forms.length; i++) {
                const form = forms[i];
                const formInfo = {
                    index: i,
                    id: form.id || '',
                    class: form.className || '',
                    action: form.action || '',
                    method: form.method || 'GET',
                    elements: []
                };
                
                // Анализируем все элементы формы
                const elements = form.querySelectorAll('input, select, textarea, button');
                elements.forEach((el, idx) => {
                    const rect = el.getBoundingClientRect();
                    formInfo.elements.push({
                        index: idx,
                        tag: el.tagName.toLowerCase(),
                        type: el.type || '',
                        id: el.id || '',
                        name: el.name || '',
                        value: el.value || '',
                        placeholder: el.placeholder || '',
                        className: el.className || '',
                        text: el.textContent ? el.textContent.trim().substring(0, 100) : '',
                        isVisible: el.offsetParent !== null,
                        position: {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        },
                        attributes: Array.from(el.attributes).map(attr => ({
                            name: attr.name,
                            value: attr.value
                        }))
                    });
                });
                
                result.push(formInfo);
            }
            
            return result;
        }
    """)
    
    # Получаем информацию о всех элементах, которые могут быть связаны с категориями
    category_elements = await page.evaluate("""
        () => {
            // Ищем все элементы, которые могут быть связаны с категориями
            const selectors = [
                'select',
                'input[type="checkbox"]',
                'input[type="radio"]',
                '.dropdown',
                '.select',
                '.category',
                '.client',
                '.filter',
                '[data-category]',
                '[data-filter]',
                'fieldset',
                'legend'
            ];
            
            const result = [];
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach((el, idx) => {
                    const rect = el.getBoundingClientRect();
                    const parent = el.parentElement;
                    const siblings = Array.from(parent ? parent.children : []);
                    
                    result.push({
                        selector: selector,
                        index: idx,
                        tag: el.tagName.toLowerCase(),
                        type: el.type || '',
                        id: el.id || '',
                        name: el.name || '',
                        value: el.value || '',
                        className: el.className || '',
                        text: el.textContent ? el.textContent.trim().substring(0, 150) : '',
                        innerHTML: el.innerHTML ? el.innerHTML.substring(0, 200) : '',
                        isVisible: el.offsetParent !== null,
                        position: {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        },
                        parent: {
                            tag: parent ? parent.tagName.toLowerCase() : '',
                            id: parent ? parent.id : '',
                            className: parent ? parent.className : ''
                        },
                        siblings: siblings.map(sibling => ({
                            tag: sibling.tagName.toLowerCase(),
                            text: sibling.textContent ? sibling.textContent.trim().substring(0, 50) : ''
                        })),
                        attributes: Array.from(el.attributes).map(attr => ({
                            name: attr.name,
                            value: attr.value
                        }))
                    });
                });
            });
            
            return result;
        }
    """)
    
    # Ищем все текстовые элементы, которые могут содержать названия категорий
    text_analysis = await page.evaluate("""
        () => {
            const categoryKeywords = [
                'individual', 'large companies', 'nfpo', 'professional firms',
                'public corporations', 'retailer', 'self-employed', 'sme',
                'start-up', 'syndicate', 'client', 'category', 'type'
            ];
            
            const allElements = document.querySelectorAll('*');
            const relevantElements = [];
            
            allElements.forEach((el, idx) => {
                const text = el.textContent ? el.textContent.toLowerCase() : '';
                const hasKeyword = categoryKeywords.some(keyword => text.includes(keyword));
                
                if (hasKeyword && el.children.length === 0) { // листовые элементы
                    const rect = el.getBoundingClientRect();
                    relevantElements.push({
                        index: idx,
                        tag: el.tagName.toLowerCase(),
                        text: el.textContent.trim(),
                        className: el.className || '',
                        id: el.id || '',
                        isVisible: el.offsetParent !== null,
                        position: {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        }
                    });
                }
            });
            
            return relevantElements;
        }
    """)
    
    return {
        'forms': forms_info,
        'category_elements': category_elements,
        'text_analysis': text_analysis
    }

async def take_screenshots(page: Page):
    """Делает скриншоты страницы"""
    output_dir = Path("debug_screenshots")
    output_dir.mkdir(exist_ok=True)
    
    # Полный скриншот
    await page.screenshot(path=output_dir / "full_page.png", full_page=True)
    logger.info(f"Сохранен полный скриншот: {output_dir / 'full_page.png'}")
    
    # Скриншот видимой области
    await page.screenshot(path=output_dir / "viewport.png")
    logger.info(f"Сохранен скриншот видимой области: {output_dir / 'viewport.png'}")

async def save_html(page: Page):
    """Сохраняет HTML страницы"""
    output_dir = Path("debug_html")
    output_dir.mkdir(exist_ok=True)
    
    html_content = await page.content()
    with open(output_dir / "page_source.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    logger.info(f"Сохранен HTML: {output_dir / 'page_source.html'}")

async def inspect_site():
    """Основная функция для изучения сайта"""
    async with async_playwright() as p:
        # Запускаем браузер в видимом режиме
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context(
            viewport={"width": 1280, "height": 800}
        )
        page = await context.new_page()
        
        try:
            logger.info(f"Переходим на страницу: {BASE_URL}")
            await page.goto(BASE_URL, timeout=60000)
            
            # Ждем загрузки страницы
            await page.wait_for_load_state("networkidle", timeout=30000)
            logger.info("Страница загружена")
            
            # Закрываем cookie banner если есть
            try:
                cookie_banner = page.locator("text=Tout refuser")
                if await cookie_banner.is_visible(timeout=5000):
                    await cookie_banner.click()
                    logger.info("Cookie banner закрыт")
                    await page.wait_for_timeout(2000)
            except:
                logger.info("Cookie banner не найден или уже закрыт")
            
            # Делаем скриншоты
            await take_screenshots(page)
            
            # Сохраняем HTML
            await save_html(page)
            
            # Анализируем структуру
            structure = await analyze_page_structure(page)
            
            # Сохраняем результаты анализа
            output_dir = Path("debug_analysis")
            output_dir.mkdir(exist_ok=True)
            
            with open(output_dir / "page_structure.json", "w", encoding="utf-8") as f:
                json.dump(structure, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Анализ сохранен: {output_dir / 'page_structure.json'}")
            
            # Выводим краткий отчет
            print("\n" + "="*50)
            print("КРАТКИЙ ОТЧЕТ ПО СТРУКТУРЕ СТРАНИЦЫ")
            print("="*50)
            
            print(f"\nНайдено форм: {len(structure['forms'])}")
            for i, form in enumerate(structure['forms']):
                print(f"  Форма {i+1}: id='{form['id']}', class='{form['class']}', элементов={len(form['elements'])}")
            
            print(f"\nЭлементы, связанные с категориями: {len(structure['category_elements'])}")
            for elem in structure['category_elements'][:10]:  # Показываем первые 10
                print(f"  {elem['tag']} (type={elem['type']}, id='{elem['id']}', class='{elem['className']}')")
                if elem['text']:
                    print(f"    Текст: {elem['text'][:100]}")
            
            print(f"\nТекстовые элементы с ключевыми словами: {len(structure['text_analysis'])}")
            for elem in structure['text_analysis'][:5]:  # Показываем первые 5
                print(f"  {elem['tag']}: {elem['text'][:100]}")
            
            # Пауза для изучения страницы
            print(f"\nСтраница открыта в браузере. Изучите интерфейс и нажмите Enter для продолжения...")
            input()
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(inspect_site()) 