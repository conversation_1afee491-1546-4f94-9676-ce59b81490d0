import logging
import os
import time
from typing import Optional, List, Dict, Set
from dotenv import load_dotenv

# Убираем импорты Playwright
# from playwright.sync_api import Page, Playwright, Browser<PERSON><PERSON><PERSON><PERSON>, Browser, sync_playwright
# Импортируем WebDriver Selenium
from selenium.webdriver.chrome.webdriver import WebDriver

# Импортируем нужные константы для значений по умолчанию
from ..config import (
    BASE_URL, OUTPUT_DIR, LOGS_DIR, MAX_RETRY_ATTEMPTS,
    CLIENT_CATEGORIES_TO_PARSE, DEBUG_DIR,
    PAGE_LOAD_TIMEOUT_SEC, IMPLICIT_WAIT_SEC, MEDIUM_TIMEOUT_SEC # Добавлен MEDIUM_TIMEOUT_SEC
)
from ..utils import setup_logging, random_sleep, CaptchaSolver
# Путь к BrowserManager остается тем же, но реализация внутри изменилась
# Обновляем путь к компонентам
from .browser import BrowserManager
from .navigation import Navigator
from .form_handler_new import FormHandler
from .captcha import CaptchaHandler
from .results import ResultsProcessor
from .details import DetailsParser
from .saver import DataSaver
# Добавляем импорты исключений Selenium
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, WebDriverException

# Добавляю декоратор для логирования времени выполнения ключевых методов
def log_duration(step_name):
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            self.logger.info(f"=== START {step_name} ===")
            start_time = time.time()
            result = func(self, *args, **kwargs)
            duration = time.time() - start_time
            self.logger.info(f"=== END {step_name} (took {duration:.2f}s) ===")
            return result
        return wrapper
    return decorator

class CpaQuebecParser:
    """
    Оркестратор для парсинга сайта CPA Quebec с использованием Selenium + undetected_chromedriver.
    Управляет браузером, компонентами парсинга и потоком данных.
    """
    def __init__(
        self,
        debug: bool = False,
        headless: bool = True,
        output_dir: str = OUTPUT_DIR, # Используем константу по умолчанию
        # Добавляем таймауты и опцию капчи с значениями по умолчанию из config
        wait_timeout: int = IMPLICIT_WAIT_SEC,
        page_load_timeout: int = PAGE_LOAD_TIMEOUT_SEC,
        element_timeout: int = MEDIUM_TIMEOUT_SEC,
        solve_captcha: bool = True
    ):
        """
        Инициализирует парсер.

        Args:
            debug (bool): Включить ли режим отладки.
            headless (bool): Запускать ли браузер в фоновом режиме.
            output_dir (str): Директория для сохранения результатов.
            wait_timeout (int): Неявное ожидание Selenium (секунды).
            page_load_timeout (int): Таймаут загрузки страницы Selenium (секунды).
            element_timeout (int): Явное ожидание элемента Selenium (секунды).
            solve_captcha (bool): Пытаться ли решать капчу.
        """
        self.debug = debug
        self.headless = headless
        self.output_dir = output_dir
        # Сохраняем переданные таймауты
        self.wait_timeout = wait_timeout
        self.page_load_timeout = page_load_timeout
        self.element_timeout = element_timeout
        self.logger = logging.getLogger(__name__)

        # Настройка логгера
        log_level = logging.DEBUG if self.debug else logging.INFO
        # Устанавливаем уровень только для этого логгера, setup_logging должен настроить остальное
        self.logger.setLevel(log_level)

        self.logger.debug(f"Инициализация CpaQuebecParser (Selenium) с таймаутами: wait={wait_timeout}, page={page_load_timeout}, element={element_timeout}")

        # Загрузка API ключа капчи и создание солвера на основе аргумента solve_captcha
        load_dotenv()
        api_key = os.getenv("ANTICAPTCHA_API_KEY")
        self.captcha_solver: Optional[CaptchaSolver] = None
        if solve_captcha:
            if api_key:
                self.captcha_solver = CaptchaSolver(api_key, self.logger)
                self.logger.info("Anti-captcha.com solver инициализирован.")
            else:
                self.logger.warning("Решение капчи включено (--solve-captcha), но ключ ANTICAPTCHA_API_KEY не найден в .env файле.")
        else:
            self.logger.info("Решение капчи отключено (--no-captcha).")


        # Менеджер браузера будет создан в __enter__
        self.browser_manager: Optional[BrowserManager] = None
        # Компоненты будут созданы в _initialize_components
        self.navigator: Optional[Navigator] = None
        self.form_handler: Optional[FormHandler] = None
        self.captcha_handler: Optional[CaptchaHandler] = None
        self.results_processor: Optional[ResultsProcessor] = None
        self.details_parser: Optional[DetailsParser] = None
        self.saver: Optional[DataSaver] = None

        # Заменяем self.page на self.driver
        self.driver: Optional[WebDriver] = None

        self.results: List[Dict] = []
        # Используем общее множество для дедупликации между запусками/категориями
        self.processed_urls: Set[str] = set()

        self.logger.debug("CpaQuebecParser инициализирован (Selenium).")

    def _initialize_components(self):
        """Инициализирует все компоненты парсера после запуска драйвера Selenium."""
        if not self.browser_manager or not self.browser_manager.driver:
            self.logger.error("Невозможно инициализировать компоненты: драйвер Selenium не запущен.")
            raise RuntimeError("Драйвер Selenium не инициализирован.")

        self.driver = self.browser_manager.get_driver() # Получаем драйвер
        if not self.driver:
             self.logger.error("Не удалось получить активный драйвер от BrowserManager.")
             raise RuntimeError("Не удалось получить драйвер Selenium.")

        self.logger.debug("Инициализация компонентов парсера (Selenium)...")

        # --- Создаем адаптированные компоненты ---
        # Создаем директорию для скриншотов, если ее нет
        self.screenshots_dir = os.path.join(DEBUG_DIR, "screenshots_selenium")
        os.makedirs(self.screenshots_dir, exist_ok=True)
        self.html_dir = os.path.join(DEBUG_DIR, "html_selenium")
        os.makedirs(self.html_dir, exist_ok=True)

        # Передаем актуальные параметры, включая self.debug и пути для отладки
        # Передаем element_timeout в Navigator для явных ожиданий
        self.navigator = Navigator(self.driver, self.logger, self.debug, self.html_dir, self.screenshots_dir, self.element_timeout)
        # Убираем navigator из аргументов FormHandler
        self.form_handler = FormHandler(self.driver, self.logger, self.debug)
        self.captcha_handler = CaptchaHandler(self.driver, self.logger, self.captcha_solver, self.navigator, self.debug) # captcha_solver может быть None
        self.details_parser = DetailsParser(self.driver, self.logger, self.navigator, self.debug)
        # Передаем ОБЩЕЕ множество processed_urls
        self.results_processor = ResultsProcessor(self.driver, self.logger, self.navigator, self.details_parser, self.processed_urls, self.debug)
        self.saver = DataSaver(self.logger, self.output_dir)
        # ----------------------------------------------------------------------------------------

        self.logger.debug("Компоненты парсера инициализированы (Selenium).")

        # --- Используем Navigator для сохранения отладочной информации ---
        self.navigator.save_debug_info('after_initialize_components')

    def __enter__(self):
        """Запускает драйвер и инициализирует компоненты."""
        self.logger.info("Запуск менеджера браузера (Selenium)...")
        # Передаем таймауты, полученные в __init__, в BrowserManager
        self.browser_manager = BrowserManager(
            headless=self.headless,
            logger=self.logger,
            page_load_timeout=self.page_load_timeout, # Используем сохраненное значение
            implicit_wait=self.wait_timeout       # Используем сохраненное значение
        )
        try:
            self.browser_manager.__enter__() # Запускаем драйвер
            self._initialize_components() # Инициализируем компоненты
            self.logger.info("Менеджер браузера запущен, компоненты инициализированы (Selenium).")
            return self
        except Exception as e:
            self.logger.critical(f"Ошибка при запуске драйвера или инициализации компонентов (Selenium): {e}", exc_info=True)
            # Убедимся, что браузер закрыт при ошибке на этом этапе
            if self.browser_manager:
                 self.browser_manager.__exit__(type(e), e, e.__traceback__)
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Останавливает драйвер."""
        self.logger.info("Остановка менеджера браузера (Selenium)...")
        if self.browser_manager:
            self.browser_manager.__exit__(exc_type, exc_val, exc_tb)
            self.logger.info("Менеджер браузера остановлен (Selenium).")
        # Здесь можно добавить сохранение необработанных результатов или логов

    # Обновленный метод parse с новым аргументом category_to_parse
    @log_duration("parse")
    def parse(self,
              last_name: str = "",
              first_name: str = "",
              region: str = "",
              city: str = "",
              by_category: bool = False,
              category_to_parse: Optional[str] = None, # Новый аргумент
              get_details: bool = True
             ) -> List[Dict]:
        """
        Основной метод парсинга. Может работать по критериям поиска,
        по всем категориям или по одной указанной категории.
        """
        search_terms = {"last_name": last_name, "first_name": first_name, "region": region, "city": city}
        search_terms = {k: v for k, v in search_terms.items() if v} # Фильтруем пустые значения

        self.logger.info(f"Начало парсинга (Selenium). Get details: {get_details}, By Category: {by_category}, Single Category: {category_to_parse}, Search: {search_terms}")
        self.results = [] # Очищаем результаты перед новым запуском

        if not all([self.navigator, self.form_handler, self.captcha_handler, self.results_processor, self.saver]):
            self.logger.critical("Парсер не инициализирован должным образом. Компоненты отсутствуют.")
            return []

        try:
            # Новый приоритет: парсинг одной категории
            if category_to_parse:
                self.logger.info(f"Запуск парсинга одной категории: '{category_to_parse}'...")
                parse_results = self._parse_single_category(category_to_parse, get_details)
                self.results.extend(parse_results)
            elif by_category:
                self.logger.info("Запуск парсинга по всем категориям...")
                parse_results = self._parse_by_categories(CLIENT_CATEGORIES_TO_PARSE, get_details)
                self.results.extend(parse_results)
            elif search_terms:
                self.logger.info(f"Запуск поиска CPA по критериям: {search_terms}...")
                parse_results = self._search_cpa(search_terms, get_details)
                self.results.extend(parse_results)
            else:
                 self.logger.info("Не указаны ни категория, ни критерии поиска. Запускаем парсинг по всем категориям (Clients served).")
                 parse_results = self._parse_by_categories(CLIENT_CATEGORIES_TO_PARSE, get_details)
                 self.results.extend(parse_results)

        except Exception as e:
             self.logger.critical(f"Непредвиденная ошибка в основном методе parse: {e}", exc_info=True)
             if self.results:
                 self.logger.info("Попытка сохранить частично собранные результаты...")
                 self.saver.save(self.results, filename_suffix="_partial_on_error")
             self.results = []
        finally:
            self.logger.info(f"Парсинг завершен. Собрано {len(self.results)} уникальных записей.")
            if self.results:
                 self.saver.save(self.results)

        return self.results

    # Восстановленный и адаптированный метод парсинга по категориям
    @log_duration("_parse_by_categories")
    def _parse_by_categories(self, categories: List[str], get_details: bool) -> List[Dict]:
        """Парсит данные по списку категорий."""
        all_category_results = []
        target_categories = categories if categories else CLIENT_CATEGORIES_TO_PARSE
        if not target_categories:
            self.logger.warning("Список категорий для парсинга пуст.")
            return []

        # Проверка компонентов еще раз (на всякий случай)
        if not all([self.navigator, self.form_handler, self.captcha_handler, self.results_processor]):
            self.logger.error("Компоненты не инициализированы перед парсингом по категориям.")
            return []

        for category in target_categories:
            self.logger.info(f"--- Начало парсинга категории: '{category}' ---")
            retries = 0
            category_results = []
            success = False
            while retries < MAX_RETRY_ATTEMPTS and not success:
                if retries > 0:
                     self.logger.warning(f"Повторная попытка {retries + 1}/{MAX_RETRY_ATTEMPTS} для категории '{category}'.")
                     random_sleep(2 * retries, 3 * retries, f"перед retry {retries+1} для категории {category}")
                try:
                    # --- Навигация и форма ---
                    if not self.navigator.goto(BASE_URL):
                        raise ConnectionError(f"Не удалось загрузить базовый URL {BASE_URL} для категории '{category}'.")
                    self.navigator.save_debug_info(f"cat_{category}_after_goto")

                    self.form_handler.clear()
                    self.navigator.save_debug_info(f"cat_{category}_after_clear")
                    random_sleep(0.5, 1.0, "после очистки формы")

                    if not self.form_handler.select_category(category):
                        self.logger.error(f"Не удалось выбрать категорию '{category}'. Пропускаем.")
                        # Не повторяем попытку для этой категории, если не удалось выбрать
                        retries = MAX_RETRY_ATTEMPTS # Прервать цикл retries
                        continue # Перейти к следующей категории
                    self.logger.info(f"Категория '{category}' выбрана.")
                    self.navigator.save_debug_info(f"cat_{category}_after_select")
                    # Увеличиваем паузу после выбора категории
                    random_sleep(2.0, 3.5, "после выбора категории")

                    # --- Капча ---
                    self.logger.info("Переходим к обработке капчи...")
                    captcha_result = self.captcha_handler.handle()
                    if captcha_result is False: # Явная ошибка решения
                        self.logger.error(f"Ошибка при обработке капчи для категории '{category}'. Пропускаем категорию.")
                        retries = MAX_RETRY_ATTEMPTS
                        continue
                    elif captcha_result is True:
                        self.logger.info("Капча успешно обработана.")
                        self.navigator.save_debug_info(f"cat_{category}_after_captcha")
                        # Увеличиваем паузу после решения капчи
                        random_sleep(3, 5, "после обработки капчи")
                    else:
                        self.logger.info("Капча не обнаружена или не требует решения.")
                        # Все равно делаем паузу для стабильности
                        random_sleep(1.5, 2.5, "после проверки наличия капчи")

                    # --- Поиск и результаты ---
                    self.logger.info("Подготовка к нажатию кнопки поиска...")
                    # Пауза перед нажатием кнопки поиска
                    random_sleep(1, 2, "перед нажатием кнопки поиска")
                    self.logger.info("Нажатие кнопки поиска...")
                    if not self.form_handler.click_search_button():
                        # Если кнопка не нажалась, возможно, стоит попробовать еще раз
                        raise RuntimeError("Не удалось нажать кнопку поиска.")
                    self.logger.info("Кнопка поиска нажата, ожидание и обработка результатов...")
                    # Увеличиваем паузу после нажатия кнопки поиска
                    random_sleep(3, 6, "после нажатия поиска, перед обработкой результатов")
                    self.navigator.save_debug_info(f"cat_{category}_before_process")

                    # Используем results_processor для сбора данных со всех страниц
                    page_results = self.results_processor.process_pages(get_details)
                    self.logger.info(f"Категория '{category}': обработано {len(page_results)} новых записей.")
                    self.navigator.save_debug_info(f"cat_{category}_after_process")

                    category_results = page_results
                    success = True # Успешно обработали, выходим из цикла retries

                except (ConnectionError, TimeoutException, StaleElementReferenceException, WebDriverException) as err:
                    self.logger.warning(f"Ошибка сети/таймаута/элемента при обработке категории '{category}' (попытка {retries + 1}): {err}")
                    self.navigator.save_debug_info(f"cat_{category}_error_retry_{retries+1}")
                    retries += 1
                except Exception as e:
                    self.logger.error(f"Непредвиденная ошибка при обработке категории '{category}' (попытка {retries + 1}): {e}", exc_info=self.debug)
                    self.navigator.save_debug_info(f"cat_{category}_unexpected_error_retry_{retries+1}")
                    retries += 1

            if not success:
                 self.logger.error(f"Не удалось обработать категорию '{category}' после {MAX_RETRY_ATTEMPTS} попыток.")

            # Добавляем результаты категории (даже если пустые) к общему списку
            all_category_results.extend(category_results)
            self.logger.info(f"--- Завершение парсинга категории: '{category}'. Собрано записей: {len(category_results)} ---")
            # Пауза между категориями
            random_sleep(2, 5, f"между категориями, после {category}")

        self.logger.info(f"Завершен парсинг по категориям. Собрано всего: {len(all_category_results)} записей.")
        return all_category_results

    # Восстановленный и адаптированный метод поиска по критериям
    @log_duration("_search_cpa")
    def _search_cpa(self, search_terms: Dict[str, str], get_details: bool) -> List[Dict]:
        """Ищет CPA по заданным критериям."""
        self.logger.info(f"--- Начало поиска CPA по критериям: {search_terms} ---")

        # Проверка компонентов
        if not all([self.navigator, self.form_handler, self.captcha_handler, self.results_processor]):
            self.logger.error("Компоненты не инициализированы перед поиском CPA.")
            return []

        retries = 0
        search_results = []
        success = False
        while retries < MAX_RETRY_ATTEMPTS and not success:
            if retries > 0:
                 self.logger.warning(f"Повторная попытка {retries + 1}/{MAX_RETRY_ATTEMPTS} для поиска CPA ({search_terms}).")
                 random_sleep(2 * retries, 3 * retries, f"перед retry {retries+1} для поиска CPA")
            try:
                # --- Навигация и форма ---
                if not self.navigator.goto(BASE_URL):
                     raise ConnectionError(f"Не удалось загрузить базовый URL {BASE_URL} для поиска.")
                self.navigator.save_debug_info("search_cpa_after_goto")

                self.form_handler.clear()
                self.navigator.save_debug_info("search_cpa_after_clear")
                random_sleep(0.5, 1.0, "после очистки формы")

                # Заполняем только если есть критерии
                if search_terms:
                    # Используем ** для распаковки словаря
                    if not self.form_handler.fill_search_criteria(**search_terms):
                        self.logger.error("Не удалось заполнить критерии поиска. Поиск невозможен.")
                        return [] # Прерываем, если не удалось заполнить
                    self.logger.info(f"Критерии поиска {search_terms} заполнены.")
                    self.navigator.save_debug_info("search_cpa_after_fill")
                    # Увеличиваем паузу после заполнения формы
                    random_sleep(2.0, 3.0, "после заполнения критериев")
                else:
                    self.logger.info("Поиск CPA без указания критериев.")
                    # Пауза для стабильности
                    random_sleep(1.0, 2.0, "после проверки отсутствия критериев")

                # --- Капча ---
                self.logger.info("Переходим к обработке капчи...")
                captcha_result = self.captcha_handler.handle()
                if captcha_result is False:
                    self.logger.error("Ошибка при обработке капчи для поиска CPA. Прерывание.")
                    return [] # Прерываем поиск, если капча не решена
                elif captcha_result is True:
                    self.logger.info("Капча успешно обработана.")
                    self.navigator.save_debug_info("search_cpa_after_captcha")
                    # Увеличиваем паузу после решения капчи
                    random_sleep(3, 5, "после обработки капчи")
                else:
                    self.logger.info("Капча не обнаружена или не требует решения.")
                    # Всё равно делаем паузу для стабильности
                    random_sleep(1.5, 2.5, "после проверки наличия капчи")

                # --- Поиск и результаты ---
                self.logger.info("Подготовка к нажатию кнопки поиска...")
                # Пауза перед нажатием кнопки поиска
                random_sleep(1, 2, "перед нажатием кнопки поиска")
                self.logger.info("Нажатие кнопки поиска...")
                if not self.form_handler.click_search_button():
                     raise RuntimeError("Не удалось нажать кнопку поиска.")
                self.logger.info("Кнопка поиска нажата, ожидание и обработка результатов...")
                # Увеличиваем паузу после нажатия кнопки поиска
                random_sleep(3, 6, "после нажатия поиска, перед обработкой результатов")
                self.navigator.save_debug_info("search_cpa_before_process")

                search_results = self.results_processor.process_pages(get_details)
                self.logger.info(f"Поиск CPA ({search_terms}): обработано {len(search_results)} новых записей.")
                self.navigator.save_debug_info("search_cpa_after_process")
                success = True # Успешно

            except (ConnectionError, TimeoutException, StaleElementReferenceException, WebDriverException) as err:
                 self.logger.warning(f"Ошибка сети/таймаута/элемента при поиске CPA ({search_terms}, попытка {retries + 1}): {err}")
                 self.navigator.save_debug_info(f"search_cpa_error_retry_{retries+1}")
                 retries += 1
            except Exception as e:
                 self.logger.error(f"Непредвиденная ошибка при поиске CPA ({search_terms}, попытка {retries + 1}): {e}", exc_info=self.debug)
                 self.navigator.save_debug_info(f"search_cpa_unexpected_error_retry_{retries+1}")
                 retries += 1

        if not success:
             self.logger.error(f"Не удалось выполнить поиск CPA по критериям {search_terms} после {MAX_RETRY_ATTEMPTS} попыток.")

        self.logger.info(f"--- Завершен поиск CPA. Собрано {len(search_results)} записей. ---")
        return search_results

    # Добавляю метод для парсинга одной категории клиентов
    @log_duration("_parse_single_category")
    def _parse_single_category(self, category: str, get_details: bool) -> List[Dict]:
        """Парсит данные для одной категории клиентов, используя общую логику _parse_by_categories."""
        return self._parse_by_categories([category], get_details)
