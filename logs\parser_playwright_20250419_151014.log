2025-04-19 15:10:14,306 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_151014.log
2025-04-19 15:10:14,315 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 15:10:14,315 - CpaQuebecParser - INFO - ==================================================
2025-04-19 15:10:14,315 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 15:10:14,315 - C<PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-19 15:10:14,316 - CpaQuebecParser - INFO - ==================================================
2025-04-19 15:10:14,316 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-19 15:10:25,958 - CpaQuebecParser - DEBUG - Пауза на 1.78 сек. (после driver.get)
2025-04-19 15:10:27,857 - CpaQuebecParser - DEBUG - Пауза на 0.70 сек. (после scroll к кнопке cookie)
2025-04-19 15:11:09,886 - CpaQuebecParser - DEBUG - Пауза на 0.77 сек. (после клика Reset (XPath))
2025-04-19 15:11:12,726 - CpaQuebecParser - DEBUG - Пауза на 0.85 сек. (после очистки формы)
2025-04-19 15:11:17,603 - CpaQuebecParser - DEBUG - Пауза на 3.00 сек. (между категориями, после Individuals)
2025-04-19 15:11:20,640 - CpaQuebecParser - DEBUG - Пауза на 2.72 сек. (перед retry 2 для категории Large companies)
2025-04-19 15:11:23,414 - CpaQuebecParser - DEBUG - Пауза на 5.04 сек. (перед retry 3 для категории Large companies)
2025-04-19 15:11:23,993 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
