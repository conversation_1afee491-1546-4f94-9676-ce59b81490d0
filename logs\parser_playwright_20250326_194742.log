2025-03-26 19:47:42,001 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_194742.log
2025-03-26 19:47:42,004 - C<PERSON><PERSON><PERSON>becParser - INFO - ==================================================
2025-03-26 19:47:42,004 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-03-26 19:47:42,004 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 30, 'page_timeout': 60, 'get_details': True, 'by_category': False}
2025-03-26 19:47:42,004 - CpaQuebecParser - INFO - ==================================================
2025-03-26 19:47:42,004 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-03-26 19:47:42,005 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_194742.log
2025-03-26 19:47:42,005 - CpaQuebecParser - INFO - Инициализация парсера CPA Quebec (Playwright)
2025-03-26 19:47:42,006 - CpaQuebecParser - WARNING - API ключ для 2Captcha не найден. Решение капчи будет пропущено.
2025-03-26 19:47:42,006 - CpaQuebecParser - INFO - Запуск браузера Chromium (Headless: True) через Playwright...
2025-03-26 19:47:43,379 - CpaQuebecParser - INFO - Браузер Chromium запущен.
2025-03-26 19:47:43,390 - CpaQuebecParser - INFO - Используется User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-03-26 19:47:43,427 - CpaQuebecParser - INFO - Контекст браузера создан.
2025-03-26 19:47:43,770 - CpaQuebecParser - INFO - Новая страница создана.
2025-03-26 19:47:43,780 - CpaQuebecParser - INFO - Добавлен init script для скрытия navigator.webdriver.
2025-03-26 19:47:43,780 - CpaQuebecParser - INFO - ============================== ЗАПУСК ПАРСИНГА (Playwright) ==============================
2025-03-26 19:47:43,781 - CpaQuebecParser - INFO - Режим: Поиск по заданным критериям.
2025-03-26 19:47:43,781 - CpaQuebecParser - INFO - Запуск поиска CPA по критериям (Playwright): LName='', FName='', Region='', City=''
2025-03-26 19:47:43,781 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:47:44,382 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:47:44,384 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:47:44,693 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_194744.png
2025-03-26 19:47:44,698 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_194744.html
2025-03-26 19:47:44,698 - CpaQuebecParser - WARNING - Поиск не дал результатов или произошла ошибка.
2025-03-26 19:47:44,698 - CpaQuebecParser - WARNING - Нет данных для сохранения.
2025-03-26 19:47:44,698 - CpaQuebecParser - INFO - Общее время парсинга (Playwright): 0.92 секунд.
2025-03-26 19:47:44,698 - CpaQuebecParser - INFO - ============================== ЗАВЕРШЕНИЕ ПАРСИНГА (Playwright) ==============================
2025-03-26 19:47:44,699 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-03-26 19:47:44,699 - CpaQuebecParser - INFO - Закрытие браузера и Playwright...
2025-03-26 19:47:44,798 - CpaQuebecParser - INFO - Браузер Playwright закрыт.
2025-03-26 19:47:44,822 - CpaQuebecParser - INFO - Playwright остановлен.
2025-03-26 19:47:44,822 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
