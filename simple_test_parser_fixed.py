#!/usr/bin/env python3
"""
Исправленный тестовый парсер CPA Quebec Directory - с выбором категории "SMEs"
"""
import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path

import aiohttp
from dotenv import load_dotenv
from playwright.async_api import async_playwright

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("test_quebec_parser")

# Константы
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"
OUTPUT_DIR = Path("output")
ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")

# Глобальная сессия для Anti-Captcha
anticaptcha_session = None

async def get_anticaptcha_session():
    """Получает или создает сессию для Anti-Captcha API"""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session

async def anticaptcha_request(method: str, **payload):
    """Выполняет запрос к Anti-Captcha API"""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()
    
    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY
    
    logger.debug(f"Anti-Captcha request: {method}, payload_keys: {list(payload.keys())}")
    try:
        async with session.post(url, json=payload, timeout=30) as resp:
            response_json = await resp.json()
            logger.debug(f"Anti-Captcha response ({method}): {response_json}")
            resp.raise_for_status()
            return response_json
    except Exception as e:
        logger.error(f"Anti-Captcha API error ({method}): {e}")
        return {"errorId": 1, "errorCode": "REQUEST_ERROR", "errorDescription": str(e)}

async def solve_captcha_simple(page):
    """Простое решение капчи через Anti-Captcha"""
    if not ANTICAPTCHA_KEY:
        logger.warning("ANTICAPTCHA_API_KEY не установлен, используем ручное решение")
        input("Решите капчу вручную и нажмите Enter для продолжения...")
        return True
    
    logger.info("Решаем капчу через Anti-Captcha...")
    
    # Получаем sitekey
    sitekey = await page.evaluate("""
        () => {
            const recaptchaDiv = document.querySelector('.g-recaptcha');
            if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                return recaptchaDiv.getAttribute('data-sitekey');
            }
            const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
            for (const iframe of iframes) {
                const src = iframe.src;
                const match = src.match(/k=([^&]+)/);
                if (match) {
                    return match[1];
                }
            }
            return null;
        }
    """)
    
    if not sitekey:
        logger.error("Не удалось найти sitekey")
        return False
    
    logger.info(f"Найден sitekey: {sitekey[:10]}...")
    
    # Создаем задачу
    task_payload = {
        "task": {
            "type": "NoCaptchaTaskProxyless",
            "websiteURL": BASE_URL,
            "websiteKey": sitekey,
        }
    }
    
    response = await anticaptcha_request("createTask", **task_payload)
    if response.get("errorId", 0) != 0:
        logger.error(f"Ошибка создания задачи: {response.get('errorDescription')}")
        return False
    
    task_id = response.get("taskId")
    logger.info(f"Задача создана, ID: {task_id}")
    
    # Ждем результат
    await asyncio.sleep(10)
    
    for attempt in range(50):
        response = await anticaptcha_request("getTaskResult", taskId=task_id)
        
        if response.get("errorId", 0) != 0:
            logger.error(f"Ошибка получения результата: {response.get('errorDescription')}")
            return False
        
        if response.get("status") == "ready":
            solution = response.get("solution")
            token = solution.get("gRecaptchaResponse") if solution else None
            
            if token and len(token) > 50:
                logger.info(f"Получен токен длиной {len(token)} символов")
                
                # Вставляем токен
                await page.evaluate(f"""
                    (token) => {{
                        let response_el = document.querySelector('#g-recaptcha-response');
                        if (!response_el) {{
                            response_el = document.createElement('textarea');
                            response_el.id = 'g-recaptcha-response';
                            response_el.name = 'g-recaptcha-response';
                            response_el.style.display = 'none';
                            document.body.appendChild(response_el);
                        }}
                        response_el.value = token;
                        
                        // Активируем кнопки
                        const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                        buttons.forEach(btn => {{
                            btn.disabled = false;
                            btn.removeAttribute('disabled');
                        }});
                        
                        console.log('Токен вставлен, кнопки активированы');
                    }}
                """, token)
                
                await page.wait_for_timeout(2000)
                logger.info("Капча решена успешно")
                return True
        
        elif response.get("status") == "processing":
            logger.debug(f"Задача обрабатывается... (попытка {attempt + 1})")
        
        await asyncio.sleep(5)
    
    logger.error("Таймаут решения капчи")
    return False

async def select_category(page, category_name="SMEs"):
    """Выбирает категорию на странице"""
    logger.info(f"Выбираем категорию: {category_name}")
    
    # Снимаем все чекбоксы
    await page.evaluate("""
        () => {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                }
            });
        }
    """)
    
    await page.wait_for_timeout(2000)
    
    # Выбираем нужную категорию
    category_selected = await page.evaluate(f"""
        (categoryName) => {{
            // Ищем по тексту метки
            const labels = document.querySelectorAll('label');
            for (const label of labels) {{
                if (label.textContent.includes(categoryName)) {{
                    console.log('Найдена метка для категории:', categoryName);
                    
                    if (label.htmlFor) {{
                        const checkbox = document.getElementById(label.htmlFor);
                        if (checkbox) {{
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('Отмечен чекбокс через ID:', checkbox.id);
                            return true;
                        }}
                    }}
                    
                    const checkbox = label.querySelector('input[type="checkbox"]');
                    if (checkbox) {{
                        checkbox.checked = true;
                        checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        console.log('Отмечен чекбокс внутри метки');
                        return true;
                    }}
                }}
            }}
            
            // Альтернативный поиск
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of checkboxes) {{
                if ((checkbox.id && checkbox.id.includes(categoryName)) ||
                    (checkbox.name && checkbox.name.includes(categoryName))) {{
                    checkbox.checked = true;
                    checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    console.log('Отмечен чекбокс по ID/name:', checkbox.id || checkbox.name);
                    return true;
                }}
            }}
            
            return false;
        }}
    """, category_name)
    
    if category_selected:
        logger.info(f"Категория '{category_name}' успешно выбрана")
        await page.wait_for_timeout(3000)
        return True
    else:
        logger.error(f"Не удалось выбрать категорию '{category_name}'")
        return False

async def click_search_button(page):
    """Улучшенная функция клика на кнопку поиска"""
    logger.info("Ищем и кликаем кнопку поиска...")
    
    # Расширенный список селекторов
    selectors = [
        "text=Rechercher",  # Французская версия
        "text=Search",  # Английская версия
        "button:has-text('Rechercher')",
        "button:has-text('Search')",
        "input[value='Rechercher']",
        "input[value='Search']",
        "button[type='submit']:has-text('Rechercher')",
        "button[type='submit']:has-text('Search')",
        "input[type='submit'][value*='Rechercher']",
        "input[type='submit'][value*='Search']",
        "button[type='submit']:not(:has-text('Reset')):not(:has-text('Réinitialiser'))",
        "input[type='submit']:not([value*='Reset']):not([value*='Réinitialiser'])"
    ]
    
    for i, selector in enumerate(selectors):
        try:
            logger.info(f"Пробуем селектор #{i+1}: {selector}")
            button = page.locator(selector).first
            button_count = await button.count()
            
            if button_count > 0:
                # Получаем текст кнопки
                try:
                    button_text = await button.inner_text() if await button.is_visible() else ""
                    button_value = await button.get_attribute("value") or ""
                    combined_text = f"{button_text} {button_value}".strip()
                except:
                    combined_text = "Unknown"
                
                # Проверяем, что это не кнопка сброса
                if any(word in combined_text.lower() for word in ["reset", "réinitialiser", "clear"]):
                    logger.info(f"Пропускаем кнопку сброса: '{combined_text}'")
                    continue
                
                logger.info(f"Найдена кнопка: '{combined_text}' с селектором: {selector}")
                
                # Проверяем видимость
                is_visible = await button.is_visible()
                is_enabled = await button.is_enabled()
                
                logger.info(f"Состояние кнопки: visible={is_visible}, enabled={is_enabled}")
                
                if is_visible and is_enabled:
                    # Получаем позицию до прокрутки
                    initial_position = await button.bounding_box()
                    logger.info(f"Позиция кнопки до прокрутки: {initial_position}")
                    
                    # Прокручиваем к кнопке
                    await button.scroll_into_view_if_needed()
                    await page.wait_for_timeout(1000)
                    
                    # Проверяем позицию после прокрутки
                    new_position = await button.bounding_box()
                    logger.info(f"Позиция после прокрутки: {new_position}")
                    
                    # Проверяем, что кнопка все еще видима
                    is_still_visible = await button.is_visible()
                    is_in_viewport = await button.is_in_viewport()
                    
                    logger.info(f"После прокрутки: visible={is_still_visible}, in_viewport={is_in_viewport}")
                    
                    # Пробуем разные методы клика
                    click_success = False
                    
                    if is_still_visible and is_in_viewport:
                        # Стандартный клик Playwright
                        try:
                            logger.info("Пробуем стандартный клик Playwright...")
                            await button.click(timeout=5000)
                            logger.info("Стандартный клик успешен")
                            click_success = True
                        except Exception as e:
                            logger.warning(f"Стандартный клик не удался: {e}")
                            
                            # Принудительный клик
                            try:
                                logger.info("Пробуем принудительный клик...")
                                await button.click(force=True, timeout=5000)
                                logger.info("Принудительный клик успешен")
                                click_success = True
                            except Exception as e2:
                                logger.warning(f"Принудительный клик не удался: {e2}")
                    
                    # JavaScript клик как fallback
                    if not click_success:
                        logger.info("Пробуем JavaScript клик...")
                        js_click_result = await page.evaluate(f"""
                            () => {{
                                const buttons = Array.from(document.querySelectorAll('button, input[type="submit"]'));
                                const targetButton = buttons.find(btn => 
                                    (btn.textContent || btn.value || '').includes('{combined_text}')
                                );
                                if (targetButton) {{
                                    // Принудительно активируем кнопку
                                    targetButton.disabled = false;
                                    targetButton.style.display = 'block';
                                    targetButton.style.visibility = 'visible';
                                    targetButton.click();
                                    console.log('JavaScript клик выполнен');
                                    return true;
                                }}
                                return false;
                            }}
                        """)
                        
                        if js_click_result:
                            logger.info("JavaScript клик успешен")
                            click_success = True
                        else:
                            logger.error("JavaScript клик не удался")
                    
                    if click_success:
                        logger.info("Кнопка поиска нажата, ожидаем навигацию...")
                        
                        # Ждем навигации
                        try:
                            await page.wait_for_navigation(timeout=30000, wait_until="domcontentloaded")
                            logger.info("Навигация завершена успешно")
                        except Exception as nav_error:
                            logger.warning(f"Навигация не произошла: {nav_error}")
                            # Проверяем изменение URL
                            await page.wait_for_timeout(5000)
                            current_url = page.url
                            if "search" in current_url.lower() or current_url != BASE_URL:
                                logger.info(f"URL изменился на: {current_url}")
                            else:
                                logger.warning("URL не изменился")
                        
                        return True
                
                else:
                    logger.warning(f"Кнопка не видима или неактивна")
                    # Пробуем JavaScript клик для невидимой кнопки
                    logger.info("Пробуем JavaScript клик для невидимой кнопки...")
                    js_click_result = await page.evaluate(f"""
                        () => {{
                            const buttons = Array.from(document.querySelectorAll('button, input[type="submit"]'));
                            const targetButton = buttons.find(btn => 
                                (btn.textContent || btn.value || '').includes('{combined_text}')
                            );
                            if (targetButton) {{
                                targetButton.disabled = false;
                                targetButton.style.display = 'block';
                                targetButton.style.visibility = 'visible';
                                targetButton.click();
                                console.log('JavaScript клик на невидимую кнопку выполнен');
                                return true;
                            }}
                            return false;
                        }}
                    """)
                    
                    if js_click_result:
                        logger.info("JavaScript клик на невидимую кнопку успешен")
                        return True
        
        except Exception as e:
            logger.debug(f"Ошибка с селектором {selector}: {e}")
            continue
    
    # Последняя попытка - любая submit кнопка
    logger.info("Последняя попытка - JavaScript клик на любую submit кнопку...")
    js_click_success = await page.evaluate("""
        () => {
            const buttons = Array.from(document.querySelectorAll('button[type="submit"], input[type="submit"]'));
            
            for (const btn of buttons) {
                const text = (btn.textContent || btn.value || '').toLowerCase();
                
                if (text.includes('reset') || text.includes('réinitialiser') || text.includes('clear')) {
                    continue;
                }
                
                if (btn.offsetParent !== null && !btn.disabled) {
                    console.log('JavaScript клик на кнопку:', btn.textContent || btn.value);
                    btn.click();
                    return true;
                }
            }
            
            return false;
        }
    """)
    
    if js_click_success:
        logger.info("Финальный JavaScript клик успешен")
        return True
    else:
        logger.error("Не удалось найти и нажать кнопку поиска")
        return False

async def run_test_parser():
    """Запускает тестовый парсер"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(viewport={"width": 1280, "height": 800})
        page = await context.new_page()
        
        try:
            logger.info("=== Исправленный тестовый парсер CPA Quebec (категория SMEs) ===")
            logger.info(f"Переходим на {BASE_URL}")
            
            # Переходим на страницу
            await page.goto(BASE_URL, timeout=60000)
            await page.wait_for_load_state("domcontentloaded")
            
            # Закрываем cookie banner
            try:
                cookie_banner = page.locator("text=Tout refuser")
                if await cookie_banner.is_visible(timeout=5000):
                    await cookie_banner.click()
                    logger.info("Cookie banner закрыт")
                    await page.wait_for_timeout(2000)
            except:
                pass
            
            # Выбираем категорию
            if not await select_category(page, "SMEs"):
                logger.error("Не удалось выбрать категорию")
                return
            
            # Решаем капчу
            logger.info("Решаем капчу...")
            if not await solve_captcha_simple(page):
                logger.error("Не удалось решить капчу")
                return
            
            # Ждем стабилизации
            logger.info("Ждем стабилизации после решения капчи...")
            await page.wait_for_timeout(5000)
            
            # Кликаем кнопку поиска
            if not await click_search_button(page):
                logger.error("Не удалось нажать кнопку поиска")
                return
            
            # Анализируем результаты
            await page.wait_for_load_state("domcontentloaded")
            await page.wait_for_timeout(5000)
            
            current_url = page.url
            logger.info(f"URL после поиска: {current_url}")
            
            # Получаем информацию о результатах
            results_info = await page.evaluate("""
                () => {
                    const pageText = document.body.innerText;
                    const resultMatches = pageText.match(/(\\d+)\\s*-\\s*(\\d+)\\s*of\\s*(\\d+)\\s*result/i);
                    
                    return {
                        url: window.location.href,
                        pageText: pageText.substring(0, 1000),
                        hasResultsText: pageText.toLowerCase().includes('result'),
                        resultMatches: resultMatches,
                        emailCount: document.querySelectorAll('a[href^="mailto"]').length,
                        phoneCount: document.querySelectorAll('a[href^="tel"]').length
                    };
                }
            """)
            
            logger.info(f"Анализ результатов: {results_info}")
            
            if results_info['resultMatches']:
                logger.info(f"Найдены результаты: {results_info['resultMatches'][0]}")
                logger.info(f"Email ссылок: {results_info['emailCount']}")
                logger.info(f"Телефонных ссылок: {results_info['phoneCount']}")
            else:
                logger.warning("Результаты поиска не найдены или равны 0")
                logger.info("Текст страницы:")
                logger.info(results_info['pageText'])
            
            # Ждем для анализа
            input("\nПроанализируйте результаты в браузере и нажмите Enter...")
            
        except Exception as e:
            logger.error(f"Ошибка: {e}")
        finally:
            if anticaptcha_session and not anticaptcha_session.closed:
                await anticaptcha_session.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(run_test_parser()) 