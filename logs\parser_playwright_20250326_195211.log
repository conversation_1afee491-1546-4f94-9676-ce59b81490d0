2025-03-26 19:52:11,313 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195211.log
2025-03-26 19:52:11,316 - C<PERSON><PERSON>uebecParser - DEBUG - Режим отладки DEBUG включен.
2025-03-26 19:52:11,316 - C<PERSON>QuebecParser - INFO - ==================================================
2025-03-26 19:52:11,316 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-03-26 19:52:11,316 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 30, 'page_timeout': 60, 'get_details': True, 'by_category': True}
2025-03-26 19:52:11,317 - CpaQuebecParser - INFO - ==================================================
2025-03-26 19:52:11,317 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-03-26 19:52:11,317 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195211.log
2025-03-26 19:52:11,317 - CpaQuebecParser - INFO - Инициализация парсера CPA Quebec (Playwright)
2025-03-26 19:52:11,318 - CpaQuebecParser - WARNING - API ключ для 2Captcha не найден. Решение капчи будет пропущено.
2025-03-26 19:52:11,318 - CpaQuebecParser - INFO - Запуск браузера Chromium (Headless: False) через Playwright...
2025-03-26 19:52:13,323 - CpaQuebecParser - INFO - Браузер Chromium запущен.
2025-03-26 19:52:13,324 - CpaQuebecParser - INFO - Используется User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-03-26 19:52:13,429 - CpaQuebecParser - INFO - Контекст браузера создан.
2025-03-26 19:52:14,429 - CpaQuebecParser - INFO - Новая страница создана.
2025-03-26 19:52:14,452 - CpaQuebecParser - INFO - Добавлен init script для скрытия navigator.webdriver.
2025-03-26 19:52:14,453 - CpaQuebecParser - INFO - ============================== ЗАПУСК ПАРСИНГА (Playwright) ==============================
2025-03-26 19:52:14,454 - CpaQuebecParser - INFO - Режим: Парсинг по категориям клиентов.
2025-03-26 19:52:14,454 - CpaQuebecParser - INFO - Запуск парсинга по категориям (Playwright): Arts and culture organizations, Co-ownership syndicates, Elderly individuals, Executives, Farmers, Indigenous communities, Individuals, Large businesses, Medium-sized businesses, Non-profit organizations, Professionals, Self-employed workers, Small businesses, Sports organizations, Start-ups
2025-03-26 19:52:14,454 - CpaQuebecParser - INFO - --- Обработка категории: Arts and culture organizations (1/15) ---
2025-03-26 19:52:14,455 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:15,150 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:15,150 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:15,618 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195215.png
2025-03-26 19:52:15,624 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195215.html
2025-03-26 19:52:15,624 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Arts and culture organizations'. Пропускаем.
2025-03-26 19:52:15,624 - CpaQuebecParser - INFO - --- Обработка категории: Co-ownership syndicates (2/15) ---
2025-03-26 19:52:15,624 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:15,793 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:15,793 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:16,081 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195215.png
2025-03-26 19:52:16,087 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195215.html
2025-03-26 19:52:16,087 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Co-ownership syndicates'. Пропускаем.
2025-03-26 19:52:16,087 - CpaQuebecParser - INFO - --- Обработка категории: Elderly individuals (3/15) ---
2025-03-26 19:52:16,087 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:16,250 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:16,252 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:16,543 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195216.png
2025-03-26 19:52:16,546 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195216.html
2025-03-26 19:52:16,547 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Elderly individuals'. Пропускаем.
2025-03-26 19:52:16,547 - CpaQuebecParser - INFO - --- Обработка категории: Executives (4/15) ---
2025-03-26 19:52:16,547 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:16,875 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:16,877 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:17,201 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195216.png
2025-03-26 19:52:17,209 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195216.html
2025-03-26 19:52:17,209 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Executives'. Пропускаем.
2025-03-26 19:52:17,209 - CpaQuebecParser - INFO - --- Обработка категории: Farmers (5/15) ---
2025-03-26 19:52:17,209 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:17,374 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:17,375 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:17,658 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195217.png
2025-03-26 19:52:17,666 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195217.html
2025-03-26 19:52:17,666 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Farmers'. Пропускаем.
2025-03-26 19:52:17,666 - CpaQuebecParser - INFO - --- Обработка категории: Indigenous communities (6/15) ---
2025-03-26 19:52:17,667 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:17,824 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:17,824 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:18,152 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195217.png
2025-03-26 19:52:18,157 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195217.html
2025-03-26 19:52:18,157 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Indigenous communities'. Пропускаем.
2025-03-26 19:52:18,158 - CpaQuebecParser - INFO - --- Обработка категории: Individuals (7/15) ---
2025-03-26 19:52:18,158 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:18,321 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:18,321 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:18,590 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195218.png
2025-03-26 19:52:18,813 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195218.html
2025-03-26 19:52:18,814 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Individuals'. Пропускаем.
2025-03-26 19:52:18,814 - CpaQuebecParser - INFO - --- Обработка категории: Large businesses (8/15) ---
2025-03-26 19:52:18,814 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:18,979 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:18,980 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:19,288 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195218.png
2025-03-26 19:52:19,296 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195218.html
2025-03-26 19:52:19,296 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Large businesses'. Пропускаем.
2025-03-26 19:52:19,296 - CpaQuebecParser - INFO - --- Обработка категории: Medium-sized businesses (9/15) ---
2025-03-26 19:52:19,296 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:19,471 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:19,471 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:20,011 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195219.png
2025-03-26 19:52:20,018 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195219.html
2025-03-26 19:52:20,018 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Medium-sized businesses'. Пропускаем.
2025-03-26 19:52:20,018 - CpaQuebecParser - INFO - --- Обработка категории: Non-profit organizations (10/15) ---
2025-03-26 19:52:20,019 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:20,176 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:20,177 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:20,482 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195220.png
2025-03-26 19:52:20,487 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195220.html
2025-03-26 19:52:20,487 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Non-profit organizations'. Пропускаем.
2025-03-26 19:52:20,487 - CpaQuebecParser - INFO - --- Обработка категории: Professionals (11/15) ---
2025-03-26 19:52:20,487 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:20,646 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:20,646 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:20,914 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195220.png
2025-03-26 19:52:20,926 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195220.html
2025-03-26 19:52:20,927 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Professionals'. Пропускаем.
2025-03-26 19:52:20,927 - CpaQuebecParser - INFO - --- Обработка категории: Self-employed workers (12/15) ---
2025-03-26 19:52:20,927 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:21,095 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:21,095 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:21,473 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195221.png
2025-03-26 19:52:21,580 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195221.html
2025-03-26 19:52:21,581 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Self-employed workers'. Пропускаем.
2025-03-26 19:52:21,582 - CpaQuebecParser - INFO - --- Обработка категории: Small businesses (13/15) ---
2025-03-26 19:52:21,582 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:21,749 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:21,750 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:22,111 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195221.png
2025-03-26 19:52:22,118 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195221.html
2025-03-26 19:52:22,118 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Small businesses'. Пропускаем.
2025-03-26 19:52:22,118 - CpaQuebecParser - INFO - --- Обработка категории: Sports organizations (14/15) ---
2025-03-26 19:52:22,118 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:22,280 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:22,280 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:22,590 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195222.png
2025-03-26 19:52:22,596 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195222.html
2025-03-26 19:52:22,596 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Sports organizations'. Пропускаем.
2025-03-26 19:52:22,596 - CpaQuebecParser - INFO - --- Обработка категории: Start-ups (15/15) ---
2025-03-26 19:52:22,596 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:22,760 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:52:22,762 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:52:23,200 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195222.png
2025-03-26 19:52:23,214 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195222.html
2025-03-26 19:52:23,215 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Start-ups'. Пропускаем.
2025-03-26 19:52:23,215 - CpaQuebecParser - INFO - Парсинг по категориям завершен. Всего найдено 0 уникальных записей.
2025-03-26 19:52:23,215 - CpaQuebecParser - WARNING - Поиск не дал результатов или произошла ошибка.
2025-03-26 19:52:23,215 - CpaQuebecParser - WARNING - Нет данных для сохранения.
2025-03-26 19:52:23,215 - CpaQuebecParser - INFO - Общее время парсинга (Playwright): 8.76 секунд.
2025-03-26 19:52:23,218 - CpaQuebecParser - INFO - ============================== ЗАВЕРШЕНИЕ ПАРСИНГА (Playwright) ==============================
2025-03-26 19:52:23,219 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-03-26 19:52:23,219 - CpaQuebecParser - INFO - Закрытие браузера и Playwright...
2025-03-26 19:52:23,699 - CpaQuebecParser - INFO - Браузер Playwright закрыт.
2025-03-26 19:52:23,729 - CpaQuebecParser - INFO - Playwright остановлен.
2025-03-26 19:52:23,730 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
