запуим аврсинг
# Подробная документация по проекту CPA Quebec Parser

---

## Важное: поддержка Stagehand

В проект интегрирован [Stagehand](https://github.com/browserbase/stagehand) — прокси для Playwright, который позволяет:

- Запускать браузеры локально или в облаке Browserbase
- Масштабировать и изолировать браузеры
- Переключаться между режимами без изменения кода

Подробнее: [docs/stagehand_integration.md](stagehand_integration.md)

---

## Содержание
1. [Назначение проекта](#назначение-проекта)
2. [Архитектура проекта](#архитектура-проекта)
3. [Структура проекта](#структура-проекта)
4. [Основные компоненты](#основные-компоненты)
5. [Конфигурация](#конфигурация)
6. [Запуск парсера](#запуск-парсера)
7. [Процесс парсинга](#процесс-парсинга)
8. [Обработка капчи](#обработка-капчи)
9. [Обработка ошибок и повторные попытки](#обработка-ошибок-и-повторные-попытки)
10. [Сохранение данных](#сохранение-данных)
11. [Отладка и логирование](#отладка-и-логирование)
12. [Требования и зависимости](#требования-и-зависимости)
13. [Часто задаваемые вопросы](#часто-задаваемые-вопросы)

---

## Назначение проекта

CPA Quebec Parser — это автоматизированный инструмент для сбора данных из каталога бухгалтеров (CPA) провинции Квебек, Канада. Парсер предназначен для:

- Автоматического сбора информации о сертифицированных бухгалтерах
- Фильтрации результатов по различным критериям (категории клиентов, регион, имя и т.д.)
- Обхода защиты от автоматизированного сбора данных (reCAPTCHA)
- Сохранения собранных данных в структурированном формате (JSON, Excel)
- Предоставления подробной информации о каждом найденном специалисте

Парсер использует библиотеку Playwright для автоматизации браузера, что обеспечивает стабильную работу даже на сайтах с динамическим контентом и защитой от ботов.

---

## Архитектура проекта

Проект построен на модульной архитектуре, где каждый компонент отвечает за определенную функциональность:

- **Браузер**: запускается либо локально, либо через Stagehand (локальный прокси или облако Browserbase)
- **Оркестратор**: управляет браузером и остальными модулями
- Остальные компоненты — без изменений


1. **Оркестратор** (`CpaQuebecParser`) — центральный компонент, координирующий работу всех остальных модулей
2. **Менеджер браузера** (`BrowserManager`) — управление сессией браузера
3. **Навигатор** (`Navigator`) — перемещение по страницам сайта
4. **Обработчик форм** (`FormHandler`) — взаимодействие с формами поиска
5. **Обработчик капчи** (`CaptchaHandler`) — решение reCAPTCHA
6. **Обработчик результатов** (`ResultsProcessor`) — извлечение данных из результатов поиска
7. **Парсер деталей** (`DetailsParser`) — получение подробной информации о каждом CPA
8. **Сохранение данных** (`DataSaver`) — запись результатов в файлы

Такая архитектура обеспечивает:
- Высокую модульность и возможность замены отдельных компонентов
- Четкое разделение ответственности
- Простоту поддержки и расширения функциональности
- Возможность повторного использования компонентов в других проектах

---

## Структура проекта

```
quebec-new/
├── cpa_quebec/
│   ├── __init__.py
│   ├── main.py                  # Точка входа в приложение
│   ├── config.py                # Конфигурационные параметры
│   ├── utils.py                 # Вспомогательные функции и классы (CaptchaSolver, настройка логирования)
│   ├── playwright_parser/       # Модули парсера на Playwright
│   │   ├── __init__.py
│   │   ├── orchestrator.py      # Оркестратор парсинга
│   │   ├── browser.py           # Управление браузером
│   │   ├── navigation.py        # Навигация по сайту
│   │   ├── form_handler.py      # Работа с формами
│   │   ├── captcha.py           # Решение капчи
│   │   ├── results.py           # Обработка результатов
│   │   ├── details.py           # Парсинг детальной информации
│   │   └── saver.py             # Сохранение данных
├── output/                      # Директория для результатов
├── logs/                        # Директория для логов
├── debug_playwright/            # Директория для отладочных скриншотов
├── .env                         # Файл с секретами (API ключи)
├── requirements.txt             # Зависимости проекта
└── docs/                        # Документация
    └── CPA_Quebec_Parser_Documentation.md

# Примечание: фактический путь к проекту: `/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new`
```

---

## Основные компоненты

### main.py
Точка входа в приложение, отвечает за:
- Парсинг аргументов командной строки
- Настройку логирования
- Запуск парсера и обработку ошибок
- Возврат кодов завершения

```python
# Пример запуска
python3 cpa_quebec/main.py --by-category --visible --get-details --debug
```

### orchestrator.py (CpaQuebecParser)
Центральный компонент, который:
- Инициализирует и координирует все остальные компоненты
- Управляет жизненным циклом браузера
- Реализует основную логику парсинга
- Обрабатывает ошибки и организует повторные попытки
- Собирает и агрегирует результаты

### form_handler.py (FormHandler)
Отвечает за взаимодействие с формами поиска:
- Заполнение полей поиска (имя, фамилия, город, регион)
- Выбор чекбоксов категорий клиентов
- Выбор дополнительных фильтров (например, "Принимает новых клиентов")
- Отправку формы и ожидание результатов
- Очистку формы между запросами

### captcha.py (CaptchaHandler)
Специализированный компонент для обхода reCAPTCHA:
- Обнаружение капчи на странице
- Взаимодействие с сервисами решения капчи (2Captcha/Anti-Captcha)
- Вставку токена решения в форму
- Подтверждение решения капчи
- Обработку ошибок при решении капчи

### navigation.py (Navigator)
Управляет навигацией по сайту:
- Переход на страницу поиска
- Ожидание загрузки страниц
- Обработка ошибок навигации
- Создание скриншотов для отладки
- Проверка состояния страницы

### results.py (ResultsProcessor)
Обрабатывает результаты поиска:
- Извлечение данных из списка результатов
- Обработка пагинации
- Сбор базовой информации о каждом CPA
- Координация с парсером деталей для получения подробной информации

### details.py (DetailsParser)
Отвечает за получение детальной информации:
- Переход на страницу профиля CPA
- Извлечение контактной информации
- Извлечение профессиональных данных
- Извлечение дополнительных сведений

### saver.py (DataSaver)
Сохраняет собранные данные:
- Запись в JSON-файл
- Запись в Excel-файл
- Форматирование данных перед сохранением
- Создание уникальных имен файлов с временными метками

### config.py
Содержит все конфигурационные параметры:
- URL сайта
- Пути к директориям
- Таймауты и задержки
- Настройки повторных попыток
- Список категорий клиентов
- Форматы файлов и шаблоны имен

### .env
Файл с секретными данными:
- API-ключ для сервиса решения капчи (ANTICAPTCHA_API_KEY)
- Другие секретные параметры, которые не должны храниться в коде

---

## Конфигурация

### Основные параметры (config.py)

```python
# URL Stagehand Proxy (оставьте пустым для локального запуска браузера)
STAGEHAND_URL = os.environ.get("STAGEHAND_URL")
```

- Если `STAGEHAND_URL` задан, парсер подключается к Stagehand по этому адресу
- Если не задан — запускается локальный браузер


```python
# Основной URL каталога CPA Quebec
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"

# Директории для файлов
OUTPUT_DIR = os.path.join(PROJECT_ROOT, "output")
LOGS_DIR = os.path.join(PROJECT_ROOT, "logs")
DEBUG_DIR = os.path.join(PROJECT_ROOT, "debug_playwright")

# Таймауты (в миллисекундах)
SHORT_TIMEOUT = 10 * 1000  # 10 секунд (для ожидания элементов)
MEDIUM_TIMEOUT = 20 * 1000 # 20 секунд
LONG_TIMEOUT = 60 * 1000   # 60 секунд (для загрузки страниц, решения капчи)

# Повторные попытки
MAX_RETRY_ATTEMPTS = 3  # Количество повторных попыток при ошибках
RETRY_DELAY = 2         # Задержка между повторными попытками (в секундах)

# Категории клиентов для парсинга
CLIENT_CATEGORIES_TO_PARSE = [
    "Individuals",
    "Large companies",
    "NFPOs",
    "Professional firms",
    "Public corporations",
    "Retailers",
    "Self-employed workers",
    "SMEs",
    "Start-ups",
    "Syndicates of co-owners",
]
```

### Секретные параметры (.env)

```
ANTICAPTCHA_API_KEY=ваш_ключ_api_для_сервиса_решения_капчи
```

---

## Запуск парсера

### Запуск Stagehand

Перед запуском парсера можно запустить Stagehand:

- **Без API-ключа (локальный прокси):**

```bash
npx stagehand --port=9222
```

- **С API-ключом Browserbase (облако):**

```bash
npx stagehand --api-key=YOUR_BROWSERBASE_API_KEY --port=9222
```

В `.env`:

```
STAGEHAND_URL=http://localhost:9222
```

---

### Базовый запуск
```bash
python3 cpa_quebec/main.py
```

### Запуск с параметрами
```bash
python3 cpa_quebec/main.py --by-category --visible --get-details --debug
```

### Основные параметры командной строки

| Параметр | Описание |
|----------|----------|
| `--last-name` | Фамилия для поиска |
| `--first-name` | Имя для поиска |
| `--region` | Регион для поиска |
| `--city` | Город для поиска |
| `--visible` | Запустить браузер в видимом режиме (не headless) |
| `--debug` | Включить режим отладки (подробное логирование, скриншоты) |
| `--get-details` | Получать детальную информацию (по умолчанию включено) |
| `--no-details` | Не получать детальную информацию (взаимоисключающий с `--get-details`) |
| `--by-category` | Запуск парсинга по категориям клиентов |
| `--accepting-new-clients` | Фильтр по CPA, принимающим новых клиентов |
| `--output-dir` | Директория для сохранения результатов |
| `--wait-timeout` | Таймаут ожидания элементов (в секундах) |
| `--page-timeout` | Таймаут загрузки страницы (в секундах) |
| `--no-captcha` | Отключить решение капчи |

### Режимы работы

1. **Парсинг по категориям** (`--by-category`)
   - Последовательно выбирает каждую категорию клиентов из списка в config.py
   - Для каждой категории выполняет поиск и сохраняет результаты

2. **Поиск по критериям** (с указанием параметров `--last-name`, `--first-name`, `--region`, `--city`)
   - Заполняет форму поиска указанными критериями
   - Выполняет поиск и сохраняет результаты

3. **Поиск без критериев** (без указания параметров)
   - Выполняет поиск по всем CPA без фильтрации
   - Может вернуть очень большое количество результатов

---

## Процесс парсинга

### Общий алгоритм работы

1. **Инициализация**
   - Запуск браузера
   - Инициализация всех компонентов
   - Создание скриншота `after_initialize_components`

2. **Парсинг по категориям** (если указан флаг `--by-category`)
   - Для каждой категории из списка `CLIENT_CATEGORIES_TO_PARSE`:
     - Создание скриншота `before_select_{category}`
     - Выбор чекбокса категории
     - Создание скриншота `after_select_{category}`
     - Решение капчи (если появилась)
     - Клик по кнопке поиска
     - Обработка результатов поиска
     - Создание скриншота `after_process_pages_{category}`
     - Сохранение результатов для категории

3. **Поиск по критериям** (если указаны параметры поиска)
   - Заполнение формы поиска указанными критериями
   - Решение капчи (если появилась)
   - Клик по кнопке поиска
   - Обработка результатов поиска
   - Сохранение результатов

4. **Завершение**
   - Закрытие браузера
   - Сохранение итоговых результатов
   - Вывод статистики

### Детальный процесс обработки результатов

1. **Обработка списка результатов**
   - Извлечение базовой информации о каждом CPA из списка
   - Обработка пагинации (переход по страницам результатов)

2. **Получение детальной информации** (если включено)
   - Для каждого найденного CPA:
     - Переход на страницу профиля
     - Извлечение контактной информации
     - Извлечение профессиональных данных
     - Возврат к списку результатов

3. **Агрегация данных**
   - Объединение базовой и детальной информации
   - Форматирование данных для сохранения

---

## Обработка капчи

### Компоненты решения капчи

В проекте используются два компонента для решения капчи:

1. **CaptchaSolver** (в utils.py)
   - Общий класс для взаимодействия с API сервисов решения капчи
   - Инициализируется с API-ключом из .env файла
   - Предоставляет методы для отправки запросов и получения результатов

2. **CaptchaHandler** (в captcha.py)
   - Специализированный компонент для работы с капчей на сайте CPA Quebec
   - Использует CaptchaSolver для решения капчи
   - Отвечает за взаимодействие с элементами капчи на странице

### Процесс решения капчи

1. **Обнаружение капчи**
   - Проверка наличия элемента reCAPTCHA на странице
   - Определение типа капчи (v2, v3)

2. **Решение капчи через внешний сервис**
   - Получение site-key капчи
   - Отправка запроса на сервис решения капчи (2Captcha/Anti-Captcha)
   - Ожидание решения (с таймаутом)
   - Получение токена решения

3. **Применение решения**
   - Вставка токена в форму
   - Подтверждение решения
   - Проверка успешности решения

### Настройка решения капчи

1. **Получение API-ключа**
   - Зарегистрироваться на сервисе решения капчи (2Captcha или Anti-Captcha)
   - Получить API-ключ
   - Пополнить баланс

2. **Настройка в проекте**
   - Добавить API-ключ в файл `.env`:
     ```
     ANTICAPTCHA_API_KEY=ваш_ключ_api
     ```
   - При запуске парсер автоматически загрузит ключ и настроит решение капчи

3. **Отключение решения капчи**
   - Для отключения решения капчи используйте параметр `--no-captcha`
   - В этом случае парсер будет ожидать ручного решения капчи пользователем

---

## Обработка ошибок и повторные попытки

### Стратегия повторных попыток

Парсер использует механизм повторных попыток для обработки временных ошибок:

1. **Настройка повторных попыток**
   - `MAX_RETRY_ATTEMPTS` в config.py определяет максимальное количество попыток
   - `RETRY_DELAY` определяет задержку между попытками в секундах

2. **Типы обрабатываемых ошибок**
   - Ошибки сети
   - Таймауты ожидания элементов
   - Ошибки навигации
   - Ошибки решения капчи
   - Неожиданные изменения структуры страницы

3. **Алгоритм повторных попыток**
   - При возникновении ошибки:
     - Логирование ошибки
     - Проверка количества оставшихся попыток
     - Ожидание указанное время (RETRY_DELAY)
     - Повторное выполнение операции
   - После исчерпания всех попыток:
     - Логирование критической ошибки
     - Переход к следующей операции или завершение работы

### Обработка критических ошибок

При возникновении критических ошибок, которые невозможно обработать повторными попытками:

1. **Логирование**
   - Запись подробной информации об ошибке в лог
   - Создание скриншота текущего состояния страницы

2. **Корректное завершение**
   - Закрытие браузера
   - Сохранение уже собранных данных
   - Возврат соответствующего кода ошибки

3. **Коды завершения**
   - `0` - успешное завершение
   - `1` - ошибка импорта или инициализации
   - `2` - ошибка аргументов командной строки или конфигурации
   - `3` - критическая ошибка выполнения
   - `4` - непредвиденная критическая ошибка

---

## Сохранение данных

### Форматы сохранения

Парсер сохраняет данные в двух форматах:

1. **JSON**
   - Полная структура данных
   - Удобен для программной обработки
   - Сохраняет все собранные поля

2. **Excel (XLSX)**
   - Табличное представление данных
   - Удобен для просмотра и анализа человеком
   - Основные поля организованы в колонки

### Структура сохраняемых данных

```json
{
  "name": "Имя CPA",
  "title": "Должность",
  "company": "Компания",
  "address": "Адрес",
  "phone": "Телефон",
  "email": "Email",
  "website": "Веб-сайт",
  "categories": ["Категория1", "Категория2"],
  "accepting_new_clients": true/false,
  "languages": ["Язык1", "Язык2"],
  "specializations": ["Специализация1", "Специализация2"],
  "additional_info": {
    // Дополнительные поля
  }
}
```

### Именование файлов

Файлы сохраняются с временной меткой для уникальности:

- JSON: `cpa_quebec_results_YYYY-MM-DD_HH-MM-SS.json`
- Excel: `cpa_quebec_results_YYYY-MM-DD_HH-MM-SS.xlsx`

---

## Отладка и логирование

### Уровни логирования

Парсер поддерживает несколько уровней логирования:

1. **INFO** (по умолчанию)
   - Основные этапы работы
   - Статистика результатов
   - Важные события

2. **DEBUG** (с флагом `--debug`)
   - Подробная информация о каждом действии
   - Значения переменных
   - Детали взаимодействия с элементами страницы

3. **WARNING/ERROR/CRITICAL**
   - Предупреждения и ошибки
   - Информация о повторных попытках
   - Критические ошибки, приводящие к завершению работы

### Скриншоты для отладки

При включенном режиме отладки (`--debug`) парсер создает скриншоты на ключевых этапах:

1. **Именование скриншотов**
   - `after_initialize_components.png` - после инициализации компонентов
   - `before_select_{category}.png` - перед выбором категории
   - `after_select_{category}.png` - после выбора категории
   - `after_process_pages_{category}.png` - после обработки страниц результатов
   - `error_{timestamp}.png` - при возникновении ошибки

2. **Расположение скриншотов**
   - Все скриншоты сохраняются в директории `debug_playwright` (указано в config.py как DEBUG_DIR)
   - Структура поддиректорий соответствует дате запуска

---

## Требования и зависимости

### Системные требования

- Python 3.10 или выше
- Доступ в интернет
- Достаточно оперативной памяти для запуска браузера (минимум 4 ГБ)

### Зависимости Python

Основные зависимости:
- `playwright` - для автоматизации браузера
- `python-dotenv` - для загрузки переменных окружения
- `pandas` - для обработки данных и экспорта в Excel
- `openpyxl` - для работы с Excel-файлами
- `anticaptchaofficial` или аналогичная библиотека для работы с сервисами решения капчи

### Установка

1. **Установка Python-зависимостей**
   ```bash
   pip install -r requirements.txt
   ```

2. **Установка браузеров для Playwright**
   ```bash
   playwright install
   ```

3. **Настройка переменных окружения**
   - Создать файл `.env` в корне проекта
   - Добавить API-ключ для сервиса решения капчи:
     ```
     ANTICAPTCHA_API_KEY=ваш_ключ_api
     ```

---

## Часто задаваемые вопросы

### Общие вопросы

**В: Как изменить список категорий для парсинга?**
О: Отредактируйте список `CLIENT_CATEGORIES_TO_PARSE` в файле `config.py`.

**В: Как изменить директорию для сохранения результатов?**
О: Используйте параметр `--output-dir` при запуске или измените `OUTPUT_DIR` в `config.py`.

**В: Можно ли запустить парсер без решения капчи?**
О: Да, используйте параметр `--no-captcha` при запуске.

### Решение проблем

**В: Парсер не может найти элементы на странице**
О: Возможно, изменилась структура сайта. Проверьте селекторы в `form_handler.py` и других файлах.

**В: Ошибка при решении капчи**
О: Проверьте баланс на сервисе решения капчи и правильность API-ключа в файле `.env`.

**В: Браузер не запускается**
О: Убедитесь, что установлены все зависимости и браузеры для Playwright:
```bash
pip install -r requirements.txt
playwright install
```

**В: Парсер работает слишком медленно**
О: Попробуйте отключить сбор детальной информации с помощью параметра `--no-details`.
