2025-04-19 13:45:05,892 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_134505.log
2025-04-19 13:45:05,895 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 13:45:05,896 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-19 13:45:05,896 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 13:45:05,896 - <PERSON><PERSON><PERSON>ue<PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals'}
2025-04-19 13:45:05,896 - CpaQuebecParser - INFO - ==================================================
2025-04-19 13:45:05,896 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-19 13:45:19,657 - CpaQuebecParser - DEBUG - Пауза на 1.84 сек. (после driver.get)
2025-04-19 13:45:21,592 - CpaQuebecParser - DEBUG - Пауза на 0.37 сек. (после scroll к кнопке cookie)
2025-04-19 13:46:03,407 - CpaQuebecParser - DEBUG - Пауза на 0.85 сек. (после клика Reset (XPath))
2025-04-19 13:46:07,659 - CpaQuebecParser - DEBUG - Пауза на 0.60 сек. (после очистки формы)
2025-04-19 13:46:10,639 - CpaQuebecParser - DEBUG - Пауза на 2.15 сек. (перед retry 2 для категории Individuals)
2025-04-19 13:46:12,166 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
