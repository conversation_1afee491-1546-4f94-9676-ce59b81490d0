2025-04-04 16:22:39,502 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_162239.log
2025-04-04 16:22:39,504 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 16:22:39,505 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:22:39,505 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:22:39,505 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:22:39,505 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:22:41,405 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): FormHandler.__init__() missing 1 required positional argument: 'navigator'
2025-04-04 16:22:41,406 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 82, in run_parser
    with CpaQuebecParser(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 134, in __enter__
    self._initialize_components() # Инициализируем компоненты после запуска браузера
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 111, in _initialize_components
    self.form_handler = FormHandler(self.page, self.logger)
TypeError: FormHandler.__init__() missing 1 required positional argument: 'navigator'

2025-04-04 16:22:41,407 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
