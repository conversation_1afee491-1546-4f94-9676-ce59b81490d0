2025-04-04 19:54:57,642 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_195457.log
2025-04-04 19:54:57,645 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:54:57,645 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 19:54:57,645 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:54:57,645 - C<PERSON>Q<PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:54:57,646 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:54:57,646 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:55:03,959 - CpaQuebecParser - DEBUG - Пауза на 1.12 сек. (после навигации)
2025-04-04 19:55:26,735 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 1
