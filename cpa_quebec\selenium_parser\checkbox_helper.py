import logging
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, ElementNotInteractableException, StaleElementReferenceException
from ..config import MEDIUM_TIMEOUT_SEC
from ..utils import random_sleep


class CheckboxHelper:
    """
    Утилитарный класс для выбора чекбоксов на странице с помощью различных стратегий.
    """

    def __init__(self, driver, wait: WebDriverWait, logger: logging.Logger, debug: bool = False):
        self.driver = driver
        self.wait = wait
        self.logger = logger
        self.debug = debug

    def select_checkbox_by_id(self, checkbox_id: str) -> bool:
        """Выбирает чекбокс по его ID с использованием WebDriverWait."""
        try:
            # Используем WebDriverWait для ожидания кликабельности
            checkbox = self.wait.until(EC.element_to_be_clickable((By.ID, checkbox_id)))
            if not checkbox.is_selected():
                # Скролл и клик
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", checkbox)
                checkbox.click()
            self.logger.info(f"Чекбокс по ID '{checkbox_id}' успешно выбран")
            return True
        except TimeoutException:
            self.logger.warning(f"Таймаут ожидания кликабельности для чекбокса ID '{checkbox_id}'")
            return False
        except Exception as e:
            self.logger.warning(f"Не удалось выбрать чекбокс по ID '{checkbox_id}': {e}", exc_info=self.debug)
            return False

    def select_checkbox_by_text(self, text: str) -> bool:
        """Основная стратегия: клик по метке <label> с ТОЧНЫМ текстом `text`."""
        lower = text.lower()

        # Стратегия 1: Точное совпадение текста
        xpath_label_exact = f"//label[translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz') = '{lower}']"

        # Стратегия 2: Частичное совпадение текста
        xpath_label_contains = f"//label[contains(translate(normalize-space(.), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{lower}')]"

        # Пробуем сначала точное совпадение
        try:
            label = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath_label_exact)))
            self.logger.debug(f"Найден label по точному тексту: '{text}'")
            self.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", label
            )
            # Попытка кликнуть по связанному чекбоксу
            fid = label.get_attribute('for')
            if fid:
                try:
                    cb = self.driver.find_element(By.ID, fid)
                    self.logger.debug(f"Найден связанный checkbox по ID '{fid}' через label@for.")
                    if not cb.is_selected():
                        self.logger.info(f"Кликаем checkbox ID '{fid}' (связан с label '{text}')")
                        cb.click()
                    self.logger.info(f"Чекбокс '{text}' выбран через метку по ID: {fid}")
                    return True
                except Exception:
                    # fallback к клику по самой метке
                    self.logger.warning(f"Не удалось кликнуть связанный checkbox ID '{fid}', пробуем клик по самому label.")
                    pass
            self.logger.info(f"Кликаем по самому label с точным текстом '{text}'")
            label.click()
            self.logger.info(f"Чекбокс '{text}' выбран кликом по метке (точное совпадение)")
            return True
        except Exception as e:
            self.logger.debug(f"Стратегия точного совпадения не сработала для '{text}': {e}", exc_info=self.debug)

        # Если точное совпадение не сработало, пробуем частичное
        try:
            label = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath_label_contains)))
            self.logger.debug(f"Найден label по частичному совпадению: '{text}'")
            self.driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", label
            )
            # Попытка кликнуть по связанному чекбоксу
            fid = label.get_attribute('for')
            if fid:
                try:
                    cb = self.driver.find_element(By.ID, fid)
                    self.logger.debug(f"Найден связанный checkbox по ID '{fid}' через label@for.")
                    if not cb.is_selected():
                        self.logger.info(f"Кликаем checkbox ID '{fid}' (связан с label '{text}')")
                        cb.click()
                    self.logger.info(f"Чекбокс '{text}' выбран через метку по ID: {fid}")
                    return True
                except Exception:
                    # fallback к клику по самой метке
                    self.logger.warning(f"Не удалось кликнуть связанный checkbox ID '{fid}', пробуем клик по самому label.")
                    pass
            self.logger.info(f"Кликаем по самому label с частичным совпадением '{text}'")
            label.click()
            self.logger.info(f"Чекбокс '{text}' выбран кликом по метке (частичное совпадение)")
            return True
        except Exception as e:
            self.logger.debug(f"Стратегия частичного совпадения не сработала для '{text}': {e}", exc_info=self.debug)

        # Если обе стратегии не сработали — переходим к JS
        return self.select_checkbox_via_js(text)

    def select_checkbox_via_js(self, text: str) -> bool:
        """Запасная стратегия: выбор чекбокса через JavaScript."""
        self.logger.debug(f"Запуск JS-стратегии для выбора чекбокса '{text}'")
        script = '''
        (function(txt) {
            txt = txt.toLowerCase();

            // Логируем все доступные метки для отладки
            var allLabels = document.querySelectorAll('label');
            console.log('All labels on page:', Array.from(allLabels).map(l => l.innerText.trim()));

            // Стратегия 1: Поиск по точному совпадению текста
            var labels = document.querySelectorAll('label');
            for (var i = 0; i < labels.length; i++) {
                var lbl = labels[i];
                if (lbl.innerText && lbl.innerText.toLowerCase().trim() === txt) {
                    console.log('JS: Found exact match label:', lbl.innerText);
                    var fid = lbl.htmlFor;
                    var cb = fid ? document.getElementById(fid) : lbl.querySelector('input[type="checkbox"]');
                    if (cb) {
                        console.log('JS: Found checkbox via exact match label, clicking checkbox', cb);
                        cb.scrollIntoView(); cb.click(); return true;
                    }
                    console.log('JS: Checkbox not found for exact match, clicking label itself', lbl);
                    lbl.scrollIntoView(); lbl.click(); return true;
                }
            }

            // Стратегия 2: Поиск по частичному совпадению текста
            for (var i = 0; i < labels.length; i++) {
                var lbl = labels[i];
                if (lbl.innerText && lbl.innerText.toLowerCase().includes(txt)) {
                    console.log('JS: Found partial match label:', lbl.innerText);
                    var fid = lbl.htmlFor;
                    var cb = fid ? document.getElementById(fid) : lbl.querySelector('input[type="checkbox"]');
                    if (cb) {
                        console.log('JS: Found checkbox via partial match label, clicking checkbox', cb);
                        cb.scrollIntoView(); cb.click(); return true;
                    }
                    console.log('JS: Checkbox not found for partial match, clicking label itself', lbl);
                    lbl.scrollIntoView(); lbl.click(); return true;
                }
            }

            // Стратегия 3: Поиск по ID чекбоксов, связанных с категориями
            var categoryIds = {
                'individuals': 'ListeClienteleDesserviesLeftColumn_0__Selected',
                'large companies': 'ListeClienteleDesserviesLeftColumn_1__Selected',
                'nfpos': 'ListeClienteleDesserviesLeftColumn_2__Selected',
                'professional firms': 'ListeClienteleDesserviesLeftColumn_3__Selected',
                'public corporations': 'ListeClienteleDesserviesLeftColumn_4__Selected',
                'retailers': 'ListeClienteleDesserviesRightColumn_0__Selected',
                'self-employed workers': 'ListeClienteleDesserviesRightColumn_1__Selected',
                'smes': 'ListeClienteleDesserviesRightColumn_2__Selected',
                'start-ups': 'ListeClienteleDesserviesRightColumn_3__Selected',
                'syndicates of co-owners': 'ListeClienteleDesserviesRightColumn_4__Selected'
            };

            // Проверяем, есть ли текст в списке известных категорий
            var checkboxId = categoryIds[txt];
            if (checkboxId) {
                var checkbox = document.getElementById(checkboxId);
                if (checkbox) {
                    console.log('JS: Found checkbox by known ID:', checkboxId);
                    checkbox.scrollIntoView(); checkbox.click(); return true;
                }
            }

            // Стратегия 4: Поиск по всем чекбоксам на странице
            var allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
            console.log('All checkboxes on page:', allCheckboxes.length);

            // Ищем чекбоксы с ID, содержащим 'Selected'
            for (var i = 0; i < allCheckboxes.length; i++) {
                var cb = allCheckboxes[i];
                if (cb.id && cb.id.includes('Selected')) {
                    console.log('JS: Found checkbox with ID containing "Selected":', cb.id);
                    // Пытаемся найти связанный label
                    var associatedLabel = document.querySelector('label[for="' + cb.id + '"]');
                    if (associatedLabel && associatedLabel.innerText.toLowerCase().includes(txt)) {
                        console.log('JS: Found matching label for checkbox:', associatedLabel.innerText);
                        cb.scrollIntoView(); cb.click(); return true;
                    }
                }
            }

            // Если ничего не нашли, возвращаем false
            console.log('JS: No matching checkbox found for:', txt);
            return false;
        })(arguments[0]);
        '''
        try:
            result = self.driver.execute_script(script, text)
            if result:
                self.logger.info(f"Чекбокс '{text}' выбран через JS-стратегию")
                return True
            self.logger.warning(f"JS-стратегия не нашла чекбокс '{text}'")
            return False
        except Exception as e:
            self.logger.error(f"Ошибка JS-стратегии для '{text}': {e}", exc_info=self.debug)
            return False