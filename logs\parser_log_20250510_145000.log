2025-05-10 14:50:00 [INFO    ] cpa_quebec_parser   : Logging setup complete. Level: INFO. Log file: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/logs/parser_log_20250510_145000.log
2025-05-10 14:50:00 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION STARTED =====
2025-05-10 14:50:00 [INFO    ] cpa_quebec_parser   : Arguments: {'by_category': False, 'category': 'Individuals', 'all': False, 'no_captcha': False, 'no_details': False, 'visible': True, 'debug': False, 'log_level': 'INFO', 'max_pages': 500, 'use_system_browser': False}
2025-05-10 14:50:00 [INFO    ] cpa_quebec_parser   : Anti-Captcha API Key found (ending with ...cc18b). Automatic solving ENABLED.
2025-05-10 14:50:00 [INFO    ] cpa_quebec_parser   : Initializing Playwright...
2025-05-10 14:50:00 [INFO    ] cpa_quebec_parser   : Launching browser (headed)...
2025-05-10 14:50:01 [INFO    ] cpa_quebec_parser   : Browser launched successfully. Version: 136.0.7103.25
2025-05-10 14:50:01 [INFO    ] cpa_quebec_parser   : Browser context created.
2025-05-10 14:50:01 [INFO    ] cpa_quebec_parser   : New page created.
2025-05-10 14:50:01 [INFO    ] cpa_quebec_parser   : Mode: Processing single category: Individuals
2025-05-10 14:50:01 [INFO    ] cpa_quebec_parser   : --- Starting processing for category: Individuals ---
2025-05-10 14:50:01 [INFO    ] cpa_quebec_parser   : ===== STARTING CATEGORY PROCESSING: Individuals =====
2025-05-10 14:50:01 [INFO    ] cpa_quebec_parser   : Navigating to base URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/
2025-05-10 14:50:07 [INFO    ] cpa_quebec_parser   : Base URL page loaded.
2025-05-10 14:50:08 [INFO    ] cpa_quebec_parser   : Found cookie banner button with selector: text=Tout refuser
2025-05-10 14:50:09 [INFO    ] cpa_quebec_parser   : Cookie banner closed.
2025-05-10 14:50:10 [INFO    ] cpa_quebec_parser   : Selecting category checkbox: 'Individuals'
2025-05-10 14:50:13 [INFO    ] cpa_quebec_parser   : Checkbox for 'Individuals' checked successfully.
2025-05-10 14:50:14 [INFO    ] cpa_quebec_parser   : Checking if CAPTCHA needs to be solved...
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Search button is already enabled. CAPTCHA might not be required.
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Clicking the search button...
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Form-specific selector failed, trying role-based selector with nth
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Found form with Search text
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Found submit button in form
2025-05-10 14:50:15 [WARNING ] cpa_quebec_parser   : Error trying to click form-filtered button: Locator.click: Element is outside of the viewport
Call log:
  - waiting for locator("form").filter(has_text="Search").locator("button[type='submit']").first
    - locator resolved to <button type="submit">Search</button>
  - attempting click action
    - scrolling into view if needed
    - done scrolling

2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Found 2 search buttons by role
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Trying to click search button #1
2025-05-10 14:50:15 [WARNING ] cpa_quebec_parser   : Failed to click search button #1: Locator.click: Element is outside of the viewport
Call log:
  - waiting for get_by_role("button", name="Search").first
    - locator resolved to <button type="submit">Search</button>
  - attempting click action
    - scrolling into view if needed
    - done scrolling

2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Trying to click search button #2
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Search button #2 clicked successfully
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Trying JavaScript click on first search button
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : JavaScript click executed
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : Search initiated successfully.
2025-05-10 14:50:15 [INFO    ] cpa_quebec_parser   : --- Processing results page 1 for category 'Individuals' ---
2025-05-10 14:50:20 [INFO    ] cpa_quebec_parser   : Using container: div.search-results
2025-05-10 14:50:20 [INFO    ] cpa_quebec_parser   : Found 1 cards with absolute selector: div[class*='result']
2025-05-10 14:50:20 [INFO    ] cpa_quebec_parser   : Successfully identified 1 result cards.
2025-05-10 14:50:20 [INFO    ] cpa_quebec_parser   : Found 1 result cards on page 1.
2025-05-10 14:50:21 [WARNING ] cpa_quebec_parser   :   Could not find profile URL for card #1 (1 - 0).
2025-05-10 14:50:21 [INFO    ] cpa_quebec_parser   : Successfully processed 1 cards out of 1 on page 1.
2025-05-10 14:50:21 [INFO    ] cpa_quebec_parser   : No active 'Next' button found. Reached the last page.
2025-05-10 14:50:21 [INFO    ] cpa_quebec_parser   : Finished collecting results for category 'Individuals'. Total items found: 1
2025-05-10 14:50:21 [INFO    ] cpa_quebec_parser   : Starting detailed scraping for 1 profiles...
2025-05-10 14:50:21 [WARNING ] cpa_quebec_parser   : Skipping detail scraping for item 1 (1 - 0) - no profile URL.
2025-05-10 14:50:21 [INFO    ] cpa_quebec_parser   : Finished scraping details. Total items with attempted details: 1
2025-05-10 14:50:21 [INFO    ] cpa_quebec_parser   : ===== FINISHED CATEGORY PROCESSING: Individuals (Total: 1 items) =====
2025-05-10 14:50:21 [INFO    ] cpa_quebec_parser   : Category 'Individuals' processed. Found 1 items.
2025-05-10 14:50:23 [INFO    ] cpa_quebec_parser   : Total unique items collected: 1 (removed 0 duplicates)
2025-05-10 14:50:23 [INFO    ] cpa_quebec_parser   : Saving 1 unique results to /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/output/cpa_results_2025-05-10_14-50-23.json...
2025-05-10 14:50:23 [INFO    ] cpa_quebec_parser   : Results successfully saved to /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new1/output/cpa_results_2025-05-10_14-50-23.json
2025-05-10 14:50:23 [INFO    ] cpa_quebec_parser   : Cleaning up resources...
2025-05-10 14:50:23 [INFO    ] cpa_quebec_parser   : ===== SCRIPT EXECUTION FINISHED =====
