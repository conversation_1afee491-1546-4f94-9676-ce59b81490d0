2025-04-22 21:06:16,749 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250422_210616.log
2025-04-22 21:06:16,752 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Parser - DEBUG - Режим отладки DEBUG включен.
2025-04-22 21:06:16,752 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-22 21:06:16,752 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-22 21:06:16,752 - <PERSON><PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-22 21:06:16,752 - CpaQuebecParser - INFO - ==================================================
2025-04-22 21:06:16,752 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-22 21:06:23,390 - CpaQuebecParser - DEBUG - Пауза на 1.88 сек. (после driver.get)
2025-04-22 21:06:53,103 - CpaQuebecParser - DEBUG - Пауза на 0.71 сек. (после очистки формы)
2025-04-22 21:06:53,905 - CpaQuebecParser - DEBUG - Пауза на 4.34 сек. (между категориями, после Individuals)
