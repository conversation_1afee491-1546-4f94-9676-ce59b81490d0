2025-04-18 14:40:21,859 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_144021.log
2025-04-18 14:40:21,865 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 14:40:21,865 - CpaQuebecParser - INFO - ==================================================
2025-04-18 14:40:21,866 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 14:40:21,866 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-18 14:40:21,867 - CpaQuebecParser - INFO - ==================================================
2025-04-18 14:40:21,867 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-18 14:40:46,214 - CpaQuebecParser - DEBUG - Пауза на 1.91 сек. (после driver.get)
2025-04-18 14:40:48,274 - CpaQuebecParser - DEBUG - Пауза на 0.55 сек. (после scroll к кнопке cookie)
2025-04-18 14:41:00,016 - CpaQuebecParser - DEBUG - Пауза на 0.56 сек. (после клика Reset (XPath))
2025-04-18 14:41:03,431 - CpaQuebecParser - DEBUG - Пауза на 0.52 сек. (после очистки формы)
2025-04-18 14:42:15,353 - CpaQuebecParser - DEBUG - Пауза на 2.15 сек. (между категориями, после Individuals)
2025-04-18 14:42:20,628 - CpaQuebecParser - DEBUG - Пауза на 2.21 сек. (после driver.get)
2025-04-18 14:43:53,697 - CpaQuebecParser - DEBUG - Пауза на 0.89 сек. (после клика Reset (XPath))
2025-04-18 14:43:56,864 - CpaQuebecParser - DEBUG - Пауза на 0.82 сек. (после очистки формы)
2025-04-18 14:45:09,065 - CpaQuebecParser - DEBUG - Пауза на 3.21 сек. (между категориями, после Large companies)
2025-04-18 14:45:16,721 - CpaQuebecParser - DEBUG - Пауза на 1.59 сек. (после driver.get)
2025-04-18 14:46:49,287 - CpaQuebecParser - DEBUG - Пауза на 0.62 сек. (после клика Reset (XPath))
2025-04-18 14:46:50,196 - CpaQuebecParser - DEBUG - Пауза на 0.77 сек. (после очистки формы)
2025-04-18 14:48:06,684 - CpaQuebecParser - DEBUG - Пауза на 3.03 сек. (между категориями, после NFPOs)
2025-04-18 14:48:13,550 - CpaQuebecParser - DEBUG - Пауза на 1.25 сек. (после driver.get)
2025-04-18 14:49:46,036 - CpaQuebecParser - DEBUG - Пауза на 0.54 сек. (после клика Reset (XPath))
2025-04-18 14:49:49,432 - CpaQuebecParser - DEBUG - Пауза на 0.71 сек. (после очистки формы)
2025-04-18 14:51:01,511 - CpaQuebecParser - DEBUG - Пауза на 4.66 сек. (между категориями, после Professional firms)
2025-04-18 14:51:08,727 - CpaQuebecParser - DEBUG - Пауза на 1.52 сек. (после driver.get)
2025-04-18 14:51:43,148 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
