2025-04-18 13:57:10,714 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_135710.log
2025-04-18 13:57:10,719 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 13:57:10,719 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-18 13:57:10,720 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 13:57:10,720 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-18 13:57:10,721 - CpaQuebecParser - INFO - ==================================================
2025-04-18 13:57:10,722 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-18 13:57:20,332 - CpaQuebecParser - DEBUG - Пауза на 1.73 сек. (после driver.get)
2025-04-18 13:57:31,244 - CpaQuebecParser - DEBUG - Пауза на 0.49 сек. (после scroll к кнопке cookie)
2025-04-18 13:57:43,116 - CpaQuebecParser - DEBUG - Пауза на 0.78 сек. (после клика Reset (XPath))
2025-04-18 13:57:45,962 - CpaQuebecParser - DEBUG - Пауза на 0.59 сек. (после очистки формы)
2025-04-18 13:58:44,321 - CpaQuebecParser - DEBUG - Пауза на 0.62 сек. (после scroll к элементу 'Individuals')
2025-04-18 13:58:46,444 - CpaQuebecParser - DEBUG - Пауза на 3.54 сек. (между категориями, после Individuals)
2025-04-18 13:58:52,342 - CpaQuebecParser - DEBUG - Пауза на 2.42 сек. (после driver.get)
