2025-04-04 16:22:58,198 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_162258.log
2025-04-04 16:22:58,202 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 16:22:58,202 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:22:58,202 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:22:58,202 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:22:58,202 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:23:00,811 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): local variable 'retries' referenced before assignment
2025-04-04 16:23:00,811 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 96, in run_parser
    results = cpa_parser.parse(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 164, in parse
    self.results = self._parse_by_categories(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 181, in _parse_by_categories
    while retries < MAX_RETRY_ATTEMPTS:
UnboundLocalError: local variable 'retries' referenced before assignment

2025-04-04 16:23:00,811 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
