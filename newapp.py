#!/usr/bin/env python3
"""
CPA Quebec Directory – one‑file parser  (v0.3)
===========================================
* Исправлено обнаружение reCAPTCHA (anchor‑iframe vs challenge‑iframe)
* Добавлено авто‑закрытие cookie‑баннера («Tout refuser»)
* Лёгкая рефакторизация solve_captcha()
* Добавлено обнаружение системного Chrome
* Улучшена обработка ошибок Anti-Captcha (включая ERROR_ZERO_BALANCE)
* Улучшена логика ожидания и проверки решения капчи
* Добавлены проверки предусловий с помощью assert в некоторых функциях для консистентности
* Добавлены таймауты и проверки видимости в ключевых операциях Playwright
* Уточнены селекторы и добавлена отладка для них

Запуск примера:
    python3 cpa_quebec_single_file.py --by-category --visible --debug
"""
import argparse
import asyncio
import json
import logging
import os
import re
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
from functools import wraps
import inspect
import shutil # Для поиска системного Chrome
import aiohttp
from urllib.parse import urlparse, parse_qs

from playwright.async_api import (
    async_playwright,
    Page,
    Locator,
    TimeoutError as PlaywrightTimeout,
    Error as PlaywrightError
)
from dotenv import load_dotenv

# Импорты для контрактного программирования
from typing import TypeVar, Callable, cast, Generic

# Настройка логирования
# Устанавливаем базовую конфигурацию до вызова setup_logging, чтобы логи были видны сразу
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("cpa_quebec_parser")

# ---------------------------------------------------------------------------
# Контрактное программирование (без изменений)
# ---------------------------------------------------------------------------

T = TypeVar('T')
R = TypeVar('R')

class ContractViolation(Exception):
    """Исключение, возникающее при нарушении контракта."""
    pass

class PreconditionViolation(ContractViolation):
    """Исключение, возникающее при нарушении предусловия."""
    pass

class PostconditionViolation(ContractViolation):
    """Исключение, возникающее при нарушении постусловия."""
    pass

class InvariantViolation(ContractViolation):
    """Исключение, возникающее при нарушении инварианта."""
    pass

def precondition(condition: Callable[..., bool], message: str = None):
    """Декоратор для проверки предусловия."""
    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @wraps(func)
        def wrapper(*args, **kwargs):
            sig = inspect.signature(func)
            try:
                bound_args = sig.bind(*args, **kwargs)
                bound_args.apply_defaults()
                # Передаем аргументы в condition в том же порядке, что и в func
                if not condition(*bound_args.args, **bound_args.kwargs):
                    error_msg = message or f"Нарушено предусловие для {func.__name__}"
                    logger.error(error_msg)
                    raise PreconditionViolation(error_msg)
            except TypeError as e:
                logger.error(f"Ошибка при проверке предусловия для {func.__name__}: {e}")
                # Можно либо пробросить исключение, либо продолжить выполнение, если это допустимо
                raise PreconditionViolation(f"Ошибка при проверке аргументов предусловия для {func.__name__}: {e}")

            return func(*args, **kwargs)
        return wrapper
    return decorator

def postcondition(condition: Callable[..., bool], message: str = None):
    """Декоратор для проверки постусловия."""
    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            sig = inspect.signature(condition)
            params = list(sig.parameters.keys())

            # Определяем, какие аргументы передать в condition
            condition_args = []
            if params and params[0] == 'result':
                condition_args.append(result)
                params.pop(0) # Убираем 'result' из списка параметров

            # Привязываем остальные аргументы функции к параметрам condition
            func_sig = inspect.signature(func)
            func_bound_args = func_sig.bind(*args, **kwargs)
            func_bound_args.apply_defaults()

            # Сопоставляем оставшиеся параметры condition с аргументами func
            condition_kwargs = {}
            all_func_args = func_bound_args.arguments
            for param_name in params:
                if param_name in all_func_args:
                    condition_kwargs[param_name] = all_func_args[param_name]
                # Можно добавить обработку *args, **kwargs condition, если нужно

            try:
                 # Передаем result как первый аргумент, если он есть в condition
                if 'result' in inspect.signature(condition).parameters:
                    if not condition(result, **condition_kwargs):
                         error_msg = message or f"Нарушено постусловие для {func.__name__}"
                         logger.error(error_msg)
                         raise PostconditionViolation(error_msg)
                else:
                    # Если result не ожидается, передаем только остальные аргументы
                    if not condition(**condition_kwargs):
                         error_msg = message or f"Нарушено постусловие для {func.__name__}"
                         logger.error(error_msg)
                         raise PostconditionViolation(error_msg)

            except TypeError as e:
                logger.error(f"Ошибка при проверке постусловия для {func.__name__}: {e}")
                raise PostconditionViolation(f"Ошибка при проверке аргументов постусловия для {func.__name__}: {e}")

            return result

        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            sig = inspect.signature(condition)
            params = list(sig.parameters.keys())

            # Определяем, какие аргументы передать в condition
            condition_args = []
            if params and params[0] == 'result':
                condition_args.append(result)
                params.pop(0) # Убираем 'result' из списка параметров

            # Привязываем остальные аргументы функции к параметрам condition
            func_sig = inspect.signature(func)
            func_bound_args = func_sig.bind(*args, **kwargs)
            func_bound_args.apply_defaults()

            # Сопоставляем оставшиеся параметры condition с аргументами func
            condition_kwargs = {}
            all_func_args = func_bound_args.arguments
            for param_name in params:
                if param_name in all_func_args:
                    condition_kwargs[param_name] = all_func_args[param_name]
                # Можно добавить обработку *args, **kwargs condition, если нужно

            try:
                 # Передаем result как первый аргумент, если он есть в condition
                if 'result' in inspect.signature(condition).parameters:
                    if not condition(result, **condition_kwargs):
                         error_msg = message or f"Нарушено постусловие для {func.__name__}"
                         logger.error(error_msg)
                         raise PostconditionViolation(error_msg)
                else:
                    # Если result не ожидается, передаем только остальные аргументы
                    if not condition(**condition_kwargs):
                         error_msg = message or f"Нарушено постусловие для {func.__name__}"
                         logger.error(error_msg)
                         raise PostconditionViolation(error_msg)

            except TypeError as e:
                logger.error(f"Ошибка при проверке постусловия для {func.__name__}: {e}")
                raise PostconditionViolation(f"Ошибка при проверке аргументов постусловия для {func.__name__}: {e}")

            return result

        # Выбираем подходящий враппер в зависимости от типа функции
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    return decorator

# Invariant и _wrap_method_with_invariant без изменений
def invariant(condition: Callable[..., bool], message: str = None):
    """Декоратор для проверки инварианта."""
    def decorator(cls):
        original_init = cls.__init__

        @wraps(original_init)
        def new_init(self, *args, **kwargs):
            original_init(self, *args, **kwargs)
            if not condition(self):
                error_msg = message or f"Нарушен инвариант для {cls.__name__} после __init__"
                logger.error(error_msg)
                raise InvariantViolation(error_msg)

        cls.__init__ = new_init

        for name, method in inspect.getmembers(cls, inspect.isfunction):
            if name != '__init__':
                 # Проверяем, существует ли атрибут, прежде чем его устанавливать
                 # Это может быть полезно для свойств или других дескрипторов
                 if hasattr(cls, name):
                     setattr(cls, name, _wrap_method_with_invariant(method, condition, message))

        return cls
    return decorator

def _wrap_method_with_invariant(method, condition, message):
    @wraps(method)
    def wrapper(self, *args, **kwargs):
        if not condition(self):
            error_msg = message or f"Нарушен инвариант перед выполнением {method.__name__}"
            logger.error(error_msg)
            raise InvariantViolation(error_msg)

        result = method(self, *args, **kwargs)

        if not condition(self):
            error_msg = message or f"Нарушен инвариант после выполнения {method.__name__}"
            logger.error(error_msg)
            raise InvariantViolation(error_msg)

        return result
    return wrapper

# Функции для проверки типов (можно было бы удалить в пользу assert/type hints, но оставим для примера)
def check_type(value, expected_type, param_name=None):
    """Проверяет, соответствует ли значение ожидаемому типу."""
    if isinstance(expected_type, tuple):
        # Handle Optional case slightly differently if None is allowed
        allow_none = type(None) in expected_type
        if allow_none and value is None:
            return True
        # Remove None from expected types for the main check if needed
        types_to_check = tuple(t for t in expected_type if t is not type(None))
        if not types_to_check: # Only None was allowed
             return value is None
    else:
        allow_none = expected_type is type(None)
        if allow_none and value is None:
            return True
        types_to_check = expected_type

    if not isinstance(value, types_to_check):
        if param_name:
            logger.error(f"Параметр {param_name} имеет неверный тип: {type(value)}, ожидается {expected_type}")
        return False
    return True

def check_not_none(value, param_name=None):
    """Проверяет, что значение не None."""
    if value is None:
        if param_name:
            logger.error(f"Параметр {param_name} не должен быть None")
        return False
    return True

# ---------------------------------------------------------------------------
# Глобальные переменные и константы
# ---------------------------------------------------------------------------
args: Optional[argparse.Namespace] = None  # Будет инициализировано в run()

PROJECT_ROOT = Path(__file__).parent.resolve() # Используем resolve для абсолютного пути
OUTPUT_DIR = PROJECT_ROOT / "output"
LOGS_DIR = PROJECT_ROOT / "logs" # Единая директория для всех логов
DEBUG_DIR_BASE = LOGS_DIR / "debug" # Базовая директория для отладки теперь внутри logs

# Директория для отладочных файлов будет создаваться динамически с датой
DEBUG_DIR: Optional[Path] = None

# Инициализация DEBUG_DIR (теперь происходит позже, когда args доступны)
def initialize_debug_dir():
    global DEBUG_DIR
    if args and args.debug:
        # Создаем директорию для логов, если она не существует
        LOGS_DIR.mkdir(parents=True, exist_ok=True)

        # Создаем директорию для отладочных файлов с датой и временем
        DEBUG_DIR = DEBUG_DIR_BASE / datetime.now().strftime("%Y%m%d_%H%M%S")
        try:
            DEBUG_DIR.mkdir(parents=True, exist_ok=True)
            logger.info(f"Debug directory created: {DEBUG_DIR}")
        except OSError as e:
            logger.error(f"Failed to create debug directory {DEBUG_DIR}: {e}")
            DEBUG_DIR = None # Не удалось создать, отключаем сохранение отладки
    else:
        DEBUG_DIR = None # Отладка не включена

MAX_DEBUG_FILES = 100

CLIENT_CATEGORIES = [
    # Оставим все категории, но при запуске можно указать одну через --category
    "Individuals",
    "Large companies",
    "NFPOs",
    "Professional firms",
    "Public corporations",
    "Retailers",
    "Self-employed workers",
    "SMEs",
    "Start-ups",
    "Syndicates of co-owners",
]
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"

# Загружаем переменные окружения из .env файла
load_dotenv()
ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")

# ---------------------------------------------------------------------------
# Utility helpers
# ---------------------------------------------------------------------------

@postcondition(
    lambda result: isinstance(result, str) and len(result) == 19,
    "Результат timestamp_str должен быть строкой длиной 19 символов"
)
def timestamp_str() -> str:
    """Возвращает текущую дату и время в формате строки для использования в именах файлов."""
    return datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

@precondition(
    lambda file_type: isinstance(file_type, str) and len(file_type) > 0,
    "file_type должен быть непустой строкой"
)
@postcondition(
    # Проверяем, что результат - строка и либо DEBUG_DIR None, либо директория существует
    lambda result, file_type: isinstance(result, str) and (DEBUG_DIR is None or Path(result).parent.exists()),
    "Результат должен быть строкой, и если DEBUG_DIR задан, директория должна существовать"
)
async def manage_debug_files(file_type: str) -> str:
    """Управляет отладочными файлами: создает имя файла в отладочной директории и
    ограничивает общее количество файлов в директории.

    Возвращает пустую строку, если DEBUG_DIR не инициализирован.
    """
    if DEBUG_DIR is None:
        logger.warning("Debug directory is not initialized. Cannot manage debug files.")
        return "" # Возвращаем пустую строку или выбрасываем исключение

    try:
        # Проверяем общее количество файлов в директории
        all_debug_files = list(DEBUG_DIR.glob("*.*"))

        # Если превышен лимит, удаляем самые старые файлы
        if len(all_debug_files) >= MAX_DEBUG_FILES:
            all_debug_files.sort(key=lambda x: x.stat().st_mtime)
            files_to_remove_count = len(all_debug_files) - MAX_DEBUG_FILES + 1
            logger.debug(f"Max debug files ({MAX_DEBUG_FILES}) reached. Removing {files_to_remove_count} oldest files.")
            for i in range(files_to_remove_count):
                try:
                    all_debug_files[i].unlink(missing_ok=True)
                    logger.debug(f"Removed old debug file: {all_debug_files[i].name}")
                except OSError as e:
                    logger.warning(f"Failed to remove old debug file {all_debug_files[i]}: {e}")

        # Создаем имя нового файла
        # Добавляем суффикс к file_type, если он еще не содержит его (например, .png, .html)
        ts = timestamp_str()
        if '.' not in file_type:
             # Определяем расширение по типу (можно расширить)
             extension = ".txt"
             if file_type.startswith("screenshot") or file_type.endswith("png"):
                 extension = ".png"
             elif file_type.startswith("html") or file_type.endswith("html"):
                 extension = ".html"
             elif file_type.startswith("json") or file_type.endswith("json"):
                 extension = ".json"
             filename = f"{file_type}_{ts}{extension}"
        else:
            filename = f"{file_type}_{ts}" # file_type уже содержит расширение

        result_path = str(DEBUG_DIR / filename)

        # Постусловие проверится декоратором
        return result_path

    except Exception as e:
        logger.error(f"Error managing debug files in {DEBUG_DIR}: {e}")
        return "" # Возвращаем пустую строку в случае ошибки

async def save_debug_snapshot(page: Page, name: str):
    """Сохраняет скриншот и HTML для отладки, если включен режим debug."""
    if args and args.debug and DEBUG_DIR:
        screenshot_path = await manage_debug_files(f"screenshot_{name}")
        html_path = await manage_debug_files(f"html_{name}")
        if screenshot_path:
            try:
                await page.screenshot(path=screenshot_path, full_page=True)
                logger.debug(f"Debug screenshot saved: {Path(screenshot_path).name}")
            except (PlaywrightError, OSError) as e:
                logger.warning(f"Failed to save screenshot {screenshot_path}: {e}")
        if html_path:
            try:
                content = await page.content()
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(content)
                logger.debug(f"Debug HTML saved: {Path(html_path).name}")
            except (PlaywrightError, OSError, IOError) as e:
                 logger.warning(f"Failed to save HTML {html_path}: {e}")

# precondition decorator already applied
@postcondition(lambda result, *args, **kwargs: isinstance(result, bool), "Результат должен быть булевым значением")
async def close_cookies_banner(page: Page) -> bool:
    """Закрывает баннер с cookie, если он присутствует."""
    logger.debug("Checking for cookie banner...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    cookie_button_selectors = [
        "text=Tout refuser",        # Французский
        "text=Reject All",          # Английский
        "text=Decline All",         # Альтернативный английский
        "button:has-text('Tout refuser')", # Более точный селектор
        "button:has-text('Reject All')",
        "button:has-text('Decline All')",
        "button[id*='cookie'][id*='reject']", # По ID
        "button[class*='cookie'][class*='reject']", # По классу
        "#onetrust-reject-all-handler", # OneTrust specific
        ".cc-btn.cc-dismiss",       # CookieConsent specific
        "button[data-testid='cookie-policy-manage-dialog-reject-button']" # Пример data-testid
    ]

    for selector in cookie_button_selectors:
        try:
            # Увеличиваем таймаут видимости, чтобы дать баннеру появиться
            button = page.locator(selector).first # Ищем первый подходящий элемент
            # Ждем появления элемента, но недолго
            await button.wait_for(state="attached", timeout=2000)

            if await button.is_visible(timeout=1500): # Проверяем видимость с небольшим таймаутом
                logger.info(f"Found cookie banner button with selector: {selector}")
                await button.click(timeout=5000, delay=100) # Небольшая задержка перед кликом
                logger.info("Cookie banner closed.")
                await page.wait_for_timeout(500) # Небольшая пауза после клика
                return True
            else:
                logger.debug(f"Button with selector '{selector}' found but not visible.")
        except PlaywrightTimeout:
            logger.debug(f"Cookie banner button not found or timed out for selector: {selector}")
            continue # Пробуем следующий селектор
        except PlaywrightError as e:
            # Логируем ошибки Playwright, но продолжаем
            logger.debug(f"Playwright error checking selector {selector}: {e}")
            continue
        except Exception as e:
            # Логируем другие неожиданные ошибки
            logger.warning(f"Unexpected error checking selector {selector}: {e}")
            continue # Пробуем следующий селектор

    logger.debug("Cookie banner not found or already closed.")
    return False

# JavaScript-шаблоны для повторного использования (без изменений)
JS_CLICK_SEARCH_BUTTON = """
    // ... (JS code remains the same) ...
"""
JS_SUBMIT_FORM = """
    // ... (JS code remains the same) ...
"""

# precondition decorator already applied
@postcondition(
    lambda result, page: result is None or isinstance(result, Locator),
    "Результат должен быть None или экземпляром Locator"
)
async def find_search_button(page: Page) -> Optional[Locator]:
    """Находит кнопку поиска на странице, используя различные селекторы.

    ВАЖНО: Функция должна находить ПРАВУЮ кнопку поиска, а не левую кнопку сброса.
    """
    logger.debug("Finding search button (RIGHT button, not the reset/left one)...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    # ПЕРВЫЙ МЕТОД: Используем JavaScript для поиска правой кнопок
    # Это самый надежный метод, так как мы можем точно определить положение кнопок
    try:
        logger.debug("Using JavaScript to find the RIGHT search button...")
        button_info = await page.evaluate("""
            () => {
                // Функция для определения, является ли кнопка кнопкой поиска
                function isSearchButton(btn) {
                    const text = (btn.textContent || btn.value || '').toLowerCase();
                    return text.includes('search') || text.includes('find') ||
                           text.includes('rechercher') || text.includes('trouver') ||
                           btn.id?.toLowerCase().includes('search') ||
                           btn.name?.toLowerCase().includes('search') ||
                           btn.className?.toLowerCase().includes('search');
                }

                // Функция для определения, является ли кнопка кнопкой сброса
                function isResetButton(btn) {
                    const text = (btn.textContent || btn.value || '').toLowerCase();
                    return text.includes('reset') || text.includes('clear') ||
                           text.includes('réinitialiser') || text.includes('effacer') ||
                           btn.type === 'reset' ||
                           btn.id?.toLowerCase().includes('reset') ||
                           btn.name?.toLowerCase().includes('reset') ||
                           btn.className?.toLowerCase().includes('reset');
                }

                // Ищем все формы на странице
                const forms = document.querySelectorAll('form');
                let rightButton = null;
                let rightButtonInfo = null;

                for (const form of forms) {
                    // Получаем все кнопки в форме
                    const buttons = Array.from(form.querySelectorAll('button, input[type="submit"], input[type="reset"], input[type="button"]'));
                    if (buttons.length < 2) continue; // Нам нужны формы с минимум 2 кнопками

                    // Сортируем кнопки по горизонтальной позиции (слева направо)
                    const sortedButtons = buttons.sort((a, b) => {
                        const aRect = a.getBoundingClientRect();
                        const bRect = b.getBoundingClientRect();
                        return aRect.left - bRect.left;
                    });

                    // Ищем кнопку сброса (обычно она слева)
                    let resetButton = null;
                    let resetIndex = -1;

                    for (let i = 0; i < sortedButtons.length; i++) {
                        const btn = sortedButtons[i];
                        if (isResetButton(btn)) {
                            resetButton = btn;
                            resetIndex = i;
                            break;
                        }
                    }

                    // Если нашли кнопку сброса, берем следующую кнопку после неё
                    if (resetButton && resetIndex < sortedButtons.length - 1) {
                        // Берем кнопку справа от кнопки сброса
                        rightButton = sortedButtons[resetIndex + 1];
                        rightButtonInfo = {
                            found: true,
                            id: rightButton.id || '',
                            className: rightButton.className || '',
                            tagName: rightButton.tagName,
                            text: rightButton.textContent?.trim() || rightButton.value || '',
                            position: 'right of reset',
                            isRightmost: resetIndex + 1 === sortedButtons.length - 1
                        };
                        break;
                    }

                    // Если не нашли кнопку сброса, берем самую правую кнопку
                    if (sortedButtons.length > 0) {
                        // Берем самую правую кнопку
                        rightButton = sortedButtons[sortedButtons.length - 1];
                        rightButtonInfo = {
                            found: true,
                            id: rightButton.id || '',
                            className: rightButton.className || '',
                            tagName: rightButton.tagName,
                            text: rightButton.textContent?.trim() || rightButton.value || '',
                            position: 'rightmost',
                            isRightmost: true
                        };
                        break;
                    }
                }

                return rightButtonInfo || { found: false };
            }
        """)

        if button_info and button_info.get("found", False):
            logger.info(f"Found RIGHT search button via JavaScript: {button_info}")

            # Создаем селектор для найденной кнопки
            tag_name = button_info.get("tagName", "").lower()
            element_id = button_info.get("id")
            class_name = button_info.get("className")
            button_text = button_info.get("text", "")

            selectors = []

            # Создаем селекторы в порядке специфичности
            if element_id:
                selectors.append(f"{tag_name}#{element_id}")

            if class_name:
                # Преобразуем имя класса в CSS-селектор
                class_selector = class_name.replace(" ", ".")
                selectors.append(f"{tag_name}.{class_selector}")

            if button_text:
                selectors.append(f"{tag_name}:has-text('{button_text}')")

            # Добавляем общий селектор как последний вариант
            selectors.append(f"{tag_name}[type='submit']")

            # Пробуем каждый селектор
            for selector in selectors:
                logger.debug(f"Trying to create locator from JS-generated selector: {selector}")
                button_locator = page.locator(selector)
                if await button_locator.count() > 0:
                    # Проверяем, что это действительно правая кнопка
                    is_rightmost = await button_locator.evaluate("""
                        (el) => {
                            const form = el.closest('form');
                            if (!form) return true; // Если нет формы, считаем правой

                            const buttons = Array.from(form.querySelectorAll('button, input[type="submit"], input[type="reset"], input[type="button"]'));
                            if (buttons.length <= 1) return true; // Если кнопка одна, считаем правой

                            // Сортируем кнопки по горизонтальной позиции
                            const sortedButtons = buttons.sort((a, b) => {
                                const aRect = a.getBoundingClientRect();
                                const bRect = b.getBoundingClientRect();
                                return aRect.left - bRect.left;
                            });

                            // Проверяем, что это не первая кнопка (не левая)
                            return sortedButtons.indexOf(el) > 0;
                        }
                    """)

                    if is_rightmost:
                        logger.info(f"Successfully created locator for RIGHT button: {selector}")
                        return button_locator
                    else:
                        logger.warning(f"Found button with selector {selector}, but it's not the right button. Skipping.")

    except Exception as e:
        logger.warning(f"Error finding RIGHT search button via JavaScript: {e}")

    # ВТОРОЙ МЕТОД: Используем селекторы, но с дополнительной проверкой на правую кнопку
    logger.debug("Falling back to selectors to find the RIGHT search button...")

    # Список селекторов для поиска кнопки, в порядке приоритета
    button_selectors = [
        # Более точные селекторы для поиска
        "button[type='submit']:has-text('Search'):not(:has-text('Reset'))",
        "button[type='submit']:has-text('SEARCH'):not(:has-text('RESET'))",
        "input[type='submit'][value~='Search' i]:not([value~='Reset' i])",
        "button:has-text('Rechercher'):not(:has-text('Réinitialiser'))",
        "button:has-text('Trouver'):not(:has-text('Effacer'))",
        "button[id*='search'][type='submit']",
        "button[class*='search'][type='submit']:not([class*='reset'])",
        # Селекторы по роли и тексту
        "role=button[name='Search']",
        "role=button[name='SEARCH']",
        "role=button[name='Rechercher']",
        # Селекторы по классам Bootstrap
        "button.btn-primary:has-text('Search')",
        "button.btn-success:has-text('Search')",
        "button.btn-default:has-text('Search')",
        # Селекторы по атрибутам
        "button[value='Search']",
        "button[name='search']",
        "button[name='Search']",
        # Более общие селекторы
        "form button[type='submit']:last-child",  # Обычно кнопка поиска - последняя в форме
        "form input[type='submit']:last-child",
        "form button:last-child",
        "button:has-text('Search')",
        "button:has-text('Rechercher')",
    ]

    for idx, selector in enumerate(button_selectors):
        logger.debug(f"Trying selector #{idx + 1}: {selector}")
        try:
            button_locator = page.locator(selector)
            count = await button_locator.count()
            if count > 0:
                # Нашли потенциальные кнопки, проверяем первую видимую
                for i in range(count):
                    button = button_locator.nth(i)
                    try:
                        if await button.is_visible(timeout=1000): # Короткий таймаут для проверки видимости
                            # Дополнительная проверка, что это не кнопка сброса
                            button_text = (await button.inner_text(timeout=500) or "").lower()
                            button_value = (await button.get_attribute("value", timeout=500) or "").lower()
                            button_type = (await button.get_attribute("type", timeout=500) or "").lower()

                            is_reset = ("reset" in button_text or "сброс" in button_text or
                                        "clear" in button_text or "effacer" in button_text or
                                        "réinitialiser" in button_text.lower() or
                                        "reset" in button_value or
                                        button_type == "reset" or
                                        # Проверяем положение кнопки - если она слева, то это, скорее всего, кнопка сброса
                                        await button.evaluate("""
                                            (el) => {
                                                // Ищем все кнопки в форме
                                                const form = el.closest('form');
                                                if (!form) return false;

                                                const buttons = Array.from(form.querySelectorAll('button, input[type="submit"], input[type="reset"]'));
                                                if (buttons.length <= 1) return false; // Если кнопка одна, то это не сброс

                                                // Если кнопка первая в форме, то это, скорее всего, сброс
                                                return buttons.indexOf(el) === 0;
                                            }
                                        """))

                            if not is_reset:
                                logger.info(f"Found visible search button with selector: {selector} (index {i})")
                                return button
                            else:
                                logger.debug(f"Selector '{selector}' (index {i}) matched a reset button, skipping.")
                        else:
                            logger.debug(f"Button with selector '{selector}' (index {i}) found but not visible.")
                    except PlaywrightTimeout:
                         logger.debug(f"Timeout checking visibility/attributes for button '{selector}' (index {i}).")
                    except Exception as e_inner:
                        logger.debug(f"Error checking button {selector} (index {i}): {e_inner}")
            else:
                logger.debug(f"Selector '{selector}' did not match any elements.")

        except PlaywrightError as e:
            logger.warning(f"Playwright error with selector {selector}: {e}")
            continue
        except Exception as e:
            logger.error(f"Unexpected error checking selector {selector}: {e}")
            continue

    # Если не нашли по селекторам, пробуем найти через JavaScript
    try:
        logger.debug("Attempting to find search button via JavaScript...")
        button_info = await page.evaluate("""
            () => {
                // Функция для определения, является ли кнопка кнопкой поиска
                function isSearchButton(btn) {
                    const text = (btn.textContent || btn.value || '').toLowerCase();
                    return text.includes('search') || text.includes('find') ||
                           text.includes('rechercher') || text.includes('trouver') ||
                           btn.id?.toLowerCase().includes('search') ||
                           btn.name?.toLowerCase().includes('search') ||
                           btn.className?.toLowerCase().includes('search');
                }

                // Функция для определения, является ли кнопка кнопкой сброса
                function isResetButton(btn) {
                    const text = (btn.textContent || btn.value || '').toLowerCase();
                    return text.includes('reset') || text.includes('clear') ||
                           text.includes('réinitialiser') || text.includes('effacer') ||
                           btn.type === 'reset' ||
                           btn.id?.toLowerCase().includes('reset') ||
                           btn.name?.toLowerCase().includes('reset') ||
                           btn.className?.toLowerCase().includes('reset');
                }

                // Ищем все формы на странице
                const forms = document.querySelectorAll('form');
                for (const form of forms) {
                    // Получаем все кнопки в форме
                    const buttons = Array.from(form.querySelectorAll('button, input[type="submit"], input[type="reset"], input[type="button"]'));
                    if (buttons.length < 2) continue; // Нам нужны формы с минимум 2 кнопками

                    // Ищем кнопку сброса (обычно она слева)
                    let resetButton = null;
                    for (const btn of buttons) {
                        if (isResetButton(btn)) {
                            resetButton = btn;
                            break;
                        }
                    }

                    if (resetButton) {
                        // Если нашли кнопку сброса, ищем кнопку справа от неё
                        const resetRect = resetButton.getBoundingClientRect();

                        // Сортируем кнопки по горизонтальной позиции
                        const sortedButtons = buttons
                            .filter(btn => btn !== resetButton) // Исключаем кнопку сброса
                            .sort((a, b) => {
                                const aRect = a.getBoundingClientRect();
                                const bRect = b.getBoundingClientRect();
                                return aRect.left - bRect.left;
                            });

                        // Ищем первую кнопку справа от кнопки сброса
                        for (const btn of sortedButtons) {
                            const btnRect = btn.getBoundingClientRect();
                            if (btnRect.left > resetRect.left) {
                                // Нашли кнопку справа от кнопки сброса
                                return {
                                    found: true,
                                    id: btn.id || '',
                                    className: btn.className || '',
                                    tagName: btn.tagName,
                                    text: btn.textContent?.trim() || btn.value || '',
                                    position: 'right of reset',
                                    xpath: getXPath(btn)
                                };
                            }
                        }
                    }

                    // Если не нашли кнопку сброса или кнопку справа от неё,
                    // берем самую правую кнопку в форме (обычно это кнопка поиска)
                    if (buttons.length >= 2) {
                        // Сортируем кнопки по горизонтальной позиции
                        const sortedButtons = buttons.sort((a, b) => {
                            const aRect = a.getBoundingClientRect();
                            const bRect = b.getBoundingClientRect();
                            return bRect.left - aRect.left; // Сортировка по убыванию (справа налево)
                        });

                        // Берем самую правую кнопку, которая не является кнопкой сброса
                        for (const btn of sortedButtons) {
                            if (!isResetButton(btn)) {
                                return {
                                    found: true,
                                    id: btn.id || '',
                                    className: btn.className || '',
                                    tagName: btn.tagName,
                                    text: btn.textContent?.trim() || btn.value || '',
                                    position: 'rightmost',
                                    xpath: getXPath(btn)
                                };
                            }
                        }
                    }
                }

                // Функция для получения XPath элемента
                function getXPath(element) {
                    if (!element) return '';

                    // Если у элемента есть id, используем его
                    if (element.id) {
                        return `//*[@id="${element.id}"]`;
                    }

                    // Иначе строим путь от родителя
                    let path = '';
                    let current = element;

                    while (current && current.nodeType === Node.ELEMENT_NODE) {
                        let index = 1;
                        let sibling = current.previousElementSibling;

                        while (sibling) {
                            if (sibling.nodeName === current.nodeName) {
                                index++;
                            }
                            sibling = sibling.previousElementSibling;
                        }

                        const nodeName = current.nodeName.toLowerCase();
                        const pathIndex = (index > 1) ? `[${index}]` : '';
                        path = `/${nodeName}${pathIndex}${path}`;

                        current = current.parentNode;
                    }

                    return path;
                }

                return { found: false };
            }
        """)

        if button_info and button_info.get("found", False):
            logger.info(f"Found search button via JavaScript: {button_info}")

            # Если нашли кнопку через JS, пробуем создать локатор по XPath
            xpath = button_info.get("xpath")
            if xpath:
                logger.debug(f"Trying to create locator from XPath: {xpath}")
                button_locator = page.locator(xpath)
                if await button_locator.count() > 0 and await button_locator.is_visible(timeout=2000):
                    logger.info(f"Successfully created locator from XPath: {xpath}")
                    return button_locator

            # Если не удалось создать локатор по XPath, пробуем по другим атрибутам
            tag_name = button_info.get("tagName", "").lower()
            element_id = button_info.get("id")
            class_name = button_info.get("className")

            if element_id:
                selector = f"{tag_name}#{element_id}"
                logger.debug(f"Trying to create locator from ID: {selector}")
                button_locator = page.locator(selector)
                if await button_locator.count() > 0 and await button_locator.is_visible(timeout=2000):
                    logger.info(f"Successfully created locator from ID: {selector}")
                    return button_locator

            if class_name:
                # Преобразуем имя класса в CSS-селектор
                class_selector = class_name.replace(" ", ".")
                selector = f"{tag_name}.{class_selector}"
                logger.debug(f"Trying to create locator from class: {selector}")
                button_locator = page.locator(selector)
                if await button_locator.count() > 0 and await button_locator.is_visible(timeout=2000):
                    logger.info(f"Successfully created locator from class: {selector}")
                    return button_locator

    except Exception as e:
        logger.warning(f"Error finding search button via JavaScript: {e}")
        await save_debug_snapshot(page, "js_search_button_error")

    logger.warning("Search button not found using standard selectors or JS fallback.")
    await save_debug_snapshot(page, "search_button_not_found")
    return None

# precondition decorator already applied
@postcondition(lambda result, *args, **kwargs: isinstance(result, bool), "Результат должен быть булевым значением")
async def submit_form(page: Page) -> bool:
    """Отправляет первую найденную форму напрямую с помощью JavaScript."""
    logger.debug("Attempting to submit form directly using JavaScript...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    # Делаем снимок перед отправкой формы
    await save_debug_snapshot(page, "before_form_submit")

    # Проверяем, решена ли капча
    captcha_status = await is_captcha_solved(page)
    if not captcha_status:
        logger.warning("CAPTCHA is not solved before form submission. This may cause issues.")
        await save_debug_snapshot(page, "captcha_not_solved_before_form_submit")
    else:
        logger.info("CAPTCHA check passed before form submission.")

    try:
        # Используем expect_navigation для ожидания перехода после отправки формы
        async with page.expect_navigation(timeout=30000, wait_until="domcontentloaded"):
            # Находим первую форму и отправляем ее
            submitted = await page.evaluate("""
                const forms = document.querySelectorAll('form');
                if (forms.length > 0) {
                    // Добавляем проверку на наличие капчи перед отправкой
                    const recaptchaResponse = forms[0].querySelector('#g-recaptcha-response');
                    if (recaptchaResponse && !recaptchaResponse.value) {
                         console.warn('Attempting to submit form without reCAPTCHA token!');
                         // Можно вернуть false или ошибку, если токен обязателен
                         // return false;
                    }
                    try {
                         forms[0].submit();
                         console.log('Form submitted directly via JS');
                         return true;
                    } catch (e) {
                         console.error('Error submitting form via JS:', e);
                         return false;
                    }
                } else {
                    console.warn('No form found to submit.');
                    return false;
                }
            """)
        if submitted:
            logger.info("Form submitted successfully via JavaScript.")
            # Дополнительное ожидание после навигации
            await page.wait_for_timeout(1000)
            return True
        else:
            logger.warning("JavaScript reported no form found or failed to submit.")
            await save_debug_snapshot(page, "form_submit_js_failed")
            return False
    except PlaywrightTimeout as e:
        logger.warning(f"Timeout waiting for navigation after form submission: {e}")
        # Делаем снимок после таймаута
        await save_debug_snapshot(page, "form_submit_timeout")

        # Проверяем наличие результатов
        await page.wait_for_timeout(3000)  # Ждем возможного обновления AJAX
        try:
            results_exist = await page.locator(".search-result-item, .result-card, article.cpa-entry, div.profile-summary, li.item").count() > 0
            if results_exist:
                logger.info("Results found after form submission without navigation. Considering successful.")
                return True
        except Exception as check_e:
            logger.debug(f"Error checking for results after form submission: {check_e}")

        # Навигации могло не быть, но форма могла отправиться (AJAX?)
        # В этом случае считаем неуспехом, так как ожидали навигацию
        return False
    except Exception as e:
        logger.error(f"Error submitting form via JavaScript: {e}")
        await save_debug_snapshot(page, "form_submit_error")
        return False

async def analyze_page_structure(page: Page) -> None:
    """Анализирует структуру страницы и сохраняет информацию для отладки."""
    if not (args and args.debug and DEBUG_DIR):
        return # Не сохраняем, если отладка выключена или директория не создана

    logger.debug("Analyzing page structure for debugging...")

    # Сохраняем скриншот и HTML
    await save_debug_snapshot(page, "page_structure_analysis")

    # Анализируем формы, капчу, кнопки (JS код остается без изменений)
    try:
        page_analysis = await page.evaluate("""
        () => {
            const analysis = { forms: [], captcha: {}, buttons: [] };

            // Анализ форм
            const forms = document.querySelectorAll('form');
            analysis.forms = Array.from(forms).map((form, i) => {
                const formInfo = {
                    index: i,
                    id: form.id || null,
                    action: form.action || null,
                    method: form.method || null,
                    has_recaptcha_response: !!form.querySelector('#g-recaptcha-response'),
                    elements: Array.from(form.elements).map(el => ({
                        name: el.name || null, id: el.id || null, type: el.type || null,
                        value: el.type === 'password' ? '***' : (el.value ? el.value.substring(0, 50) + (el.value.length > 50 ? '...' : '') : null),
                        isVisible: el.offsetParent !== null,
                        isDisabled: el.disabled
                    })),
                    buttons: Array.from(form.querySelectorAll('button, input[type="submit"], input[type="button"], input[type="reset"]')).map(btn => {
                         const rect = btn.getBoundingClientRect();
                         return {
                            type: btn.type || null, text: (btn.textContent || btn.value || '').trim().substring(0, 50),
                            id: btn.id || null, name: btn.name || null, isVisible: btn.offsetParent !== null,
                            isDisabled: btn.disabled,
                            position: { x: rect.x, y: rect.y, w: rect.width, h: rect.height }
                         };
                    })
                };
                return formInfo;
            });

            // Анализ капчи
            const recaptchaDiv = document.querySelector('.g-recaptcha');
            analysis.captcha.recaptchaDiv = recaptchaDiv ? {
                sitekey: recaptchaDiv.getAttribute('data-sitekey'),
                size: recaptchaDiv.getAttribute('data-size'),
                theme: recaptchaDiv.getAttribute('data-theme'),
                callback: recaptchaDiv.getAttribute('data-callback'),
                position: recaptchaDiv.getBoundingClientRect()
            } : null;

            analysis.captcha.recaptchaIframes = Array.from(document.querySelectorAll('iframe[src*="recaptcha"]')).map(iframe => ({
                 src: iframe.src ? iframe.src.substring(0, 100) + (iframe.src.length > 100 ? '...' : '') : null,
                 position: iframe.getBoundingClientRect(),
                 isVisible: iframe.offsetParent !== null
            }));

            const recaptchaResponse = document.querySelector('#g-recaptcha-response');
            analysis.captcha.recaptchaResponse = recaptchaResponse ? {
                 valueExists: !!recaptchaResponse.value,
                 valueLength: recaptchaResponse.value ? recaptchaResponse.value.length : 0
            } : null;

            // Анализ всех кнопок
            const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"], input[type="reset"]');
            analysis.buttons = Array.from(allButtons).map((button, i) => {
                 const rect = button.getBoundingClientRect();
                 return {
                     index: i, type: button.type || null, text: (button.textContent || button.value || '').trim().substring(0, 50),
                     id: button.id || null, name: button.name || null, classes: button.className || null,
                     isVisible: button.offsetParent !== null, isDisabled: button.disabled,
                     position: { x: rect.x, y: rect.y, w: rect.width, h: rect.height,
                                 inViewport: rect.top >= 0 && rect.left >= 0 && rect.bottom <= window.innerHeight && rect.right <= window.innerWidth }
                 };
            });

            return analysis;
        }
        """)

        # Логируем основную информацию
        logger.debug(f"Page Analysis: {len(page_analysis['forms'])} forms, "
                     f"{len(page_analysis['captcha'].get('recaptchaIframes', []))} reCAPTCHA iframes, "
                     f"{len(page_analysis['buttons'])} total buttons.")

        # Сохраняем детальный JSON
        debug_path = await manage_debug_files('page_structure_analysis.json')
        if debug_path:
            try:
                with open(debug_path, "w", encoding="utf-8") as f:
                    json.dump(page_analysis, f, indent=2, ensure_ascii=False, default=lambda o: '<not serializable>') # Handle non-serializable parts
                logger.debug(f"Detailed page analysis saved: {Path(debug_path).name}")
            except (IOError, TypeError) as e:
                 logger.warning(f"Failed to save page analysis JSON {debug_path}: {e}")

    except PlaywrightError as e:
         logger.warning(f"Failed to execute page analysis script: {e}")
    except Exception as e:
         logger.error(f"Unexpected error during page analysis: {e}")


# precondition decorator already applied
@postcondition(lambda result, *args, **kwargs: isinstance(result, bool), "Результат должен быть булевым значением")
async def click_search_button(page: Page) -> bool:
    """Надежный клик на ПРАВУЮ кнопку поиска с использованием различных методов.

    ВАЖНО: Функция должна находить и кликать по ПРАВОЙ кнопке поиска, а не по левой кнопке сброса.
    """
    logger.info("Attempting to click the RIGHT search button (not the reset/left one)...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    # Анализируем структуру перед кликом (если включена отладка)
    await analyze_page_structure(page)
    await save_debug_snapshot(page, "before_search_click")

    # Проверяем, решена ли капча (если она есть)
    captcha_status = await is_captcha_solved(page)
    if not captcha_status:
        logger.warning("CAPTCHA is not solved before clicking search button. This may cause issues.")
        await save_debug_snapshot(page, "captcha_not_solved_before_search")
    else:
        logger.info("CAPTCHA check passed before clicking search button.")

    # Сначала пытаемся найти кнопку с помощью улучшенной функции find_search_button
    search_button: Optional[Locator] = await find_search_button(page)

    if search_button:
        logger.info("Search button found using find_search_button.")
        try:
            # Прокручиваем к кнопке и делаем паузу для стабилизации страницы
            logger.debug("Scrolling to search button...")
            try:
                # Используем JavaScript для прокрутки к элементу и его видимости
                await page.evaluate("""
                    (selector) => {
                        const button = document.querySelector(selector);
                        if (button) {
                            // Прокрутка к элементу
                            button.scrollIntoView({behavior: 'smooth', block: 'center'});
                            // Дополнительная прокрутка для уверенности
                            window.scrollBy(0, -100);
                        }
                    }
                """, search_button.evaluate("el => el.tagName + (el.id ? '#' + el.id : '') + (el.className ? '.' + el.className.replace(/ /g, '.') : '')"))
                logger.debug("Used JavaScript to scroll to button")
            except Exception as e:
                logger.warning(f"JavaScript scroll failed: {e}, falling back to Playwright scroll")
                await search_button.scroll_into_view_if_needed(timeout=10000)

            await page.wait_for_timeout(1500)  # Увеличенная пауза для стабилизации страницы

            # Проверяем видимость и кликабельность перед кликом
            if not await search_button.is_visible(timeout=5000):
                 logger.warning("Search button became hidden after scroll.")
                 await save_debug_snapshot(page, "search_button_hidden_after_scroll")
                 # Попробуем JS клик как запасной вариант ниже
            elif await search_button.is_disabled(timeout=5000):
                 logger.warning("Search button is disabled.")
                 await save_debug_snapshot(page, "search_button_disabled")
                 # Проверяем, может быть капча не решена
                 if not await is_captcha_solved(page):
                     logger.error("Search button is disabled because CAPTCHA is not solved.")
                     return False
                 # Попробуем JS клик как запасной вариант ниже
            else:
                 logger.debug("Attempting standard Playwright click...")
                 # Делаем снимок непосредственно перед кликом
                 await save_debug_snapshot(page, "immediately_before_search_click")
                 try:
                      # Сначала пробуем прямой JavaScript клик
                      logger.debug("Attempting direct JavaScript click first...")
                      button_selector = await search_button.evaluate("el => el.tagName + (el.id ? '#' + el.id : '') + (el.className ? '.' + el.className.replace(/ /g, '.') : '')")

                      # Используем expect_navigation для ожидания перехода после клика
                      async with page.expect_navigation(timeout=60000, wait_until="domcontentloaded"):
                           await page.evaluate("""
                               (selector) => {
                                   const button = document.querySelector(selector);
                                   if (button) {
                                       console.log('Found button, clicking via JS');
                                       button.click();
                                   } else {
                                       console.error('Button not found with selector:', selector);
                                   }
                               }
                           """, button_selector)
                      logger.info("JavaScript direct click successful, navigation detected.")
                      await page.wait_for_timeout(1000) # Пауза после навигации
                      return True
                 except PlaywrightTimeout as js_timeout:
                      logger.warning(f"Timeout waiting for navigation after JS click: {js_timeout}. Trying standard click...")
                      try:
                          # Используем expect_navigation для ожидания перехода после клика
                          async with page.expect_navigation(timeout=60000, wait_until="domcontentloaded"):
                               await search_button.click(delay=200, timeout=15000, force=True) # Добавляем force=True
                          logger.info("Standard Playwright click successful, navigation detected.")
                          await page.wait_for_timeout(1000) # Пауза после навигации
                          return True
                      except PlaywrightTimeout as std_timeout:
                          logger.warning(f"Timeout waiting for navigation after standard click: {std_timeout}. Page might have updated via AJAX.")
                 except PlaywrightTimeout as nav_timeout:
                     logger.warning(f"Timeout waiting for navigation after standard click: {nav_timeout}. Page might have updated via AJAX.")
                     # Проверяем, изменился ли URL или появились ли результаты
                     await page.wait_for_timeout(3000) # Ждем возможного обновления AJAX
                     await save_debug_snapshot(page, "after_search_click_no_navigation")
                     # Проверяем наличие результатов
                     try:
                         results_exist = await page.locator(".search-result-item, .result-card, article.cpa-entry, div.profile-summary, li.item").count() > 0
                         if results_exist:
                             logger.info("Results found after click without navigation. Considering successful.")
                             return True
                     except Exception as e:
                         logger.debug(f"Error checking for results: {e}")

                     logger.info("Assuming AJAX update, proceeding as successful click.")
                     return True # Считаем успехом, если навигации не было, но и ошибки клика тоже
                 except PlaywrightError as click_error:
                     logger.warning(f"Standard Playwright click failed: {click_error}")
                     await save_debug_snapshot(page, "search_button_click_error_standard")
                     # Переходим к запасным вариантам

        except Exception as e:
            logger.warning(f"Error during standard click attempt: {e}")
            await save_debug_snapshot(page, "search_button_click_error")

    else:
        logger.warning("Search button not found by find_search_button initially.")

    # --- Запасные варианты, если стандартный клик не сработал или кнопка не найдена ---

    logger.info("Standard click failed or button not found initially, trying JavaScript click methods...")

    # МЕТОД 2: Прямой JavaScript-клик на ПРАВУЮ кнопку
    try:
        logger.debug("Attempting direct JavaScript click on RIGHT search button...")
        # Делаем снимок перед JS-кликом
        await save_debug_snapshot(page, "before_js_right_button_click")

        # JavaScript для клика на ПРАВУЮ кнопку
        clicked = await page.evaluate("""
            () => {
                // Функция для определения, является ли кнопка кнопкой сброса
                function isResetButton(btn) {
                    const text = (btn.textContent || btn.value || '').toLowerCase();
                    return text.includes('reset') || text.includes('clear') ||
                           text.includes('réinitialiser') || text.includes('effacer') ||
                           btn.type === 'reset' ||
                           btn.id?.toLowerCase().includes('reset') ||
                           btn.name?.toLowerCase().includes('reset') ||
                           btn.className?.toLowerCase().includes('reset');
                }

                // Ищем все формы на странице
                const forms = document.querySelectorAll('form');
                let buttonClicked = false;
                let debugInfo = { formCount: forms.length, attempts: [] };

                for (const form of forms) {
                    // Получаем все кнопки в форме
                    const buttons = Array.from(form.querySelectorAll('button, input[type="submit"], input[type="reset"], input[type="button"]'));
                    if (buttons.length < 2) {
                        debugInfo.attempts.push({
                            form: form.id || 'unnamed-form',
                            buttonCount: buttons.length,
                            action: 'skipped - not enough buttons'
                        });
                        continue; // Нам нужны формы с минимум 2 кнопками
                    }

                    // Сортируем кнопки по горизонтальной позиции (слева направо)
                    const sortedButtons = buttons.sort((a, b) => {
                        const aRect = a.getBoundingClientRect();
                        const bRect = b.getBoundingClientRect();
                        return aRect.left - bRect.left;
                    });

                    // Ищем кнопку сброса (обычно она слева)
                    let resetButton = null;
                    let resetIndex = -1;

                    for (let i = 0; i < sortedButtons.length; i++) {
                        const btn = sortedButtons[i];
                        if (isResetButton(btn)) {
                            resetButton = btn;
                            resetIndex = i;
                            break;
                        }
                    }

                    // Если нашли кнопку сброса, кликаем по кнопке справа от неё
                    if (resetButton && resetIndex < sortedButtons.length - 1) {
                        // Берем кнопку справа от кнопки сброса
                        const rightButton = sortedButtons[resetIndex + 1];

                        // Проверяем, что кнопка видима
                        if (rightButton.offsetParent !== null) {
                            console.log('Clicking button RIGHT of reset button');
                            rightButton.click();
                            buttonClicked = true;
                            debugInfo.attempts.push({
                                form: form.id || 'unnamed-form',
                                action: 'clicked button right of reset',
                                buttonText: rightButton.textContent || rightButton.value || 'no-text',
                                buttonType: rightButton.type || 'no-type'
                            });
                            break;
                        }
                    }

                    // Если не нашли кнопку сброса, кликаем по самой правой кнопке
                    if (!buttonClicked && sortedButtons.length > 0) {
                        // Берем самую правую кнопку, которая не является кнопкой сброса
                        for (let i = sortedButtons.length - 1; i >= 0; i--) {
                            const rightmostButton = sortedButtons[i];
                            if (!isResetButton(rightmostButton) && rightmostButton.offsetParent !== null) {
                                console.log('Clicking rightmost non-reset button');
                                rightmostButton.click();
                                buttonClicked = true;
                                debugInfo.attempts.push({
                                    form: form.id || 'unnamed-form',
                                    action: 'clicked rightmost non-reset button',
                                    buttonText: rightmostButton.textContent || rightmostButton.value || 'no-text',
                                    buttonType: rightmostButton.type || 'no-type'
                                });
                                break;
                            }
                        }
                    }

                    if (buttonClicked) break;
                }

                // Если не нашли подходящую кнопку в формах, ищем кнопки вне форм
                if (!buttonClicked) {
                    const allButtons = Array.from(document.querySelectorAll('button, input[type="submit"]'));

                    // Сортируем кнопки по горизонтальной позиции (справа налево)
                    const sortedButtons = allButtons.sort((a, b) => {
                        const aRect = a.getBoundingClientRect();
                        const bRect = b.getBoundingClientRect();
                        return bRect.left - aRect.left; // Сортировка по убыванию (справа налево)
                    });

                    // Берем самую правую кнопку, которая не является кнопкой сброса
                    for (const btn of sortedButtons) {
                        if (!isResetButton(btn) && btn.offsetParent !== null) {
                            console.log('Clicking rightmost button outside form');
                            btn.click();
                            buttonClicked = true;
                            debugInfo.attempts.push({
                                action: 'clicked rightmost button outside form',
                                buttonText: btn.textContent || btn.value || 'no-text',
                                buttonType: btn.type || 'no-type'
                            });
                            break;
                        }
                    }
                }

                return { clicked: buttonClicked, debug: debugInfo };
            }
        """)

        logger.debug(f"JavaScript click result: {clicked}")

        if clicked.get("clicked", False):
            logger.info("JavaScript direct click on RIGHT button initiated.")
            # Ждем навигации или обновления страницы
            try:
                await page.wait_for_navigation(timeout=30000, wait_until="domcontentloaded")
                logger.info("Navigation detected after JavaScript click on RIGHT button.")
                await page.wait_for_timeout(1000) # Пауза после навигации
                return True
            except PlaywrightTimeout:
                logger.info("No navigation after JavaScript click, checking for AJAX updates...")
                # Проверяем, обновилась ли страница через AJAX
                await page.wait_for_timeout(3000)
                await save_debug_snapshot(page, "after_js_right_button_click_no_navigation")
                try:
                    await page.wait_for_selector(
                        ".search-results, .results, .no-results, .error-message, div[class*='result'], div[class*='search']",
                        state="attached",
                        timeout=10000
                    )
                    logger.info("Results appeared without navigation (AJAX update).")
                    return True
                except PlaywrightTimeout:
                    logger.warning("No results found after JavaScript click, but proceeding anyway.")
                    return True  # Возвращаем True, так как клик был выполнен
        else:
            logger.warning("JavaScript click failed - no suitable RIGHT button found.")
    except PlaywrightTimeout as nav_timeout:
        logger.warning(f"Timeout waiting for navigation after JS click: {nav_timeout}. Assuming AJAX update.")
        await page.wait_for_timeout(3000)
        await save_debug_snapshot(page, "after_js_click_no_navigation")

        # Проверяем наличие результатов
        try:
            results_exist = await page.locator(".search-result-item, .result-card, article.cpa-entry, div.profile-summary, li.item").count() > 0
            if results_exist:
                logger.info("Results found after JS click without navigation. Considering successful.")
                return True
        except Exception as e:
            logger.debug(f"Error checking for results after JS click: {e}")

        return True # Считаем успехом
    except Exception as js_error:
        logger.error(f"Error executing JavaScript click: {js_error}")
        await save_debug_snapshot(page, "js_click_error")

    # МЕТОД 3: Отправить форму напрямую
    logger.info("JavaScript click failed, trying to submit form directly...")
    if await submit_form(page):
         logger.info("Form submitted directly.")
         return True

    # МЕТОД 4: Нажать Enter в форме (менее надежно, но как последний шанс)
    logger.info("Form submission failed, trying to press Enter in the form...")
    try:
        forms = page.locator("form")
        if await forms.count() > 0:
            first_form = forms.first
            # Попробуем найти поле ввода для фокуса перед нажатием Enter
            input_field = first_form.locator("input[type='text'], input[type='search'], textarea").first
            if await input_field.count() > 0 and await input_field.is_visible(timeout=1000):
                 logger.debug("Focusing input field before pressing Enter.")
                 await input_field.focus(timeout=5000)
                 await page.wait_for_timeout(200)
                 async with page.expect_navigation(timeout=30000, wait_until="domcontentloaded"):
                     await input_field.press("Enter")
                 logger.info("Pressed Enter in form input field, navigation detected.")
                 await page.wait_for_timeout(1000)
                 return True
            else:
                 # Если поля нет, жмем Enter на самой форме
                 logger.debug("Pressing Enter on the form element itself.")
                 async with page.expect_navigation(timeout=30000, wait_until="domcontentloaded"):
                      await first_form.press("Enter")
                 logger.info("Pressed Enter on form element, navigation detected.")
                 await page.wait_for_timeout(1000)
                 return True
        else:
            logger.warning("No form found to press Enter in.")
    except PlaywrightTimeout as nav_timeout:
         logger.warning(f"Timeout waiting for navigation after pressing Enter: {nav_timeout}. Assuming AJAX.")
         await page.wait_for_timeout(3000)
         return True # Считаем успехом
    except Exception as enter_error:
        logger.error(f"Error pressing Enter in form: {enter_error}")
        await save_debug_snapshot(page, "enter_press_error")

    logger.error("All methods to click search button or submit form failed.")
    await save_debug_snapshot(page, "search_click_failed_all_methods")
    return False


# ---------------------------------------------------------------------------
# Captcha solving (Anti‑Captcha)
# ---------------------------------------------------------------------------

# Глобальная сессия aiohttp
anticaptcha_session: Optional[aiohttp.ClientSession] = None

async def get_anticaptcha_session() -> aiohttp.ClientSession:
    """Возвращает существующую или создает новую сессию для запросов к Anti-Captcha API."""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        logger.debug("Creating new aiohttp session for Anti-Captcha")
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session

async def close_anticaptcha_session() -> None:
    """Закрывает сессию для запросов к Anti-Captcha API."""
    global anticaptcha_session
    if anticaptcha_session and not anticaptcha_session.closed:
        logger.debug("Closing aiohttp session for Anti-Captcha")
        await anticaptcha_session.close()
        anticaptcha_session = None

async def anticaptcha_request(method: str, **payload) -> Dict[str, Any]:
    """Выполняет запрос к API Anti-Captcha."""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()

    # Добавляем clientKey ко всем запросам, если он не передан явно
    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY

    logger.debug(f"Sending request to Anti-Captcha: {method}, payload keys: {list(payload.keys())}")

    try:
        async with session.post(url, json=payload, timeout=30) as resp: # Добавляем таймаут
            resp.raise_for_status() # Проверяем HTTP статус
            response_json = await resp.json()
            logger.debug(f"Anti-Captcha response ({method}): {response_json}")
            return response_json
    except aiohttp.ClientResponseError as e:
        logger.error(f"Anti-Captcha API HTTP Error ({method}, status={e.status}): {e.message}")
        # Попытка прочитать тело ответа, если возможно
        error_body = await e.response.text()
        logger.error(f"Anti-Captcha API Error Body: {error_body}")
        return {"errorId": 1, "errorCode": f"HTTP_{e.status}", "errorDescription": e.message}
    except asyncio.TimeoutError:
        logger.error(f"Anti-Captcha API Timeout ({method})")
        return {"errorId": 1, "errorCode": "TIMEOUT_ERROR", "errorDescription": "Request timed out"}
    except Exception as e:
        logger.error(f"Error during Anti-Captcha API request ({method}): {e}")
        return {"errorId": 1, "errorCode": "CONNECTION_ERROR", "errorDescription": str(e)}

# precondition decorator already applied
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple) and len(result) == 2 and isinstance(result[0], bool) and (result[1] is None or isinstance(result[1], int)),
    "Результат должен быть кортежем (bool, Optional[int])"
)
async def create_captcha_task(site_key: str, page_url: str, page: Optional[Page] = None) -> Tuple[bool, Optional[int]]:
    """Создает задачу на решение капчи в Anti-Captcha."""
    assert isinstance(site_key, str) and len(site_key) > 0, "site_key должен быть непустой строкой"
    assert isinstance(page_url, str) and len(page_url) > 0, "page_url должен быть непустой строкой"

    if not ANTICAPTCHA_KEY:
        logger.error("ANTICAPTCHA_API_KEY is not set. Cannot create task.")
        return False, None

    logger.info(f"Creating Anti-Captcha task, sitekey={site_key[:8]}..., page={page_url}")

    # Получаем User-Agent браузера для лучшей совместимости с enterprise reCAPTCHA
    user_agent = None
    if page and not page.is_closed():
        try:
            user_agent = await page.evaluate("() => navigator.userAgent")
            logger.debug(f"Using browser User-Agent: {user_agent}")
        except Exception as e:
            logger.warning(f"Could not get browser User-Agent: {e}")
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            logger.debug(f"Using fallback User-Agent: {user_agent}")
    else:
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        logger.debug(f"Using default User-Agent: {user_agent}")

    create_payload = {
        # "clientKey" будет добавлен в anticaptcha_request
        "task": {
            "type": "NoCaptchaTaskProxyless",
            "websiteURL": page_url,
            "websiteKey": site_key,
        },
        "softId": 8041 # Пример softId, если есть
    }

    # Добавляем User-Agent, если он доступен
    if user_agent:
        create_payload["task"]["userAgent"] = user_agent

    task_resp = await anticaptcha_request("createTask", **create_payload)

    # Детальное логирование ответа API для отладки
    logger.debug(f"Anti-Captcha createTask response: {task_resp}")

    task_id = task_resp.get("taskId")
    error_id = task_resp.get("errorId", 0)

    if error_id > 0 or not task_id:
        error_code = task_resp.get("errorCode", "UNKNOWN_ERROR")
        error_desc = task_resp.get("errorDescription", "Failed to create task")
        logger.error(f"Anti-Captcha createTask failed: {error_code} - {error_desc}")
        # Особая обработка критических ошибок
        if error_code in ["ERROR_KEY_DOES_NOT_EXIST", "ERROR_ZERO_BALANCE"]:
             logger.critical(f"Critical Anti-Captcha error: {error_code}. Stopping.")
             # Можно выбросить исключение, чтобы остановить весь процесс
             raise Exception(f"AntiCaptcha Error: {error_code}")
        return False, None

    logger.info(f"Anti-Captcha task created successfully, Task ID: {task_id}")
    return True, task_id

# precondition decorator already applied
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple) and len(result) == 2 and isinstance(result[0], bool) and (result[1] is None or isinstance(result[1], str)),
     "Результат должен быть кортежем (bool, Optional[str])"
)
async def get_captcha_result(task_id: int, max_attempts: int = 60, initial_wait: int = 5, max_wait_time: int = 10) -> Tuple[bool, Optional[str]]:
    """Ожидает и получает результат решения капчи с адаптивным ожиданием."""
    assert isinstance(task_id, int) and task_id > 0, "task_id должен быть положительным целым числом"
    assert isinstance(max_attempts, int) and max_attempts > 0, "max_attempts должен быть положительным целым числом"
    assert isinstance(initial_wait, int) and initial_wait > 0, "initial_wait должен быть положительным целым числом"
    assert isinstance(max_wait_time, int) and max_wait_time > 0, "max_wait_time должен быть положительным целым числом"

    if not ANTICAPTCHA_KEY:
        logger.error("ANTICAPTCHA_API_KEY is not set. Cannot get task result.")
        return False, None

    logger.info(f"Waiting for Anti-Captcha result for task ID: {task_id} (max attempts: {max_attempts})")
    start_time = time.time()

    await asyncio.sleep(initial_wait) # Первоначальное ожидание перед первым запросом

    for attempt in range(max_attempts):
        logger.debug(f"Attempt {attempt + 1}/{max_attempts} to get result for task {task_id}...")

        try:
            res = await anticaptcha_request("getTaskResult", taskId=task_id)

            # Детальное логирование ответа API для отладки
            logger.debug(f"Anti-Captcha getTaskResult response: {res}")

            status = res.get("status")
            error_id = res.get("errorId", 0)

            if error_id > 0:
                error_code = res.get("errorCode", "UNKNOWN_ERROR")
                error_desc = res.get("errorDescription", "Failed to get task result")
                logger.error(f"Anti-Captcha getTaskResult error: {error_code} - {error_desc}")

                # Обработка критических ошибок
                if error_code in ["ERROR_KEY_DOES_NOT_EXIST", "ERROR_ZERO_BALANCE"]:
                    logger.critical(f"Critical Anti-Captcha error: {error_code}. Stopping.")
                    raise Exception(f"AntiCaptcha Error: {error_code}") # Останавливаем процесс
                # Если задача не найдена или токен истек, возвращаем ошибку
                elif error_code in ["ERROR_TOKEN_EXPIRED", "ERROR_TASK_NOT_FOUND"]:
                    logger.warning(f"Anti-Captcha task expired or not found: {error_code}")
                    return False, None

                # Для других ошибок продолжаем попытки
                logger.warning(f"Non-critical Anti-Captcha error: {error_code}. Continuing...")

            elif status == "ready":
                solution = res.get("solution")
                token = solution.get("gRecaptchaResponse") if solution else None
                if token:
                    elapsed = time.time() - start_time
                    logger.info(f"Anti-Captcha solution received successfully for task {task_id} in {elapsed:.1f}s. Token length: {len(token)}")
                    logger.debug(f"Token: {token[:20]}...")
                    # Дополнительная проверка токена
                    if len(token) < 50:
                        logger.warning("Received suspiciously short token from Anti-Captcha.")
                    return True, token
                else:
                    logger.error(f"Anti-Captcha returned status 'ready' but no token found for task {task_id}. Response: {res}")
                    return False, None
            elif status == "processing":
                logger.debug(f"Task {task_id} is still processing...")
            else:
                 logger.warning(f"Unknown Anti-Captcha status for task {task_id}: {status}. Response: {res}")

        except Exception as e:
            # Обработка исключений, выброшенных из anticaptcha_request или критических ошибок
            if "AntiCaptcha Error" in str(e):
                logger.critical(f"Critical Anti-Captcha error: {e}")
                raise # Пробрасываем критические ошибки дальше
            logger.error(f"Unexpected error during get_captcha_result (attempt {attempt + 1}): {e}")
            # Добавляем небольшую паузу после ошибки перед следующей попыткой
            await asyncio.sleep(2)

        # Адаптивное ожидание перед следующей попыткой
        wait_time = min(initial_wait + attempt, max_wait_time) # Увеличиваем ожидание линейно до max_wait_time
        # Не ждем после последней попытки
        if attempt < max_attempts - 1:
            logger.debug(f"Waiting {wait_time}s before next attempt...")
            await asyncio.sleep(wait_time)

    elapsed = time.time() - start_time
    logger.error(f"Anti-Captcha task {task_id} timed out after {max_attempts} attempts ({elapsed:.1f}s).")
    return False, None

# precondition decorator already applied
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple) and len(result) == 2 and isinstance(result[0], bool) and (result[1] is None or isinstance(result[1], str)),
    "Результат должен быть кортежем (bool, Optional[str])"
)
async def extract_recaptcha_sitekey(page: Page) -> Tuple[bool, Optional[str]]:
    """Извлекает sitekey reCAPTCHA со страницы, пробуя несколько методов."""
    logger.debug("Extracting reCAPTCHA sitekey...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    site_key = None

    # Метод 1: Извлечение из URL iframe (самый надежный для v2 checkbox)
    try:
        # Ищем iframe с чекбоксом (anchor), включая enterprise версию
        iframe_locator = page.locator("iframe[src*='api2/anchor'], iframe[src*='recaptcha'], iframe[src*='enterprise']")
        count = await iframe_locator.count()
        if count > 0:
            logger.debug(f"Found {count} reCAPTCHA iframe(s). Checking each one.")

            # Проверяем каждый iframe
            for i in range(count):
                iframe_element = iframe_locator.nth(i)
                src = await iframe_element.get_attribute("src", timeout=5000)
                if src:
                    logger.debug(f"reCAPTCHA iframe #{i+1} src: {src[:100]}...")
                    parsed_url = urlparse(src)
                    qs = parse_qs(parsed_url.query)

                    # Проверяем параметр k (стандартный)
                    site_key = qs.get("k", [None])[0]
                    if site_key:
                        logger.info(f"Sitekey found in iframe #{i+1} URL parameter 'k': {site_key}")
                        return True, site_key

                    # Проверяем параметр sitekey (enterprise)
                    site_key = qs.get("sitekey", [None])[0]
                    if site_key:
                        logger.info(f"Sitekey found in iframe #{i+1} URL parameter 'sitekey': {site_key}")
                        return True, site_key

                    # Дополнительная проверка для других форматов URL через regex
                    if "sitekey" in src or "key" in src:
                        # Проверяем разные варианты параметров
                        for pattern in [r'[?&]sitekey=([^&]+)', r'[?&]key=([^&]+)', r'[?&]k=([^&]+)']:
                            match = re.search(pattern, src)
                            if match:
                                site_key = match.group(1)
                                logger.info(f"Sitekey found in iframe #{i+1} URL via regex '{pattern}': {site_key}")
                                return True, site_key

            logger.debug("Could not extract sitekey from any iframe src.")
        else:
            logger.debug("No reCAPTCHA iframe found.")

    except Exception as e:
        logger.debug(f"Error extracting sitekey from iframe: {e}")

    # Метод 2: Извлечение из data-sitekey атрибута div.g-recaptcha
    if not site_key:
        logger.debug("Attempting to extract sitekey from div.g-recaptcha...")
        try:
            # Расширяем селектор для поиска элементов с data-sitekey
            div_locator = page.locator("div.g-recaptcha[data-sitekey], .g-recaptcha[data-sitekey], [class*='recaptcha'][data-sitekey]")
            count = await div_locator.count()
            if count > 0:
                logger.debug(f"Found {count} element(s) with data-sitekey. Checking each one.")

                # Проверяем каждый элемент
                for i in range(count):
                    element = div_locator.nth(i)
                    extracted_key = await element.get_attribute("data-sitekey", timeout=5000)
                    if extracted_key:
                        logger.info(f"Sitekey found in element #{i+1} data-sitekey: {extracted_key}")
                        return True, extracted_key

                logger.debug("Elements with data-sitekey found, but all attributes are empty.")
            else:
                logger.debug("No elements with data-sitekey found.")

            # Дополнительно пробуем извлечь через JavaScript
            logger.debug("Attempting to extract sitekey via JavaScript...")
            js_sitekey = await page.evaluate("""
                () => {
                    // Ищем в data-sitekey
                    const elements = document.querySelectorAll('[data-sitekey]');
                    for (const el of elements) {
                        const key = el.getAttribute('data-sitekey');
                        if (key && key.length > 10) return key;
                    }

                    // Ищем в скриптах
                    const scripts = document.querySelectorAll('script');
                    for (const script of scripts) {
                        const content = script.textContent || '';
                        const siteKeyMatch = content.match(/['"]sitekey['"]\\s*:\\s*['"]([^'"]+)['"]/i);
                        if (siteKeyMatch) return siteKeyMatch[1];

                        // Другие форматы
                        const recaptchaMatch = content.match(/['"]recaptcha['"][^{]*?['"]key['"][^:]*?:\\s*['"]([^'"]+)['"]/i);
                        if (recaptchaMatch) return recaptchaMatch[1];
                    }

                    return null;
                }
            """)

            if js_sitekey:
                logger.info(f"Sitekey found via JavaScript: {js_sitekey}")
                return True, js_sitekey

        except Exception as e:
            logger.debug(f"Error extracting sitekey from elements with data-sitekey: {e}")

    # Метод 3: Извлечение из data-sitekey атрибута любого элемента (менее надежно)
    if not site_key:
        logger.debug("Attempting to extract sitekey from any element with data-sitekey...")
        try:
            element_locator = page.locator("[data-sitekey]")
            count = await element_locator.count()
            if count > 0:
                 logger.debug(f"Found {count} element(s) with data-sitekey. Checking the first one.")
                 potential_site_key = await element_locator.first.get_attribute("data-sitekey", timeout=5000)
                 # Проверяем, что ключ выглядит как стандартный reCAPTCHA ключ (длина ~40)
                 if potential_site_key and len(potential_site_key) > 30:
                     site_key = potential_site_key
                     logger.info(f"Sitekey found in generic element data-sitekey: {site_key}")
                     return True, site_key
                 else:
                     logger.debug(f"Element with data-sitekey found, but attribute value '{potential_site_key}' seems invalid.")
            else:
                 logger.debug("No elements with data-sitekey found.")
        except Exception as e:
             logger.debug(f"Error extracting sitekey from generic element: {e}")

    # Метод 4: Поиск в JavaScript переменных (сложно и ненадежно, пропускаем для простоты)

    logger.error("Failed to extract reCAPTCHA sitekey using all methods.")
    await save_debug_snapshot(page, "sitekey_not_found")
    return False, None


async def is_search_button_enabled(page: Page) -> bool:
    """Проверяет, активна ли кнопка поиска."""
    logger.debug("Checking if search button is enabled...")
    search_button = await find_search_button(page)
    if not search_button:
        logger.warning("Search button not found while checking enabled state.")
        return False # Не можем проверить, считаем что не активна
    try:
        # Используем is_disabled() для проверки
        is_disabled = await search_button.is_disabled(timeout=5000)
        logger.debug(f"Search button is_disabled: {is_disabled}")
        return not is_disabled
    except Exception as e:
        logger.warning(f"Error checking if search button is disabled: {e}")
        return False # Ошибка при проверке, считаем что не активна

async def is_recaptcha_checkbox_checked(page: Page) -> bool:
    """Проверяет, отмечен ли чекбокс reCAPTCHA."""
    try:
        # Переключаемся в iframe капчи
        iframe = page.locator("iframe[src*='api2/anchor']").first
        if not await iframe.count():
            logger.debug("Чекбокс reCAPTCHA не найден")
            return False

        # ИСПРАВЛЕНИЕ: используем frame_locator вместо content_frame()
        frame_locator = page.frame_locator("iframe[src*='api2/anchor']")
        if not frame_locator:
            logger.debug("Не удалось получить frame капчи")
            return False

        # Проверяем состояние чекбокса
        checkbox = frame_locator.locator("#recaptcha-anchor")
        if not await checkbox.count():
            logger.debug("Чекбокс не найден в frame")
            return False

        # Проверяем атрибут aria-checked
        is_checked = await checkbox.get_attribute("aria-checked")
        return is_checked == "true"

    except Exception as e:
        logger.error(f"Ошибка при проверке состояния чекбокса (is_recaptcha_checkbox_checked): {e}", exc_info=True)
        return False


async def has_recaptcha_challenge(page: Page) -> bool:
    """Проверяет, открыт ли iframe с заданием reCAPTCHA (bframe)."""
    logger.debug("Checking for reCAPTCHA challenge iframe (bframe)...")
    try:
        # Ищем iframe с заданием (bframe)
        challenge_iframe_locator = page.locator("iframe[src*='api2/bframe']")
        count = await challenge_iframe_locator.count()
        if count > 0:
            # Проверяем, видимы ли эти iframes
            for i in range(count):
                 iframe = challenge_iframe_locator.nth(i)
                 try:
                      if await iframe.is_visible(timeout=1000):
                           logger.debug(f"Visible reCAPTCHA challenge iframe found (index {i}).")
                           return True
                 except PlaywrightTimeout:
                      continue
                 except Exception as e_inner:
                     logger.debug(f"Error checking visibility of challenge iframe {i}: {e_inner}")
            logger.debug(f"Found {count} challenge iframe(s), but none are visible.")
            return False
        else:
            logger.debug("No reCAPTCHA challenge iframe (bframe) found.")
            return False
    except Exception as e:
        logger.warning(f"Error checking for reCAPTCHA challenge iframe: {e}")
        return False # Ошибка при проверке


async def get_recaptcha_token_value(page: Page) -> Optional[str]:
    """Получает значение токена из поля g-recaptcha-response."""
    logger.debug("Getting value from g-recaptcha-response field...")
    try:
        token_value = await page.evaluate("""
            () => {
                const response = document.querySelector('#g-recaptcha-response');
                // Также проверяем поля с классом, на всякий случай
                const responseByClass = document.querySelector('.g-recaptcha-response');
                const target = response || responseByClass;
                return target ? target.value : null;
            }
        """)
        if token_value:
            logger.debug(f"Found token in response field, length: {len(token_value)}, value: {token_value[:20]}...")
        else:
            logger.debug("g-recaptcha-response field not found or is empty.")
        return token_value
    except Exception as e:
        logger.warning(f"Error getting g-recaptcha-response value: {e}")
        return None

async def is_captcha_solved(page: Page) -> bool:
    """Проверяет, решена ли капча на странице."""
    logger.debug(f"is_captcha_solved: Started. Type of page: {type(page)}")
    try:
        # Part 1: page.evaluate
        logger.debug("is_captcha_solved: Attempting page.evaluate for token")
        token = await page.evaluate("""
            () => {
                const response = document.querySelector('#g-recaptcha-response');
                return response && response.value && response.value.length > 50;
            }
        """)
        logger.debug(f"is_captcha_solved: page.evaluate result for token: {token}")
        if token:
            logger.debug("is_captcha_solved: Token found, captcha is solved.")
            return True

        # Part 2: iframe interaction - ИСПРАВЛЕНО
        logger.debug("is_captcha_solved: Attempting page.locator for recaptcha_iframes")
        recaptcha_iframes = page.locator("iframe[src*='api2/anchor']")
        logger.debug(f"is_captcha_solved: Type of recaptcha_iframes: {type(recaptcha_iframes)}")
        
        count = await recaptcha_iframes.count()
        logger.debug(f"is_captcha_solved: recaptcha_iframes count: {count}")
        
        if count > 0:
            for i in range(count):
                logger.debug(f"is_captcha_solved: Processing iframe index {i}")
                iframe_locator_single = recaptcha_iframes.nth(i)
                logger.debug(f"is_captcha_solved: Type of iframe_locator_single (iframe {i}): {type(iframe_locator_single)}")
                
                # ИСПРАВЛЕНИЕ: используем frame_locator вместо content_frame()
                logger.debug(f"is_captcha_solved: Attempting frame_locator for iframe {i}")
                try:
                    frame_locator = page.frame_locator(f"iframe[src*='api2/anchor']:nth-of-type({i+1})")
                    logger.debug(f"is_captcha_solved: Type of frame_locator (iframe {i}): {type(frame_locator)}")
                    
                    if frame_locator:
                        logger.debug(f"is_captcha_solved: frame_locator for iframe {i} exists, attempting locator for '.recaptcha-checkbox'")
                        checkbox_locator = frame_locator.locator(".recaptcha-checkbox")
                        logger.debug(f"is_captcha_solved: Type of checkbox_locator (iframe {i}): {type(checkbox_locator)}")
                        
                        checkbox_count = await checkbox_locator.count()
                        logger.debug(f"is_captcha_solved: checkbox_locator count for iframe {i}: {checkbox_count}")
                        if checkbox_count > 0:
                            logger.debug(f"is_captcha_solved: Checkbox found in iframe {i}, attempting get_attribute 'aria-checked'")
                            is_checked = await checkbox_locator.get_attribute("aria-checked")
                            logger.debug(f"is_captcha_solved: Checkbox in iframe {i} aria-checked: {is_checked}")
                            if is_checked == "true":
                                logger.debug(f"is_captcha_solved: Checkbox in iframe {i} is checked, captcha is solved.")
                 return True
                except Exception as frame_error:
                    logger.debug(f"is_captcha_solved: Error with iframe {i}: {frame_error}")
                    continue

        logger.debug("is_captcha_solved: No token and no checked checkbox found, captcha is not solved.")
        return False
    except Exception as e:
        logger.error(f"Ошибка при проверке состояния капчи (is_captcha_solved): {e}", exc_info=True)
    return False


# precondition decorator already applied
@postcondition(lambda result, *args, **kwargs: isinstance(result, bool), "Результат должен быть булевым значением")
async def click_recaptcha_checkbox(page: Page) -> bool:
    """Кликает по чекбоксу reCAPTCHA."""
    try:
        # Находим iframe капчи
        iframe = page.locator("iframe[src*='api2/anchor']").first
        if not await iframe.count():
            logger.debug("Чекбокс reCAPTCHA не найден")
            return False

        # ИСПРАВЛЕНИЕ: используем frame_locator вместо content_frame()
        frame_locator = page.frame_locator("iframe[src*='api2/anchor']")
        if not frame_locator:
            logger.debug("Не удалось получить frame капчи")
             return False

        # Находим и кликаем по чекбоксу
        checkbox = frame_locator.locator("#recaptcha-anchor")
        if not await checkbox.count():
            logger.debug("Чекбокс не найден в frame")
                return False

        await checkbox.click()
        logger.info("Клик по чекбоксу reCAPTCHA выполнен")

        # Ждем немного после клика
        await page.wait_for_timeout(2000)
            return True

    except Exception as e:
        logger.error(f"Ошибка при клике по чекбоксу (click_recaptcha_checkbox): {e}", exc_info=True)
        return False

# precondition decorator already applied
@postcondition(lambda result, *args, **kwargs: isinstance(result, bool), "Результат должен быть булевым значением")
async def insert_captcha_token(page: Page, token: str) -> bool:
    """Вставляет токен решения капчи в поле и пытается вызвать callback."""
    logger.info(f"Inserting CAPTCHA token (length: {len(token)})...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"
    assert isinstance(token, str) and len(token) > 0, "token должен быть непустой строкой"

    await save_debug_snapshot(page, "before_token_insert")

    try:
        # Шаг 1: Вставляем токен в поле g-recaptcha-response внутри формы с кнопкой Search
        logger.debug("Inserting token into #g-recaptcha-response field via JS...")
        inserted = await page.evaluate("""
            (token) => {
                // Функция для создания или обновления поля g-recaptcha-response
                function ensureResponseField(token) {
                    // Сначала ищем форму, содержащую кнопку Search
                    const form = document.querySelector('form[action*=searchDirectory], form:has(button[type=submit]:has-text("Search"))');
                    if (!form) {
                        console.warn('Could not find search form, falling back to any form');
                    }

                    // Ищем существующее поле g-recaptcha-response
                    let response = document.querySelector('#g-recaptcha-response');
                    if (!response) {
                        // Если поле не найдено, создаем его
                        response = document.createElement('textarea');
                        response.id = 'g-recaptcha-response';
                        response.name = 'g-recaptcha-response';
                        response.className = 'g-recaptcha-response';
                        response.style.display = 'none';

                        // Добавляем в форму или в body, если форма не найдена
                        if (form) {
                            form.appendChild(response);
                            console.log('Created new #g-recaptcha-response field in search form');
                        } else {
                            // Ищем любую форму
                            const anyForm = document.querySelector('form');
                            if (anyForm) {
                                anyForm.appendChild(response);
                                console.log('Created new #g-recaptcha-response field in form');
                            } else {
                                document.body.appendChild(response);
                                console.log('Created new #g-recaptcha-response field in body');
                            }
                        }
                    } else {
                        console.log('Found existing #g-recaptcha-response field');
                    }

                    // Устанавливаем значение и генерируем событие
                    response.value = token;
                    response.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('Token inserted into #g-recaptcha-response, length:', token.length);
                    return true;
                }

                // Вставляем токен в поле
                return ensureResponseField(token);
            }
        """, token)

        if not inserted:
             logger.error("Failed to find or create #g-recaptcha-response field.")
             await save_debug_snapshot(page, "token_insert_field_not_found")
             return False

        # Проверяем, что токен действительно вставлен
        await page.wait_for_timeout(500) # Короткая пауза
        current_token = await get_recaptcha_token_value(page)
        if current_token != token:
            logger.error(f"Token verification failed after insertion! Expected length {len(token)}, found {len(current_token or '')}.")
            await save_debug_snapshot(page, "token_insert_verify_failed")
            return False
        logger.debug("Token successfully inserted and verified in the field.")

        # Шаг 2: Пытаемся вызвать callback-функцию reCAPTCHA и выполнить grecaptcha.execute()
        logger.debug("Attempting to execute reCAPTCHA callback via JS...")
        callback_executed = await page.evaluate("""
            (token) => {
                let callbackSuccess = false;
                let callbackResults = [];

                // 1. Проверяем тип reCAPTCHA (видимая или невидимая)
                const isInvisible = document.querySelector('.g-recaptcha[data-size="invisible"]') !== null;
                console.log('reCAPTCHA type:', isInvisible ? 'Invisible' : 'Visible checkbox');

                // 2. Ищем функцию callback, указанную в data-callback атрибуте
                const recaptchaDiv = document.querySelector('.g-recaptcha[data-callback]');
                if (recaptchaDiv && recaptchaDiv.dataset.callback) {
                    const callbackFuncName = recaptchaDiv.dataset.callback;
                    console.log('Found data-callback:', callbackFuncName);
                    if (typeof window[callbackFuncName] === 'function') {
                        try {
                            console.log('Executing window["' + callbackFuncName + '"](token)...');
                            window[callbackFuncName](token);
                            callbackSuccess = true;
                            callbackResults.push('data-callback executed');
                        } catch (e) {
                            console.error('Error executing data-callback function:', e);
                            callbackResults.push('data-callback error: ' + e.message);
                        }
                    } else {
                        console.warn('Callback function "' + callbackFuncName + '" not found or not a function.');
                        callbackResults.push('data-callback not found');
                    }
                } else {
                    callbackResults.push('no data-callback attribute found');
                }

                // 3. Пробуем выполнить grecaptcha.execute() для невидимой капчи
                if (window.grecaptcha) {
                    try {
                        // Для невидимой капчи нужно вызвать execute
                        if (isInvisible && typeof window.grecaptcha.execute === 'function') {
                            console.log('Executing grecaptcha.execute() for invisible reCAPTCHA...');
                            window.grecaptcha.execute();
                            callbackSuccess = true;
                            callbackResults.push('grecaptcha.execute() called');
                        }
                        // Для обычной капчи можно попробовать вызвать reset и затем программно установить ответ
                        else if (typeof window.grecaptcha.reset === 'function' && typeof window.grecaptcha.getResponse === 'function') {
                            // Проверяем, есть ли уже ответ
                            const currentResponse = window.grecaptcha.getResponse();
                            if (!currentResponse) {
                                console.log('No current response, trying to reset and set response...');
                                // Можно попробовать сбросить и программно установить ответ
                                // Но это может не сработать для всех реализаций
                            } else {
                                console.log('reCAPTCHA already has response of length:', currentResponse.length);
                                callbackResults.push('reCAPTCHA already has response');
                            }
                        }
                    } catch (e) {
                        console.error('Error with grecaptcha operations:', e);
                        callbackResults.push('grecaptcha error: ' + e.message);
                    }
                } else {
                    console.warn('grecaptcha object not found in window');
                    callbackResults.push('grecaptcha not found');
                }

                // 4. Ищем callback через внутренние структуры grecaptcha
                if (typeof window.___grecaptcha_cfg !== 'undefined' && window.___grecaptcha_cfg.clients) {
                    try {
                        const clients = window.___grecaptcha_cfg.clients;
                        for (const widgetId in clients) {
                            const client = clients[widgetId];
                            // Ищем разные варианты callback'ов
                            const potentialCallbacks = [];
                            for (const key in client) {
                                if (client[key] && typeof client[key].callback === 'function') potentialCallbacks.push(client[key].callback);
                                if (client[key] && client[key].challenge && typeof client[key].challenge.callback === 'function') potentialCallbacks.push(client[key].challenge.callback);
                                if (client[key] && typeof client[key].onSuccess === 'function') potentialCallbacks.push(client[key].onSuccess);
                            }
                            if (potentialCallbacks.length > 0) {
                                try {
                                    console.log('Executing internal reCAPTCHA callback for widget', widgetId);
                                    potentialCallbacks[0](token); // Вызываем первый найденный
                                    callbackSuccess = true;
                                    callbackResults.push('internal callback executed');
                                } catch (e) {
                                    console.error('Error executing internal callback:', e);
                                    callbackResults.push('internal callback error: ' + e.message);
                                }
                            }
                        }
                    } catch (e) {
                        console.error('Error accessing grecaptcha_cfg:', e);
                        callbackResults.push('grecaptcha_cfg error: ' + e.message);
                    }
                } else {
                    callbackResults.push('grecaptcha_cfg not found');
                }

                // 5. Активируем кнопку поиска напрямую
                try {
                    const searchButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                    for (const btn of searchButtons) {
                        if (btn.disabled) {
                            btn.disabled = false;
                            btn.removeAttribute('disabled');
                            console.log('Enabled search button:', btn.textContent || btn.value);
                            callbackResults.push('search button enabled');
                            callbackSuccess = true;
                        }
                    }
                } catch (e) {
                    console.error('Error enabling search button:', e);
                    callbackResults.push('enable button error: ' + e.message);
                }

                return { success: callbackSuccess, results: callbackResults };
            }
        """, token)

        if isinstance(callback_executed, dict):
            success = callback_executed.get('success', False)
            results = callback_executed.get('results', [])
            if success:
                logger.info(f"reCAPTCHA callback executed successfully. Results: {results}")
            else:
                logger.info(f"No reCAPTCHA callback was successfully executed. Attempts: {results}")
        else:
            # Обратная совместимость со старым форматом
            if callback_executed:
                logger.info("reCAPTCHA callback function or grecaptcha.execute() executed successfully.")
            else:
                logger.info("No reCAPTCHA callback function was found or executed (might be okay).")

        # Шаг 3: Принудительно активируем кнопку поиска (на случай, если она была заблокирована до callback'а)
        logger.debug("Attempting to enable the search button via JS...")
        await page.evaluate("""
            () => {
                const buttons = document.querySelectorAll('form button[type="submit"], form input[type="submit"], button[id*="search"], input[value*="Search"]');
                let enabled = false;
                buttons.forEach(btn => {
                    if (btn.disabled) {
                        btn.disabled = false;
                        btn.removeAttribute('disabled');
                        console.log('Enabled button:', btn.id || btn.name || btn.textContent.trim().substring(0,30));
                        enabled = true;
                    }
                });
                return enabled;
            }
        """)
        logger.debug("Attempted to enable search button.")

        await save_debug_snapshot(page, "after_token_insert")
        return True

    except Exception as e:
        logger.error(f"Error inserting captcha token or calling callback: {e}")
        await save_debug_snapshot(page, "token_insert_error")
        return False


# precondition decorator already applied
@postcondition(lambda result, *args, **kwargs: isinstance(result, bool), "Результат должен быть булевым значением")
async def wait_for_captcha_solution(page: Page, max_wait_seconds: int = 60) -> bool:
    """Ожидает подтверждения решения капчи после вставки токена."""
    logger.info(f"Waiting for captcha solution confirmation (max {max_wait_seconds}s)...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"
    assert isinstance(max_wait_seconds, int) and max_wait_seconds > 0, "max_wait_seconds должен быть положительным целым числом"

    start_time = time.time()
    check_interval_sec = 2 # Интервал проверки
    required_confirmations = 2 # Сколько раз подряд проверка должна пройти
    current_confirmations = 0

    while time.time() - start_time < max_wait_seconds:
        elapsed = time.time() - start_time
        logger.debug(f"Checking captcha status... (Elapsed: {elapsed:.1f}s)")

        try:
            solved = await is_captcha_solved(page)

            if solved:
                current_confirmations += 1
                logger.debug(f"Captcha appears solved (Confirmation {current_confirmations}/{required_confirmations})")
                if current_confirmations >= required_confirmations:
                    logger.info(f"Captcha solution confirmed after {elapsed:.1f}s.")
                    return True
            else:
                # Сбрасываем счетчик подтверждений, если проверка не прошла
                if current_confirmations > 0:
                     logger.debug("Captcha solution confirmation reset.")
                current_confirmations = 0
                logger.debug("Captcha does not appear solved yet.")

                # Проверяем наличие ошибок reCAPTCHA (например, истекший токен)
                error_message = await page.evaluate("""
                    () => {
                        // Ищем сообщение об ошибке в iframe
                        try {
                             const iframe = document.querySelector("iframe[src*='api2/anchor']");
                             if (iframe && iframe.contentDocument) {
                                 // Ищем видимое сообщение об ошибке
                                 const errorElement = iframe.contentDocument.querySelector('.rc-anchor-error-msg[style*="display: block"]');
                                 if (errorElement) return errorElement.innerText;
                             }
                        } catch (e) {}
                        // Ищем сообщение об истечении срока
                        const expiredElement = document.querySelector('.rc-anchor-alert'); // Селектор может отличаться
                        if (expiredElement && expiredElement.offsetParent !== null) { // Проверка видимости
                             return expiredElement.innerText || 'Captcha expired indication found';
                        }
                        return null; // Нет ошибки
                    }
                """)

                if error_message:
                    logger.error(f"Detected reCAPTCHA error message: {error_message}")
                    await save_debug_snapshot(page, "recaptcha_error_message")
                    # Если токен истек, нет смысла ждать дальше
                    if "expired" in error_message.lower() or "expirée" in error_message.lower():
                        logger.error("Captcha token likely expired. Stopping wait.")
                        return False
                    # Для других ошибок тоже прекращаем ожидание
                    return False

        except Exception as e:
            logger.warning(f"Error during captcha status check: {e}")
            # Продолжаем ожидание, возможно, временная ошибка

        # Ждем перед следующей проверкой
        await page.wait_for_timeout(check_interval_sec * 1000)

    # Если вышли из цикла по таймауту
    elapsed = time.time() - start_time
    logger.error(f"Timeout waiting for captcha solution confirmation after {elapsed:.1f}s.")
    await save_debug_snapshot(page, "captcha_wait_timeout")
    # Проверяем состояние в последний раз
    final_check = await is_captcha_solved(page)
    if final_check:
        logger.warning(f"Final captcha status check: SOLVED (but took too long to confirm)")
        return True
    else:
        logger.error(f"Final captcha status check: NOT SOLVED")
        # Выбрасываем исключение, чтобы остановить выполнение скрипта
        raise Exception("CAPTCHA solution could not be confirmed after multiple attempts")


# precondition decorator already applied
@postcondition(lambda result, *args, **kwargs: isinstance(result, bool), "Результат должен быть булевым значением")
async def solve_captcha(page: Page) -> bool:
    """Решает reCAPTCHA v2: проверяет статус, кликает (если надо), использует Anti‑Captcha, вставляет токен."""
    logger.info("===== STARTING CAPTCHA SOLVING PROCESS =====")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    await save_debug_snapshot(page, "before_captcha_solve")

    # 0. Проверяем, активна ли кнопка поиска (если активна, капча может быть не нужна)
    # Используем более надежный селектор для кнопки поиска
    search_button_locator = page.locator("button[type='submit']:has-text('Search'), button:has-text('Search'), input[type='submit'][value='Search']").first
    if await search_button_locator.count() > 0:
        is_disabled = await search_button_locator.evaluate("button => button.disabled || button.getAttribute('disabled') !== null")
        if not is_disabled:
            logger.info("Search button is already enabled. CAPTCHA might not be required.")
            logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Button Already Enabled) =====")
            return True
        else:
            logger.info("Search button is disabled. CAPTCHA solving is required.")

    # 1. Проверяем, есть ли вообще капча на странице
    has_captcha_elements = await page.evaluate("""
        () => (document.querySelector('iframe[src*="api2/anchor"]') ||
               document.querySelector('iframe[src*="enterprise"]') ||
               document.querySelector('div.g-recaptcha')) !== null
    """)
    if not has_captcha_elements:
        logger.info("No reCAPTCHA elements detected. Assuming captcha not required.")
        logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Not Required) =====")
        return True

    # 2. Проверяем, не решена ли капча уже
    logger.debug("Checking initial captcha status...")
    if await is_captcha_solved(page):
        logger.info("Captcha is already solved. Skipping.")
        logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Already Solved) =====")
        return True
    else:
        logger.info("Initial check: Captcha is not solved.")

    # Проверка наличия ANTICAPTCHA_KEY сразу после проверки статуса капчи
    if not ANTICAPTCHA_KEY and not (args and args.no_captcha):
        logger.error("ANTICAPTCHA_API_KEY is not set, and --no-captcha is not used. Cannot solve automatically.")
        logger.info("===== CAPTCHA SOLVING PROCESS FAILED (No API Key) =====")
        # Предлагаем решить вручную как запасной вариант
        input(">> ANTICAPTCHA_API_KEY not found. Please solve manually and press Enter...")
        if await is_captcha_solved(page):
            logger.info("Manual CAPTCHA solution confirmed.")
            logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Manual Fallback) =====")
            return True
        else:
            logger.error("CAPTCHA still not solved after manual fallback prompt.")
            await save_debug_snapshot(page, "manual_fallback_failed")
            logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Manual Fallback) =====")
            return False

    # 3. Обработка без Anti-Captcha (ручное решение или --no-captcha)
    if args and args.no_captcha:
        logger.warning("Automatic captcha solving disabled (--no-captcha).")
        logger.info("Waiting for manual CAPTCHA solution...")

        # Ждем, пока пользователь решит капчу (до 2 минут)
        max_wait_time = 120  # 2 минуты
        check_interval = 1   # 1 секунда
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            # Проверяем, решена ли капча
            if await is_captcha_solved(page):
                elapsed = time.time() - start_time
                logger.info(f"Manual CAPTCHA solution confirmed after {elapsed:.1f} seconds.")
                logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Manual Solve) =====")
                return True

            # Проверяем, активна ли кнопка поиска
            search_button_enabled = await page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"], button.btn-search');
                    for (const btn of buttons) {
                        if (!btn.disabled) return true;
                    }
                    return false;
                }
            """)

            if search_button_enabled:
                logger.info("Search button is enabled. Manual CAPTCHA solution confirmed.")
                logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Manual Solve) =====")
                return True

            # Ждем перед следующей проверкой
            await page.wait_for_timeout(check_interval * 1000)

            # Каждые 10 секунд выводим сообщение
            elapsed = time.time() - start_time
            if int(elapsed) % 10 == 0:
                logger.info(f"Still waiting for manual CAPTCHA solution... ({int(elapsed)}s elapsed)")

        # Если вышли из цикла по таймауту
        logger.error(f"Timeout waiting for manual CAPTCHA solution after {max_wait_time} seconds.")

        # Последняя проверка перед выходом
        if await is_captcha_solved(page):
            logger.info("Manual CAPTCHA solution confirmed at the last moment.")
            logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Manual Solve) =====")
            return True

        await save_debug_snapshot(page, "manual_captcha_timeout")
        logger.error("CAPTCHA not solved manually within the timeout period. Stopping script.")
        logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Manual Solve Timeout) =====")
        # Выбрасываем исключение, чтобы остановить выполнение скрипта
        raise Exception("Manual CAPTCHA solving failed: timeout waiting for solution")

    # 4. Автоматическое решение с Anti-Captcha
    logger.info("Starting automatic CAPTCHA solving with Anti-Captcha...")

    # Проверяем наличие ключа Anti-Captcha еще раз
    if not ANTICAPTCHA_KEY:
        logger.error("ANTICAPTCHA_API_KEY is not set. Cannot proceed with Anti-Captcha.")
        logger.info("===== CAPTCHA SOLVING PROCESS FAILED (No API Key) =====")
        return False

    # 4.1 Извлекаем sitekey
    logger.debug("Extracting reCAPTCHA sitekey...")
    success, site_key = await extract_recaptcha_sitekey(page)
    if not success or not site_key:
        logger.error("Failed to extract reCAPTCHA sitekey. Cannot proceed with Anti-Captcha.")
        logger.info("===== CAPTCHA SOLVING PROCESS FAILED (No Sitekey) =====")
        return False

    logger.info(f"Successfully extracted reCAPTCHA sitekey: {site_key}")

    # 4.2 Определяем тип reCAPTCHA и кликаем чекбокс при необходимости
    is_invisible = await page.evaluate("""
        () => {
            // Проверяем наличие атрибута data-size="invisible"
            const invisibleRecaptcha = document.querySelector('.g-recaptcha[data-size="invisible"]');
            if (invisibleRecaptcha) return true;

            // Проверяем наличие ключевых слов в скриптах
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                const content = script.textContent || '';
                if (content.includes('invisible') && content.includes('recaptcha')) return true;
            }

            return false;
        }
    """)

    if is_invisible:
        logger.info("Invisible reCAPTCHA detected. Skipping checkbox click.")
        # Для невидимой капчи не нужно кликать на чекбокс, но можно попробовать вызвать execute
        try:
            await page.evaluate("""
                () => {
                    if (window.grecaptcha && typeof window.grecaptcha.execute === 'function') {
                        console.log('Attempting to execute invisible reCAPTCHA...');
                        try {
                            window.grecaptcha.execute();
                            return true;
                        } catch (e) {
                            console.error('Error executing invisible reCAPTCHA:', e);
                            return false;
                        }
                    }
                    return false;
                }
            """)
            logger.debug("Attempted to execute invisible reCAPTCHA via JavaScript.")
            # Ждем немного, чтобы увидеть, появится ли челлендж
            await page.wait_for_timeout(2000)
        except Exception as e:
            logger.debug(f"Error attempting to execute invisible reCAPTCHA: {e}")
    else:
        logger.info("Visible reCAPTCHA detected. Checking checkbox...")

        # Проверяем, не отмечен ли уже чекбокс
        if await is_recaptcha_checkbox_checked(page):
            logger.info("reCAPTCHA checkbox is already checked. Skipping click.")
        else:
            # Находим iframe с чекбоксом
            anchor_iframe_locator = page.locator("iframe[src*='api2/anchor'], iframe[src*='recaptcha'][src*='anchor'], iframe[src*='enterprise']")
            if await anchor_iframe_locator.count() > 0:
                iframe_element = anchor_iframe_locator.first
                recaptcha_frame = iframe_element.frame_locator(':scope')
                checkbox_locator = recaptcha_frame.locator("#recaptcha-anchor, .recaptcha-checkbox")

                # Прокручиваем к iframe и делаем паузу для стабилизации страницы
                logger.debug("Scrolling to reCAPTCHA iframe...")
                await iframe_element.scroll_into_view_if_needed(timeout=10000)
                await page.wait_for_timeout(1000)  # Увеличенная пауза для стабилизации

                # Ждем, пока сам чекбокс станет видимым внутри фрейма
                logger.debug("Waiting for checkbox element to be visible...")
                try:
                    await checkbox_locator.wait_for(state="visible", timeout=10000)

                    # Пробуем кликнуть с использованием улучшенного метода
                    await click_recaptcha_checkbox(page)

                    # Ждем появления челленджа или подтверждения решения
                    await page.wait_for_timeout(3000)
                    if await has_recaptcha_challenge(page):
                        logger.info("Challenge appeared after checkbox click.")
                    elif await is_captcha_solved(page):
                        logger.info("Captcha solved automatically after checkbox click!")
                        logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Auto Solved Post-Click) =====")
                        return True
                except Exception as e:
                    logger.error(f"Error with checkbox interaction: {e}")
                    await save_debug_snapshot(page, "recaptcha_checkbox_interaction_error")
            else:
                logger.warning("reCAPTCHA anchor iframe not found. Cannot click checkbox.")

    # 4.3 Отправляем задачу в Anti-Captcha и обрабатываем результат
    try:
        # Создаем задачу
        logger.info(f"Creating Anti-Captcha task for sitekey: {site_key}")
        success, task_id = await create_captcha_task(site_key, page.url, page)
        if not success or task_id is None:
            # Ошибка уже залогирована в create_captcha_task
            logger.error("Failed to create Anti-Captcha task")
            logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Task Creation) =====")
            return False

        logger.info(f"Anti-Captcha task created successfully, Task ID: {task_id}")

        # Получаем результат
        logger.info(f"Waiting for Anti-Captcha result for task ID: {task_id}")
        success, token = await get_captcha_result(task_id)
        if not success or token is None:
            # Ошибка уже залогирована в get_captcha_result
            logger.error("Failed to get Anti-Captcha result")
            logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Get Result) =====")
            return False

        logger.info(f"Anti-Captcha solution received successfully, token length: {len(token)}")

        # Проверяем, что токен имеет правильную длину
        if len(token) < 50:
            logger.warning(f"Received suspiciously short token from Anti-Captcha: {len(token)} chars")
            await save_debug_snapshot(page, "short_token_received")
            # Продолжаем, но с предупреждением

        # Вставляем токен
        logger.info("Inserting token into page...")
        if not await insert_captcha_token(page, token):
            logger.error("Failed to insert CAPTCHA token into page.")
            logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Token Insert) =====")
            await save_debug_snapshot(page, "token_insert_failed")
            return False

        logger.info("Token inserted successfully")

        # Ждем, пока кнопка Search перестанет быть disabled
        try:
            logger.debug("Waiting for Search button to become enabled...")
            await page.wait_for_function("""
                () => {
                    const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                    for (const btn of buttons) {
                        if (!btn.disabled) return true;
                    }
                    return false;
                }
            """, timeout=10000)
            logger.info("Search button is now enabled after token insertion.")
        except Exception as e:
            logger.warning(f"Timeout waiting for Search button to become enabled: {e}")
            await save_debug_snapshot(page, "button_not_enabled_after_token")
            # Продолжаем, даже если кнопка не стала активной - возможно, она активируется позже

        # Ждем подтверждения решения
        try:
            logger.info("Waiting for captcha solution confirmation...")
            await wait_for_captcha_solution(page)
            # Если функция не выбросила исключение, значит капча решена успешно
            logger.info("Captcha solution confirmed!")
        except Exception as e:
            logger.error(f"Captcha solution could not be confirmed after inserting token: {e}")
            logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Confirmation Timeout) =====")
            await save_debug_snapshot(page, "captcha_confirmation_failed")
            # Пробрасываем исключение дальше, чтобы остановить выполнение скрипта
            raise Exception(f"CAPTCHA solution confirmation failed: {e}")

        logger.info("CAPTCHA solved and confirmed successfully!")
        logger.info("===== CAPTCHA SOLVING PROCESS FINISHED (Success) =====")
        await save_debug_snapshot(page, "captcha_solved_confirmed")
        return True
    except Exception as e:
        # Обработка критических ошибок Anti-Captcha, которые пробрасываются
        if "AntiCaptcha Error" in str(e):
            logger.critical(f"Stopping due to critical Anti-Captcha error: {e}")
            # Здесь можно решить, останавливать ли весь скрипт или только эту категорию
            # raise e # Раскомментировать, чтобы остановить весь скрипт
            logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Critical API Error) =====")
            await save_debug_snapshot(page, "captcha_critical_error")
            return False # Останавливаем только текущую попытку
        else:
            logger.error(f"Unexpected error during Anti-Captcha process: {e}", exc_info=True)
            await save_debug_snapshot(page, "captcha_unexpected_error")
            logger.info("===== CAPTCHA SOLVING PROCESS FAILED (Unexpected Error) =====")
            return False


# ---------------------------------------------------------------------------
# Core parsing routines
# ---------------------------------------------------------------------------

# precondition decorator already applied
@postcondition(
    lambda result, *args, **kwargs: (isinstance(result, tuple) and len(result) == 3 and
                                     isinstance(result[0], bool) and
                                     (result[1] is None or isinstance(result[1], Locator)) and
                                     isinstance(result[2], int) and result[2] >= 0),
    "Результат должен быть кортежем (bool, Optional[Locator], int >= 0)"
)
async def collect_result_cards(page: Page) -> Tuple[bool, Optional[Locator], int]:
    """Ищет контейнер с карточками результатов и сами карточки."""
    logger.debug("Looking for result cards container and cards...")
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    # Селекторы для контейнера результатов (где могут быть карточки И пагинация)
    container_selectors = [
        "div.search-results-list",
        "div#search-results",
        "ul#search-results",  # Добавлен селектор для CPA Quebec
        "div.results-container",
        "section.results",
        "ul.results-list",
        "div.listing",
        "div.cpa-directory-results",  # Добавлен селектор для CPA Quebec
        "div.directory-results",
        "div.search-results",
        "div#directory-results",
        "div#searchResults",
        "main", # Как общий контейнер
        "body" # В крайнем случае
    ]

    # Селекторы для самих карточек результатов внутри контейнера
    card_selectors_relative = [
        ".search-result-item",
        ".searchResult-item",  # Добавлен селектор для CPA Quebec
        ".result-card",
        ".profile-card",  # Добавлен селектор для CPA Quebec
        "article.cpa-entry",
        "div.profile-summary",
        "li.item",
        "li.searchResult-item",  # Добавлен селектор для CPA Quebec
        "div.row.result", # Bootstrap-like
        "div[class*='result-item']",
        "div[class*='search-result']",
        "div[class*='card']",
        "div[class*='profile']",
        "div[class*='entry']",
        "tr[data-id]", # Результаты в виде таблицы
        "tr.result-row",
        "tbody > tr", # Строки таблицы
        "div.col-md-4", # Bootstrap колонки (часто используются для карточек)
        "div.col-sm-6",
        "div.col-lg-3"
    ]
    # Абсолютные селекторы, если не нашли в контейнере
    card_selectors_absolute = [
        "ul#search-results > li",  # Добавлен селектор для CPA Quebec
        "ul#search-results > li.searchResult-item",  # Добавлен селектор для CPA Quebec
        ".search-result", ".result-item", ".result", ".cpa-result",
        ".searchResult-item",  # Добавлен селектор для CPA Quebec
        ".profile-card",  # Добавлен селектор для CPA Quebec
        ".directory-result", "article", ".card", "div.listing-item",
        "div.directory-item", "div.member", "div.profile", "li.result",
        "div[class*='result']", "div[class*='card']", "div[class*='profile']",
        "div[class*='entry']", "div[class*='item']", "div[class*='listing']",
        "div[class*='directory']", "div[class*='member']", "div[class*='cpa']",
        "table tr", "tbody tr", "ul li.item", "div.row > div" # Более общие селекторы
    ]

    results_locator: Optional[Locator] = None
    card_count: int = 0

    # Сначала ищем общий контейнер
    container_locator = None
    for selector in container_selectors:
        logger.debug(f"Trying container selector: {selector}")
        locator = page.locator(selector).first # Обычно контейнер один
        try:
            if await locator.count() > 0 and await locator.is_visible(timeout=2000):
                 logger.debug(f"Found potential results container: {selector}")
                 container_locator = locator
                 break
        except Exception as e:
             logger.debug(f"Error checking container selector {selector}: {e}")

    if container_locator:
         logger.info(f"Using container: {container_selectors[container_selectors.index(selector)]}")
         # Ищем карточки внутри контейнера
         for card_selector in card_selectors_relative:
              logger.debug(f"Trying relative card selector: {card_selector}")
              current_cards = container_locator.locator(card_selector)
              count = await current_cards.count()
              if count > 0:
                   logger.info(f"Found {count} cards with relative selector: {card_selector}")
                   results_locator = current_cards
                   card_count = count
                   break
              else:
                   logger.debug(f"No cards found with relative selector: {card_selector}")
    else:
         logger.warning("Results container not found. Searching for cards globally.")

    # Если не нашли карточки в контейнере или не нашли сам контейнер, ищем глобально
    if not results_locator:
         logger.debug("Searching for cards using absolute selectors...")
         for card_selector in card_selectors_absolute:
              logger.debug(f"Trying absolute card selector: {card_selector}")
              current_cards = page.locator(card_selector)
              count = await current_cards.count()
              # Добавим проверку, чтобы не выбрать слишком много элементов (например, все div)
              if count > 0 and count < 200: # Условный лимит
                   # Проверим текст первого элемента, чтобы убедиться, что это не что-то левое
                   try:
                       first_card_text = await current_cards.first.inner_text(timeout=1000)
                       if len(first_card_text.strip()) > 5: # Проверка на непустой текст
                           logger.info(f"Found {count} cards with absolute selector: {card_selector}")
                           results_locator = current_cards
                           card_count = count
                           break
                       else:
                            logger.debug(f"Selector {card_selector} matched {count} elements, but first is empty.")
                   except Exception as e_text:
                        logger.debug(f"Error checking text for selector {card_selector}: {e_text}")
              elif count >= 200:
                   logger.debug(f"Selector {card_selector} matched too many elements ({count}). Skipping.")
              else:
                   logger.debug(f"No cards found with absolute selector: {card_selector}")


    if results_locator and card_count > 0:
         logger.info(f"Successfully identified {card_count} result cards.")
         return True, results_locator, card_count
    else:
         # Проверяем наличие сообщения "нет результатов"
         logger.warning("No result cards found.")
         await save_debug_snapshot(page, "no_result_cards_found")
         no_results_text = await page.evaluate("""
            () => {
                const bodyText = document.body.innerText.toLowerCase();
                const indicators = ['no results found', 'aucun résultat', '0 results', 'zero results'];
                return indicators.some(ind => bodyText.includes(ind));
            }
         """)
         if no_results_text:
              logger.info("Detected 'no results' message on the page.")
              return False, None, 0 # Явно нет результатов
         else:
              logger.error("Failed to find result cards and no 'no results' message detected.")
              return False, None, 0 # Не нашли результаты


async def extract_card_data(card_locator: Locator, index: int, category: str) -> Optional[Dict[str, Any]]:
    """Извлекает данные (имя, URL) из одной карточки результата."""
    logger.debug(f"Processing card #{index + 1}...")
    try:
        # Плавно прокручиваем карточку в видимую область, если нужно
        await card_locator.scroll_into_view_if_needed(timeout=5000)
        # Делаем небольшую паузу, чтобы убедиться, что контент загрузился (если есть lazy loading)
        await card_locator.page.wait_for_timeout(100)

        name = f"Name Not Found - Card {index + 1}"
        profile_url = None

        # Извлечение имени (пробуем разные селекторы)
        name_selectors = [
            "h3", "h2", "h4", "h1",
            ".name", ".title", ".cpa-name", ".profile-name", ".card-title",
            "strong", "b",
            "a[href*='/profil']", "a[href*='/profile']", "a[href*='/directory']",
            "div[class*='name']", "div[class*='title']", "span[class*='name']",
            "td:first-child", # Первая ячейка в строке таблицы
            "a" # Любая ссылка как последний вариант
        ]
        for sel in name_selectors:
            name_element = card_locator.locator(sel).first
            try:
                if await name_element.is_visible(timeout=500):
                    extracted_name = await name_element.inner_text(timeout=1000)
                    if extracted_name.strip():
                         name = extracted_name.strip()
                         logger.debug(f"  Name found with '{sel}': {name}")
                         break # Нашли имя, выходим
            except (PlaywrightTimeout, PlaywrightError):
                continue # Пробуем следующий селектор

        # Извлечение URL профиля (пробуем разные селекторы)
        url_selectors = [
            "a:has-text('Profile')", "a:has-text('Profil')", "a:has-text('View')", "a:has-text('Details')",
            "a:has-text('More')", "a:has-text('Info')", "a:has-text('See more')",
            "a.profile-link", "a.details-link", "a.more-link", "a.view-link", "a.info-link",
            "a[href*='/profil']", "a[href*='/profile']", "a[href*='/detail']", "a[href*='/view']",
            "a[href*='/info']", "a[href*='/member']", "a[href*='/directory']", "a[href*='/cpa']",
            "h3 > a", "h2 > a", "h4 > a", "h1 > a", # Ссылка на заголовке
            ".name a", ".title a", ".card-title a", # Ссылка в элементе с именем
            "td:first-child a", # Ссылка в первой ячейке таблицы
            "a.btn", "a.button", # Кнопки-ссылки
            "a" # Последний шанс - любая ссылка в карточке
        ]
        for sel in url_selectors:
            link_element = card_locator.locator(sel).first
            try:
                if await link_element.is_visible(timeout=500):
                    href = await link_element.get_attribute("href", timeout=1000)
                    if href and (href.startswith("http") or href.startswith("/")):
                         # Преобразуем относительный URL в абсолютный
                         profile_url = card_locator.page.urljoin(href)
                         logger.debug(f"  Profile URL found with '{sel}': {profile_url}")
                         break # Нашли URL, выходим
            except (PlaywrightTimeout, PlaywrightError):
                continue

        # Если URL не найден, логируем предупреждение
        if not profile_url:
            logger.warning(f"  Could not find profile URL for card #{index + 1} ({name}).")
            card_html = await card_locator.inner_html(timeout=1000)
            logger.debug(f"  Card HTML: {card_html[:200]}...")


        # Формируем результат для карточки
        item_data = {
            "name": name,
            "category": category,
            "profile_url": profile_url,
             # Можно добавить извлечение краткой информации прямо с карточки, если она есть
            # "location": await card_locator.locator(".location").first.inner_text(timeout=500) catch ""
        }
        logger.debug(f"  Card #{index + 1} processed: Name='{name}', URL='{profile_url}'")
        return item_data

    except Exception as e:
        logger.error(f"Error processing card #{index + 1}: {e}", exc_info=True)
        try:
            # Попытка сохранить HTML сбойной карточки
            await save_debug_snapshot(card_locator.page, f"card_processing_error_{index+1}")
            card_html = await card_locator.inner_html(timeout=2000)
            error_file = await manage_debug_files(f"error_card_{index+1}.html")
            if error_file:
                 with open(error_file, "w", encoding="utf-8") as f:
                      f.write(card_html)
                 logger.debug(f"HTML of problematic card saved to {Path(error_file).name}")
        except Exception as e_save:
             logger.warning(f"Could not save HTML of problematic card: {e_save}")
        return None # Возвращаем None в случае ошибки обработки карточки

# precondition decorator applied via asserts inside
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, list),
    "Результат должен быть списком словарей"
)
async def process_category(page: Page, category: str, get_details: bool) -> List[Dict[str, Any]]:
    """Обрабатывает одну категорию: выбирает чекбокс, решает капчу, выполняет поиск, собирает результаты."""
    # Явные проверки предусловий в начале функции
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"
    assert isinstance(category, str), "category должна быть строкой" # Допускаем пустую строку для "All"
    assert isinstance(get_details, bool), "get_details должен быть булевым значением"

    category_display_name = category if category else "All" # Для логирования
    logger.info(f"===== STARTING CATEGORY PROCESSING: {category_display_name} =====")
    await save_debug_snapshot(page, f"start_category_{category_display_name}")

    # 1. Навигация и подготовка страницы
    try:
        current_url = page.url
        if not current_url.startswith(BASE_URL):
            logger.info(f"Navigating to base URL: {BASE_URL}")
            await page.goto(BASE_URL, timeout=90000, wait_until="domcontentloaded")
            logger.info("Base URL page loaded.")
        else:
            logger.info("Already on the correct base URL.")
            # Если мы уже на странице, возможно, нужно сбросить фильтры перед выбором нового
            logger.debug("Attempting to reset form/filters...")
            try:
                 # Ищем кнопку сброса
                 reset_button = page.locator("button[type='reset'], input[type='reset'], button:has-text('Reset'), button:has-text('Clear')").first
                 if await reset_button.is_visible(timeout=2000):
                      logger.info("Clicking reset button...")
                      await reset_button.click(timeout=5000)
                      await page.wait_for_timeout(1500) # Пауза после сброса
                 else:
                      logger.debug("Reset button not found, reloading page as fallback...")
                      await page.reload(timeout=60000, wait_until="domcontentloaded")
            except Exception as e_reset:
                 logger.warning(f"Could not reset form, reloading page: {e_reset}")
                 await page.reload(timeout=60000, wait_until="domcontentloaded")

        await close_cookies_banner(page)

    except Exception as e:
        logger.critical(f"Failed to navigate to or prepare the page: {e}", exc_info=True)
        await save_debug_snapshot(page, f"error_navigating_{category_display_name}")
        logger.info(f"===== CATEGORY PROCESSING FAILED (Navigation): {category_display_name} =====")
        return []

    # 2. Выбор категории (если не "All")
    if category:
        logger.info(f"Selecting category checkbox: '{category}'")
        try:
             # Используем get_by_label как основной метод
             checkbox_locator = page.get_by_label(category, exact=True)
             logger.debug("Locating checkbox by label...")
             await checkbox_locator.wait_for(state="attached", timeout=15000)

             # Прокручиваем к чекбоксу и делаем паузу для стабилизации страницы
             await checkbox_locator.scroll_into_view_if_needed(timeout=10000)
             await page.wait_for_timeout(1000)  # Увеличенная пауза для стабилизации

             # Проверяем видимость
             if not await checkbox_locator.is_visible(timeout=5000):
                  raise PlaywrightError(f"Checkbox for '{category}' found but not visible.")

             # Отмечаем чекбокс, если он еще не отмечен
             if not await checkbox_locator.is_checked(timeout=5000):
                  logger.debug("Checkbox is not checked. Checking it...")
                  # Делаем снимок перед кликом
                  await save_debug_snapshot(page, f"before_category_check_{category_display_name}")

                  # Используем force=True на случай перекрытия
                  await checkbox_locator.check(force=True, timeout=10000)
                  # Добавляем паузу после клика
                  await page.wait_for_timeout(300)

                  # Увеличенная пауза для обновления состояния
                  await page.wait_for_timeout(1500)

                  # Проверяем результат
                  if not await checkbox_locator.is_checked(timeout=5000):
                       raise PlaywrightError(f"Failed to check the checkbox for '{category}' after clicking.")
                  logger.info(f"Checkbox for '{category}' checked successfully.")
             else:
                  logger.info(f"Checkbox for '{category}' was already checked.")

             await save_debug_snapshot(page, f"after_category_{category_display_name}")

        except Exception as e:
             logger.error(f"Failed to select category checkbox '{category}': {e}", exc_info=True)
             await save_debug_snapshot(page, f"error_category_select_{category_display_name}")
             # Можно решить, продолжать ли без выбора категории или остановить
             logger.warning("Proceeding without category selection due to error.")
             # return [] # Раскомментировать, если выбор категории критичен
    else:
         logger.info("Processing 'All' categories, no specific checkbox selected.")

    # Делаем паузу после выбора категории перед решением капчи
    await page.wait_for_timeout(1500)


    # 3. Проверяем, нужно ли решать капчу
    logger.info("Checking if CAPTCHA needs to be solved...")

    # Проверяем, активна ли кнопка поиска (если активна, капча может быть не нужна)
    # Используем более надежный селектор для поиска кнопки на сайте CPA Quebec
    search_button_locator = page.locator("button[type='submit']:has-text('Search'), button[type='submit'], input[type='submit'][value='Search']").first
    if await search_button_locator.count() > 0:
        # Более надежная проверка состояния кнопки
        is_disabled = await search_button_locator.evaluate("""
            button => button.disabled ||
                     button.getAttribute('disabled') !== null ||
                     button.classList.contains('disabled') ||
                     button.closest('.disabled') !== null
        """)
        if not is_disabled:
            logger.info("Search button is already enabled. CAPTCHA might not be required.")
        else:
            logger.info("Search button is disabled. CAPTCHA solving is required.")
            # Решаем капчу с повторными попытками
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"Attempt {attempt + 1} of {max_attempts} to solve CAPTCHA...")
                    captcha_solved = await solve_captcha(page)
                    if captcha_solved:
                        logger.info("CAPTCHA solved successfully.")
                        # Дополнительная проверка состояния кнопки после решения
                        await page.wait_for_timeout(2000)  # Даем время на обновление состояния
                        is_disabled = await search_button_locator.evaluate(""
                            button => button.disabled ||
                                 button.getAttribute('disabled') !== null ||
                                 button.classList.contains('disabled') ||
                                 button.closest('.disabled') !== null
                        """)
                        if not is_disabled:
                            logger.info("Search button is now enabled after CAPTCHA solution.")
                            break
                        else:
                            logger.warning("Search button is still disabled after CAPTCHA solution. Retrying...")
                            continue
                    else:
                        logger.warning("CAPTCHA solving returned False. Retrying...")
                        continue
                except Exception as e:
                    logger.error(f"Attempt {attempt + 1} failed: {e}")
                    if attempt == max_attempts - 1:
                        # Обработка критических ошибок Anti-Captcha, которые пробрасываются из solve_captcha
                        if "AntiCaptcha Error" in str(e):
                            logger.critical(f"Stopping category processing due to critical Anti-Captcha error: {e}")
                            logger.info(f"===== CATEGORY PROCESSING FAILED (Critical API Error): {category_display_name} =====")
                            raise e # Пробрасываем дальше, чтобы остановить весь скрипт
                        elif "CAPTCHA solution confirmation failed" in str(e) or "Manual CAPTCHA solving failed" in str(e):
                            logger.critical(f"CAPTCHA was not solved: {e}")
                            logger.info(f"===== CATEGORY PROCESSING FAILED (CAPTCHA Not Solved): {category_display_name} =====")
                            raise e # Пробрасываем дальше, чтобы остановить весь скрипт
                        else:
                            logger.error(f"Unexpected error during CAPTCHA process: {e}", exc_info=True)
                            logger.info(f"===== CATEGORY PROCESSING FAILED (CAPTCHA Error): {category_display_name} =====")
                            raise Exception(f"CAPTCHA solving failed with unexpected error: {e}") # Останавливаем скрипт
                    else:
                        logger.info(f"Retrying CAPTCHA solution (attempt {attempt + 2} of {max_attempts})...")
                        await page.wait_for_timeout(3000)  # Пауза перед следующей попыткой
    else:
        logger.warning("Search button not found. Attempting to solve CAPTCHA anyway.")
        # Решаем капчу с повторными попытками
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                logger.info(f"Attempt {attempt + 1} of {max_attempts} to solve CAPTCHA...")
                captcha_solved = await solve_captcha(page)
                if captcha_solved:
                    logger.info("CAPTCHA solved successfully.")
                    break
                else:
                    logger.warning("CAPTCHA solving returned False. Retrying...")
                    continue
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_attempts - 1:
                    # Обработка критических ошибок Anti-Captcha, которые пробрасываются из solve_captcha
                    if "AntiCaptcha Error" in str(e):
                        logger.critical(f"Stopping category processing due to critical Anti-Captcha error: {e}")
                        logger.info(f"===== CATEGORY PROCESSING FAILED (Critical API Error): {category_display_name} =====")
                        raise e # Пробрасываем дальше, чтобы остановить весь скрипт
                    elif "CAPTCHA solution confirmation failed" in str(e) or "Manual CAPTCHA solving failed" in str(e):
                        logger.critical(f"CAPTCHA was not solved: {e}")
                        logger.info(f"===== CATEGORY PROCESSING FAILED (CAPTCHA Not Solved): {category_display_name} =====")
                        raise e # Пробрасываем дальше, чтобы остановить весь скрипт
                    else:
                        logger.error(f"Unexpected error during CAPTCHA process: {e}", exc_info=True)
                        logger.info(f"===== CATEGORY PROCESSING FAILED (CAPTCHA Error): {category_display_name} =====")
                        raise Exception(f"CAPTCHA solving failed with unexpected error: {e}") # Останавливаем скрипт
                else:
                    logger.info(f"Retrying CAPTCHA solution (attempt {attempt + 2} of {max_attempts})...")
                    await page.wait_for_timeout(3000)  # Пауза перед следующей попыткой

    # 4. Клик на кнопку поиска
    logger.info("Clicking the search button...")
    try:
        # Используем более специфичный селектор для кнопки Search в форме поиска
        # Сначала пробуем найти кнопку в основной форме поиска
        search_button = page.locator("form[action*='searchDirectory'] button[type='submit'], form[action*='directory'] button[type='submit']").first

        if await search_button.count() > 0:
            logger.info("Using form-specific selector for Search button")
            await search_button.click(force=True, timeout=10000)
            logger.info("Search button clicked successfully using form-specific selector")
        else:
            # Если не нашли по специфичному селектору, пробуем найти по роли, но с уточнением
            logger.info("Form-specific selector failed, trying role-based selector with nth")

            # Пробуем найти кнопку по более специфичному селектору
            try:
                # Ищем кнопку внутри формы
                form_locator = page.locator("form").filter(has_text="Search")
                if await form_locator.count() > 0:
                    logger.info("Found form with Search text")
                    search_button_in_form = form_locator.locator("button[type='submit']").first
                    if await search_button_in_form.count() > 0:
                        logger.info("Found submit button in form")
                        await search_button_in_form.click(force=True, timeout=10000)
                        logger.info("Search button clicked successfully using form filter")
                        # Успешно нажали кнопку, продолжаем выполнение
            except Exception as form_error:
                logger.warning(f"Error trying to click form-filtered button: {form_error}")

            # Если предыдущий метод не сработал, пробуем по роли
            search_buttons = page.get_by_role('button', name='Search')
            count = await search_buttons.count()

            if count > 0:
                logger.info(f"Found {count} search buttons by role")
                # Пробуем все кнопки по очереди, начиная с первой
                for i in range(count):
                    try:
                        button_to_click = search_buttons.nth(i)
                        logger.info(f"Trying to click search button #{i+1}")
                        await button_to_click.click(force=True, timeout=5000)
                        logger.info(f"Search button #{i+1} clicked successfully")
                        # Успешно нажали кнопку, выходим из цикла
                        break
                    except Exception as btn_error:
                        logger.warning(f"Failed to click search button #{i+1}: {btn_error}")

                # Если ни одна кнопка не сработала, пробуем JavaScript клик на первой
                logger.info("Trying JavaScript click on first search button")
                await page.evaluate("document.querySelector('button[type=\"submit\"]').click()")
                logger.info("JavaScript click executed")
            else:
                # Если не нашли по роли, используем стандартную функцию
                if not await click_search_button(page):
                    logger.error("Failed to click search button or submit form. Stopping processing for this category.")
                    logger.info(f"===== CATEGORY PROCESSING FAILED (Search Click): {category_display_name} =====")
                    # Снепшот должен был сохраниться внутри click_search_button
                    return []
        logger.info("Search initiated successfully.")
    except Exception as e:
        logger.error(f"Error clicking search button: {e}")
        await save_debug_snapshot(page, f"search_button_click_error_{category_display_name}")
        logger.info(f"===== CATEGORY PROCESSING FAILED (Search Click Error): {category_display_name} =====")
        return []

    # 5. Сбор результатов с пагинацией
    all_category_results: List[Dict[str, Any]] = []
    page_num = 1
    max_pages = 500 # Ограничение на всякий случай

    while page_num <= max_pages:
        logger.info(f"--- Processing results page {page_num} for category '{category_display_name}' ---")
        await save_debug_snapshot(page, f"results_page_{page_num}_{category_display_name}")

        # Ожидание загрузки результатов на текущей странице
        try:
            # Ждем появления либо карточек, либо сообщения "нет результатов"
            # Расширяем список селекторов и увеличиваем таймаут
            await page.wait_for_selector(
                ".search-result-item, .searchResult-item, .result-card, .profile-card, article.cpa-entry, div.profile-summary, li.item, " # Карточки (добавлены селекторы для CPA Quebec)
                "ul#search-results > li, " # Селектор для CPA Quebec
                ".no-results, .aucun-résultat, .search-results, .results-container, div.cpa-directory-results, " # Сообщение об отсутствии результатов или контейнер результатов
                "div[class*='result'], div[class*='card'], div[class*='profile'], div[class*='entry'], " # Общие селекторы по части класса
                "table tr[data-id], .table-row, tbody tr", # Результаты в виде таблицы
                state="attached", # Ждем появления в DOM
                timeout=60000 # Увеличенный таймаут для загрузки результатов
            )

            # Дополнительная пауза для полной загрузки результатов
            await page.wait_for_timeout(2000)

            # Проверяем, есть ли на странице информация о результатах (например, "1-10 of 100 results")
            try:
                results_info = await page.locator(".panel.callout, .results-info, .pagination-info").first.inner_text(timeout=2000)
                logger.debug(f"Results information found: {results_info}")
            except Exception:
                logger.debug("No results information element found.")

            logger.debug("Results area or 'no results' message appeared.")
        except PlaywrightTimeout:
            logger.error(f"Timeout waiting for results to load on page {page_num}.")
            await save_debug_snapshot(page, f"error_results_timeout_page_{page_num}_{category_display_name}")
            break # Прерываем пагинацию

        # Ищем карточки на текущей странице
        found_cards, cards_locator, card_count = await collect_result_cards(page)

        if not found_cards or card_count == 0:
            logger.info(f"No more result cards found on page {page_num}. Ending pagination.")
            break

        logger.info(f"Found {card_count} result cards on page {page_num}.")

        # Обработка карточек на текущей странице
        page_results: List[Dict[str, Any]] = []
        tasks = []
        for i in range(card_count):
            # Запускаем извлечение данных для каждой карточки асинхронно (в пределах одной страницы)
             tasks.append(asyncio.create_task(extract_card_data(cards_locator.nth(i), i, category)))

        # Собираем результаты обработки карточек на странице
        card_processing_results = await asyncio.gather(*tasks)
        for result_item in card_processing_results:
             if result_item: # Добавляем только успешно обработанные
                 page_results.append(result_item)

        logger.info(f"Successfully processed {len(page_results)} cards out of {card_count} on page {page_num}.")
        all_category_results.extend(page_results)

        # Пагинация: ищем кнопку "Next"
        logger.debug("Looking for 'Next' pagination button...")
        try:
            next_button_selectors = [
                 "a[rel='next']",                   # Стандартный rel=next
                 "a[aria-label='Next page']",      # Aria label
                 "a:has-text('Next')",             # Текст "Next"
                 "a:has-text('Suivant')",          # Текст "Suivant" (франц.)
                 "li.pagination-next > a",         # Структура пагинации
                 ".pagination .next a"             # Другая структура
            ]
            next_button: Optional[Locator] = None
            for sel in next_button_selectors:
                 locator = page.locator(sel).first
                 if await locator.count() > 0:
                      if await locator.is_visible(timeout=1000):
                           logger.debug(f"Found potential 'Next' button with selector: {sel}")
                           # Проверяем, что кнопка не отключена
                           if not await locator.evaluate("node => node.classList.contains('disabled') or node.hasAttribute('disabled') or node.closest('.disabled')", timeout=1000):
                                next_button = locator
                                break
                           else:
                                logger.debug(f"Next button ({sel}) found but is disabled.")
                                next_button = None # Явно сбрасываем, т.к. она неактивна
                                break # Если нашли отключенную, дальше можно не искать
                      else:
                           logger.debug(f"Next button ({sel}) found but not visible.")

            if next_button:
                logger.info(f"Found active 'Next' button. Clicking to page {page_num + 1}...")
                await next_button.scroll_into_view_if_needed(timeout=5000)
                await page.wait_for_timeout(200)
                await next_button.click(timeout=15000)
                page_num += 1
                # Ожидание после клика - лучше ждать конкретного события/селектора
                # await page.wait_for_load_state("domcontentloaded", timeout=30000)
                await page.wait_for_timeout(2000) # Короткая пауза для начала загрузки
            else:
                logger.info("No active 'Next' button found. Reached the last page.")
                break # Выход из цикла пагинации

        except Exception as e:
            logger.error(f"Error handling pagination on page {page_num}: {e}", exc_info=True)
            await save_debug_snapshot(page, f"error_pagination_page_{page_num}_{category_display_name}")
            break # Прерываем пагинацию при ошибке

    logger.info(f"Finished collecting results for category '{category_display_name}'. Total items found: {len(all_category_results)}")

    # 6. Сбор деталей профилей (если включено)
    final_results = all_category_results
    if get_details and all_category_results:
        logger.info(f"Starting detailed scraping for {len(all_category_results)} profiles...")
        detailed_results: List[Dict[str, Any]] = []
        # Используем свой контекст для открытия вкладок, чтобы изолировать их
        # Используем стандартный размер viewport вместо получения его из page
        detail_context = await page.context.browser.new_context(
            viewport={"width": 1366, "height": 768}  # Стандартный размер
        )
        detail_page = await detail_context.new_page() # Одна страница для последовательного обхода

        processed_urls = set() # Для избежания дубликатов URL

        for i, item in enumerate(all_category_results):
             profile_url = item.get("profile_url")
             if profile_url and profile_url not in processed_urls:
                 logger.info(f"Scraping details for item {i+1}/{len(all_category_results)}: {item.get('name')} ({profile_url})")
                 try:
                     # Используем scrape_details_sequential для последовательного обхода
                     details = await scrape_details_sequential(detail_page, profile_url)
                     item.update(details) # Обновляем исходный словарь
                     detailed_results.append(item)
                     processed_urls.add(profile_url)
                     # Небольшая пауза между запросами деталей
                     await asyncio.sleep(0.5)
                 except Exception as e_detail:
                     logger.error(f"Failed to scrape details for {profile_url}: {e_detail}")
                     # Добавляем запись без деталей, чтобы не потерять основную информацию
                     detailed_results.append(item)
             elif not profile_url:
                  logger.warning(f"Skipping detail scraping for item {i+1} ({item.get('name')}) - no profile URL.")
                  detailed_results.append(item) # Добавляем без деталей
             elif profile_url in processed_urls:
                  logger.debug(f"Skipping duplicate URL: {profile_url}")
                  # Найдем оригинал и добавить текущую запись как дубликат? Или просто пропустим?
                  # Пока просто пропустим добавление дубликата в final_results
                  pass # Не добавляем дубликат

        final_results = detailed_results # Заменяем список результатами с деталями (или без, если были ошибки)
        logger.info(f"Finished scraping details. Total items with attempted details: {len(final_results)}")
        # Закрываем контекст и страницу для сбора деталей
        await detail_page.close()
        await detail_context.close()
    elif not get_details:
         logger.info("Detailed scraping is disabled.")

    logger.info(f"===== FINISHED CATEGORY PROCESSING: {category_display_name} (Total: {len(final_results)} items) =====")
    return final_results


# Семафор теперь не используется для scrape_details, используем scrape_details_sequential

# Версия для последовательного сбора деталей с использованием одной вкладки
@precondition(
    lambda page, url: isinstance(page, Page) and not page.is_closed() and
    isinstance(url, str) and len(url) > 0 and (url.startswith("http") or url.startswith("/")),
    "page должен быть экземпляром Page и не должен быть закрыт, url должен быть непустой строкой и начинаться с 'http' или '/'"
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, dict),
    "Результат должен быть словарем"
)
async def scrape_details_sequential(page: Page, url: str) -> Dict[str, Any]:
    """Получает детальную информацию о профиле, используя переданную страницу."""
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"
    assert isinstance(url, str) and len(url) > 0, "url должен быть непустой строкой"
    assert url.startswith("http") or url.startswith("/"), "url должен начинаться с 'http' или '/'"

    logger.debug(f"--- Scraping details from: {url} ---")
    details = {"email": None, "phone": None, "website": None, "address": None} # Добавим поля

    try:
        # Переходим по URL на переданной странице
        await page.goto(url, timeout=60000, wait_until="domcontentloaded")
        logger.debug("Detail page loaded.")

        # Ждем загрузки основного контента
        try:
            await page.wait_for_selector("h1, .profile-header, .entry-content", timeout=15000)
            logger.debug("Main content appeared.")
        except PlaywrightTimeout:
            logger.warning("Timeout waiting for main content on detail page.")
            await save_debug_snapshot(page, f"detail_page_timeout_{url.split('/')[-1]}")
            # Продолжаем попытку извлечь данные

        # --- Извлечение данных ---
        # Email
        try:
            email_locator = page.locator("a[href^='mailto:']").first
            if await email_locator.is_visible(timeout=1000):
                email_href = await email_locator.get_attribute('href')
                details['email'] = email_href.replace('mailto:', '').strip() if email_href else await email_locator.inner_text()
                logger.debug(f"  Email found (href): {details['email']}")
            else: # Поиск по тексту
                 content = await page.inner_text('body')
                 match = re.search(r'[\w\.\-+]+@[\w\.\-]+\.\w+', content)
                 if match:
                      details['email'] = match.group(0)
                      logger.debug(f"  Email found (regex): {details['email']}")
        except Exception as e:
            logger.debug(f"  Could not extract email: {e}")

        # Телефон
        try:
            phone_locator = page.locator("a[href^='tel:']").first
            if await phone_locator.is_visible(timeout=1000):
                phone_href = await phone_locator.get_attribute('href')
                details['phone'] = phone_href.replace('tel:', '').strip() if phone_href else await phone_locator.inner_text()
                logger.debug(f"  Phone found (href): {details['phone']}")
            else: # Поиск по тексту (улучшенный regex)
                 content = await page.inner_text('body')
                 # Regex для разных форматов телефонов (Северная Америка)
                 match = re.search(r'\(?\b[2-9]\d{2}\)?[-.\s]?\d{3}[-.\s]?\d{4}\b', content)
                 if match:
                      details['phone'] = match.group(0)
                      logger.debug(f"  Phone found (regex): {details['phone']}")
        except Exception as e:
            logger.debug(f"  Could not extract phone: {e}")

        # Веб-сайт
        try:
             # Ищем ссылку с текстом вроде "Website", "Site Web" или по URL
             website_locator = page.locator(
                 "a:has-text('Website'), a:has-text('Site Web'), a[href*='http']:not([href*='mailto']):not([href*='tel'])"
             ).first
             # Дополнительно проверяем ссылки рядом с иконкой глобуса/ссылки
             icon_link = page.locator(".fa-globe + a, .icon-website + a").first
             if await website_locator.is_visible(timeout=1000):
                  details['website'] = await website_locator.get_attribute('href')
                  logger.debug(f"  Website found (text): {details['website']}")
             elif await icon_link.is_visible(timeout=1000):
                  details['website'] = await icon_link.get_attribute('href')
                  logger.debug(f"  Website found (icon): {details['website']}")

        except Exception as e:
            logger.debug(f"  Could not extract website: {e}")

        # Адрес (может быть в нескольких строках)
        try:
             address_locator = page.locator(".address, .adr, [itemprop='address'], div:has(> .fa-map-marker)") # Разные селекторы
             if await address_locator.count() > 0:
                  # Берем текст всех найденных элементов адреса
                  address_parts = await address_locator.all_inner_texts()
                  details['address'] = " ".join(part.strip() for part in address_parts if part.strip())
                  logger.debug(f"  Address found: {details['address']}")
        except Exception as e:
             logger.debug(f"  Could not extract address: {e}")


    except PlaywrightError as e:
        logger.error(f"Playwright error scraping details for {url}: {e}")
        await save_debug_snapshot(page, f"detail_page_error_{url.split('/')[-1]}")
    except Exception as e:
        logger.error(f"Unexpected error scraping details for {url}: {e}", exc_info=True)
        await save_debug_snapshot(page, f"detail_page_unexpected_error_{url.split('/')[-1]}")

    logger.debug(f"--- Finished scraping details for {url}. Data: {details} ---")
    # Очищаем куки и localStorage перед следующим переходом, чтобы избежать накопления состояния
    try:
        await page.context.clear_cookies()
        await page.evaluate('localStorage.clear()')
        await page.evaluate('sessionStorage.clear()')
    except Exception as e_clear:
        logger.warning(f"Could not clear context state: {e_clear}")

    return details

# ---------------------------------------------------------------------------
# CLI & main
# ---------------------------------------------------------------------------

def setup_logging(log_level: str) -> None:
    """Настройка логирования с указанным уровнем."""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO # Уровень по умолчанию, если передан неверный
        print(f"Warning: Invalid log level '{log_level}'. Defaulting to INFO.")

    # Удаляем существующие обработчики, чтобы избежать дублирования при повторном вызове
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Создаем директорию для логов, если она не существует
    logs_dir = PROJECT_ROOT / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Создаем файл лога в директории logs
    log_filename = logs_dir / f"parser_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    # Настройка базовой конфигурации
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s [%(levelname)-8s] %(name)-20s: %(message)s", # Улучшенный формат
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_filename, encoding='utf-8') # Явно указываем кодировку
        ]
    )

    # Настройка логгера нашего приложения
    logger.setLevel(numeric_level)
    # Настройка логгеров библиотек
    logging.getLogger("playwright").setLevel(logging.WARNING)
    # Можно сделать логи playwright более подробными для отладки
    # logging.getLogger("playwright").setLevel(logging.DEBUG if numeric_level <= logging.DEBUG else logging.INFO)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.INFO) # INFO может быть полезно для отладки asyncio
    logging.getLogger("aiohttp.client").setLevel(logging.WARNING) # Уменьшаем шум от aiohttp

    logger.info(f"Logging setup complete. Level: {log_level}. Log file: {log_filename}")


def build_cli() -> argparse.Namespace:
    """Парсинг аргументов командной строки."""
    p = argparse.ArgumentParser(
        description="CPA Quebec Directory Parser (v0.3)",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter # Показывает значения по умолчанию
    )
    mode = p.add_mutually_exclusive_group(required=True) # Режим работы обязателен
    mode.add_argument("--by-category", action="store_true", help="Parse all client categories sequentially.")
    mode.add_argument("--category", choices=CLIENT_CATEGORIES, help="Parse only the specified client category.")
    mode.add_argument("--all", action='store_true', help="Parse without category filters (equivalent to 'All').")

    p.add_argument("--no-captcha", action="store_true", help="Disable automatic CAPTCHA solving (requires manual intervention).")
    p.add_argument("--no-details", action="store_true", help="Disable scraping of detailed profile pages (faster).")
    p.add_argument("--visible", action="store_true", help="Run the browser in visible mode (headed).")
    p.add_argument("--debug", action="store_true", help="Enable debug mode (saves screenshots, HTML, etc.).")
    p.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                   help="Set the logging level.")
    p.add_argument("--max-pages", type=int, default=500, help="Maximum number of result pages to scrape per category.")
    p.add_argument("--use-system-browser", action="store_true", help="Attempt to use system-installed Chrome/Chromium.")

    parsed_args = p.parse_args()

    # Обработка --all как отсутствие категории
    if parsed_args.all:
        parsed_args.category = None # Явно устанавливаем None, если выбран --all
        parsed_args.by_category = False # Убедимся, что не выбран другой режим

    return parsed_args


async def run() -> None:
    """Основная функция запуска парсера."""
    global args, ANTICAPTCHA_KEY
    args = build_cli()

    # Инициализация логирования и директории отладки после парсинга args
    setup_logging(args.log_level)
    initialize_debug_dir() # Создаем папку debug, если нужно

    logger.info("===== SCRIPT EXECUTION STARTED =====")
    logger.info(f"Arguments: {vars(args)}")

    # Обновляем ANTICAPTCHA_KEY на основе аргумента --no-captcha
    if args.no_captcha:
        logger.warning("Automatic CAPTCHA solving is DISABLED (--no-captcha).")
        ANTICAPTCHA_KEY = None
    else:
        # Загружаем ключ только если авто-решение включено
        load_dotenv()
        ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")
        if ANTICAPTCHA_KEY:
            logger.info(f"Anti-Captcha API Key found (ending with ...{ANTICAPTCHA_KEY[-5:]}). Automatic solving ENABLED.")
        else:
            logger.error("Anti-Captcha API Key not found in .env file or environment variables!")
            logger.error("Automatic CAPTCHA solving will FAIL. Use --no-captcha for manual solving.")
            # Можно либо выйти, либо продолжить с предупреждением
            # sys.exit(1)

    # Настройка параметров запуска браузера
    launch_opts = {
        "headless": not args.visible,
        "args": [
            "--no-sandbox", # Часто требуется в Docker/Linux
            "--disable-setuid-sandbox",
            "--disable-infobars",
            "--ignore-certifcate-errors",
            "--ignore-ssl-errors",
            # "--disable-blink-features=AutomationControlled" # Попытка скрыть автоматизацию
        ],
        # "slow_mo": 50 # Замедление для отладки
    }
    if not args.visible:
         launch_opts["args"].append("--start-maximized") # Максимизируем окно только в headless

    logger.debug(f"Browser launch options: {launch_opts}")

    # Поиск системного браузера, если запрошено
    executable_path = None
    if args.use_system_browser:
        logger.info("Attempting to find system Chrome/Chromium...")
        possible_names = ["google-chrome", "chrome", "chromium-browser", "chromium"]
        for name in possible_names:
            path = shutil.which(name)
            if path:
                 executable_path = path
                 logger.info(f"Using system browser found at: {executable_path}")
                 break
        if not executable_path:
             logger.warning("System browser not found, will use Playwright's bundled browser.")
    if executable_path:
        launch_opts["executable_path"] = executable_path

    browser = None
    context = None
    page = None
    playwright = None

    try:
        logger.info("Initializing Playwright...")
        playwright = await async_playwright().start()
        logger.info(f"Launching browser ({'headed' if args.visible else 'headless'})...")
        browser = await playwright.chromium.launch(**launch_opts)
        logger.info(f"Browser launched successfully. Version: {browser.version}")

        # Создаем контекст с настройками
        context = await browser.new_context(
            viewport={"width": 1366, "height": 768}, # Типичное разрешение
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36", # Пример User Agent
            accept_downloads=False,
            # locale='en-US', # Установка локали
            # timezone_id='America/New_York' # Установка таймзоны
        )
        logger.info("Browser context created.")
        # Добавляем обработку диалоговых окон (например, alert)
        context.on("page", lambda p: p.on("dialog", lambda dialog: asyncio.create_task(dialog.dismiss())))

        page = await context.new_page()
        logger.info("New page created.")

        # Основная логика парсинга
        all_results: List[Dict[str, Any]] = []
        categories_to_process: List[Optional[str]] = []

        if args.by_category:
            categories_to_process = CLIENT_CATEGORIES
            logger.info(f"Mode: Processing all {len(categories_to_process)} categories.")
        elif args.category:
            categories_to_process = [args.category]
            logger.info(f"Mode: Processing single category: {args.category}")
        elif args.all:
            categories_to_process = [""] # Используем пустую строку для режима "All"
            logger.info("Mode: Processing without category filters ('All').")

        get_details_flag = not args.no_details

        for category_name in categories_to_process:
            category_display = category_name if category_name else "All"
            logger.info(f"--- Starting processing for category: {category_display} ---")
            try:
                # Вызываем process_category
                # Передаем максимальное количество страниц из аргументов
                # process_category.max_pages = args.max_pages
                cat_results = await process_category(page, category_name, get_details=get_details_flag)
                logger.info(f"Category '{category_display}' processed. Found {len(cat_results)} items.")
                all_results.extend(cat_results)
                # Небольшая пауза между категориями
                await asyncio.sleep(2)
            except Exception as e:
                 # Обработка критических ошибок Anti-Captcha
                 if "AntiCaptcha Error: ERROR_ZERO_BALANCE" in str(e):
                      logger.critical("ZERO BALANCE in Anti-Captcha. Stopping script execution.")
                      break # Прерываем цикл обработки категорий
                 elif "AntiCaptcha Error" in str(e):
                      logger.critical(f"Critical Anti-Captcha error during category '{category_display}': {e}. Stopping script execution.")
                      break # Прерываем цикл
                 else:
                      logger.error(f"Error processing category '{category_display}': {e}", exc_info=True)
                      await save_debug_snapshot(page, f"error_fatal_category_{category_display}")
                      logger.warning(f"Skipping category '{category_display}' due to error.")
                      continue # Продолжаем со следующей категорией

        # Сохранение результатов
        if all_results:
            # Удаление дубликатов по URL профиля перед сохранением
            unique_results = []
            seen_urls = set()
            for item in all_results:
                url = item.get("profile_url")
                # Добавляем, если URL нет или он еще не встречался
                if not url or url not in seen_urls:
                    unique_results.append(item)
                    if url:
                        seen_urls.add(url)
                else:
                    logger.debug(f"Removing duplicate entry by URL: {url} (Name: {item.get('name')})")

            logger.info(f"Total unique items collected: {len(unique_results)} (removed {len(all_results) - len(unique_results)} duplicates)")

            timestamp = timestamp_str()
            if not OUTPUT_DIR.exists():
                 OUTPUT_DIR.mkdir(parents=True)
            outfile = OUTPUT_DIR / f"cpa_results_{timestamp}.json"
            logger.info(f"Saving {len(unique_results)} unique results to {outfile}...")
            try:
                with open(outfile, 'w', encoding='utf-8') as f:
                     json.dump(unique_results, f, ensure_ascii=False, indent=2)
                logger.info(f"Results successfully saved to {outfile}")
            except (IOError, TypeError) as e:
                 logger.error(f"Failed to save results to {outfile}: {e}")
        else:
            logger.warning("No results collected to save.")

    except Exception as e:
        logger.critical(f"A critical error occurred during script execution: {e}", exc_info=True)
        # Попытка сохранить последний скриншот/HTML, если страница еще существует
        if page and not page.is_closed():
            await save_debug_snapshot(page, "critical_error_final_state")
    finally:
        # Гарантированное закрытие ресурсов
        logger.info("Cleaning up resources...")
        await close_anticaptcha_session() # Закрываем сессию Anti-Captcha
        if context:
            logger.debug("Closing browser context...")
            await context.close()
        if browser:
            logger.debug("Closing browser...")
            await browser.close()
        if playwright:
             logger.debug("Stopping Playwright...")
             await playwright.stop()
        logger.info("===== SCRIPT EXECUTION FINISHED =====")


if __name__ == "__main__":
    # Запуск асинхронной функции run
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        logger.info("Script execution interrupted by user (KeyboardInterrupt).")
    except Exception as main_exception:
         # Логируем необработанные исключения верхнего уровня
         logger.critical(f"Unhandled exception at top level: {main_exception}", exc_info=True)