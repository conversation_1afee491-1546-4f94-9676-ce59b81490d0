#!/usr/bin/env python3
"""
CPA Quebec Directory – one‑file parser  (v0.2)
===========================================
* Исправлено обнаружение reCAPTCHA (anchor‑iframe vs challenge‑iframe)
* Добавлено авто‑закрытие cookie‑баннера («Tout refuser»)
* Лёгкая рефакторизация solve_captcha()
* Добавлены улучшения в обработке ошибок и логировании

Запуск примера:
    python3 cpa_quebec_single_file.py --by-category --visible --debug
"""
import argparse
import asyncio
import json
import logging
import os
import re
import time
from datetime import datetime
from pathlib import Path
from typing import (
    List,
    Dict,
    Any,
    Optional,
    Tuple,
    Union,
    TypeVar,
    Callable,
    cast,
    Generic,
)
from functools import wraps
import inspect
import aiohttp
from dotenv import load_dotenv
from playwright.async_api import (
    async_playwright,
    Page,
    Locator,
    TimeoutError as PlaywrightTimeoutError,
    Browser,
    BrowserContext,
)
from dataclasses import dataclass

# Настройка логирования
logger = logging.getLogger("cpa_quebec_parser")

# ---------------------------------------------------------------------------
# Конфигурационные классы
# ---------------------------------------------------------------------------


@dataclass
class BrowserConfig:
    """Конфигурация браузера"""

    viewport_width: int = 1280
    viewport_height: int = 800
    headless: bool = True
    timeout: int = 30000
    user_agent: str = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    )


@dataclass
class CaptchaConfig:
    """Конфигурация капчи"""

    max_wait_seconds: int = 180
    check_interval: int = 3
    required_consecutive_checks: int = 2
    token_min_length: int = 50
    max_retries: int = 3
    retry_delay: int = 2


@dataclass
class ParserConfig:
    """Конфигурация парсера"""

    debug: bool = False
    by_category: bool = False
    category: Optional[str] = None
    get_details: bool = True
    output_dir: Path = Path("output")


# ---------------------------------------------------------------------------
# Кастомные исключения
# ---------------------------------------------------------------------------


class CaptchaError(Exception):
    """Базовый класс для ошибок капчи"""

    pass


class ZeroBalanceError(CaptchaError):
    """Ошибка нулевого баланса в Anti-Captcha"""

    pass


class BrowserError(Exception):
    """Ошибки браузера"""

    pass


class ParserError(Exception):
    """Ошибки парсера"""

    pass


# ---------------------------------------------------------------------------
# Улучшенное логирование
# ---------------------------------------------------------------------------


def setup_logging(log_level: str) -> None:
    """Улучшенная настройка логирования"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[logging.FileHandler("cpa_parser.log"), logging.StreamHandler()],
    )


def log_with_context(message: str, **context):
    """Логирование с контекстом"""
    extra = {k: v for k, v in context.items() if v is not None}
    logger.info(message, extra=extra)


# ---------------------------------------------------------------------------
# Контрактное программирование
# ---------------------------------------------------------------------------

T = TypeVar("T")
R = TypeVar("R")


class ContractViolation(Exception):
    """Исключение, возникающее при нарушении контракта."""

    pass


class PreconditionViolation(ContractViolation):
    """Исключение, возникающее при нарушении предусловия."""

    pass


class PostconditionViolation(ContractViolation):
    """Исключение, возникающее при нарушении постусловия."""

    pass


class InvariantViolation(ContractViolation):
    """Исключение, возникающее при нарушении инварианта."""

    pass


def precondition(condition: Callable[..., bool], message: str = None):
    """Декоратор для проверки предусловия.

    Args:
        condition: Функция, принимающая те же аргументы, что и декорируемая функция,
                  и возвращающая булево значение.
        message: Сообщение об ошибке при нарушении предусловия.
    """

    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Получаем имена параметров функции
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            # Проверяем предусловие
            if not condition(*args, **kwargs):
                error_msg = message or f"Нарушено предусловие для {func.__name__}"
                logger.error(error_msg)
                raise PreconditionViolation(error_msg)

            return func(*args, **kwargs)

        return wrapper

    return decorator


def postcondition(condition: Callable[..., bool], message: str = None):
    """Декоратор для проверки постусловия.

    Args:
        condition: Функция, принимающая результат декорируемой функции и её аргументы,
                  и возвращающая булево значение.
        message: Сообщение об ошибке при нарушении постусловия.
    """

    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)

            # Проверяем постусловие
            if not condition(result, *args, **kwargs):
                error_msg = message or f"Нарушено постусловие для {func.__name__}"
                logger.error(error_msg)
                raise PostconditionViolation(error_msg)

            return result

        return wrapper

    return decorator


def invariant(condition: Callable[..., bool], message: str = None):
    """Декоратор для проверки инварианта.

    Args:
        condition: Функция, проверяющая инвариант объекта.
        message: Сообщение об ошибке при нарушении инварианта.
    """

    def decorator(cls):
        original_init = cls.__init__

        @wraps(original_init)
        def new_init(self, *args, **kwargs):
            original_init(self, *args, **kwargs)
            if not condition(self):
                error_msg = message or f"Нарушен инвариант для {cls.__name__}"
                logger.error(error_msg)
                raise InvariantViolation(error_msg)

        cls.__init__ = new_init

        # Перехватываем все методы класса
        for name, method in inspect.getmembers(cls, inspect.isfunction):
            if name != "__init__":
                setattr(
                    cls, name, _wrap_method_with_invariant(method, condition, message)
                )

        return cls

    return decorator


def _wrap_method_with_invariant(method, condition, message):
    @wraps(method)
    def wrapper(self, *args, **kwargs):
        # Проверяем инвариант перед выполнением метода
        if not condition(self):
            error_msg = (
                message or f"Нарушен инвариант перед выполнением {method.__name__}"
            )
            logger.error(error_msg)
            raise InvariantViolation(error_msg)

        result = method(self, *args, **kwargs)

        # Проверяем инвариант после выполнения метода
        if not condition(self):
            error_msg = (
                message or f"Нарушен инвариант после выполнения {method.__name__}"
            )
            logger.error(error_msg)
            raise InvariantViolation(error_msg)

        return result

    return wrapper


# Функции для проверки типов
def check_type(value, expected_type, param_name=None):
    """Проверяет, соответствует ли значение ожидаемому типу.

    Args:
        value: Проверяемое значение.
        expected_type: Ожидаемый тип или кортеж типов.
        param_name: Имя параметра для сообщения об ошибке.

    Returns:
        bool: True, если тип соответствует ожидаемому, иначе False.
    """
    if value is None and type(None) in (
        expected_type if isinstance(expected_type, tuple) else (expected_type,)
    ):
        return True

    if not isinstance(value, expected_type):
        if param_name:
            logger.error(
                f"Параметр {param_name} имеет неверный тип: {type(value)}, ожидается {expected_type}"
            )
        return False
    return True


def check_not_none(value, param_name=None):
    """Проверяет, что значение не None.

    Args:
        value: Проверяемое значение.
        param_name: Имя параметра для сообщения об ошибке.

    Returns:
        bool: True, если значение не None, иначе False.
    """
    if value is None:
        if param_name:
            logger.error(f"Параметр {param_name} не должен быть None")
        return False
    return True


# Глобальные переменные
ANTICAPTCHA_KEY: Optional[str] = None
args = None
anticaptcha_session: Optional[aiohttp.ClientSession] = None

# ---------------------------------------------------------------------------
# Функции для работы с Anti-Captcha API
# ---------------------------------------------------------------------------


async def get_anticaptcha_session() -> aiohttp.ClientSession:
    """Возвращает существующую или создает новую сессию для запросов к Anti-Captcha API."""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        logger.debug("Создание новой aiohttp сессии для Anti-Captcha")
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session


async def close_anticaptcha_session() -> None:
    """Закрывает сессию для запросов к Anti-Captcha API."""
    global anticaptcha_session
    if anticaptcha_session and not anticaptcha_session.closed:
        logger.debug("Закрытие aiohttp сессии для Anti-Captcha")
        await anticaptcha_session.close()
        anticaptcha_session = None


async def anticaptcha_request(method: str, **payload) -> Dict[str, Any]:
    """Выполняет запрос к API Anti-Captcha."""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()

    # Добавляем clientKey ко всем запросам, если он не передан явно
    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY

    logger.debug(
        f"Отправка запроса к Anti-Captcha: {method}, ключи payload: {list(payload.keys())}"
    )

    try:
        async with session.post(
            url, json=payload, timeout=30
        ) as resp:  # Добавляем таймаут
            resp.raise_for_status()  # Проверяем HTTP статус
            response_json = await resp.json()
            logger.debug(f"Ответ Anti-Captcha ({method}): {response_json}")
            return response_json
    except aiohttp.ClientResponseError as e:
        logger.error(
            f"Ошибка HTTP API Anti-Captcha ({method}, статус={e.status}): {e.message}"
        )
        # Попытка прочитать тело ответа, если возможно
        error_body = await e.response.text()
        logger.error(f"Тело ошибки API Anti-Captcha: {error_body}")
        return {
            "errorId": 1,
            "errorCode": f"HTTP_{e.status}",
            "errorDescription": e.message,
        }
    except asyncio.TimeoutError:
        logger.error(f"Таймаут API Anti-Captcha ({method})")
        return {
            "errorId": 1,
            "errorCode": "TIMEOUT_ERROR",
            "errorDescription": "Истекло время ожидания запроса",
        }
    except Exception as e:
        logger.error(f"Ошибка при запросе к API Anti-Captcha ({method}): {e}")
        return {
            "errorId": 1,
            "errorCode": "CONNECTION_ERROR",
            "errorDescription": str(e),
        }


@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple)
    and len(result) == 2
    and isinstance(result[0], bool)
    and (result[1] is None or isinstance(result[1], int)),
    "Результат должен быть кортежем (bool, Optional[int])",
)
async def create_captcha_task(
    site_key: str, page_url: str, page: Optional[Page] = None
) -> Tuple[bool, Optional[int]]:
    """Создает задачу на решение капчи в Anti-Captcha."""
    assert (
        isinstance(site_key, str) and len(site_key) > 0
    ), "site_key должен быть непустой строкой"
    assert (
        isinstance(page_url, str) and len(page_url) > 0
    ), "page_url должен быть непустой строкой"

    if not ANTICAPTCHA_KEY:
        logger.error("ANTICAPTCHA_API_KEY не установлен. Невозможно создать задачу.")
        return False, None

    logger.info(
        f"Создание задачи Anti-Captcha, sitekey={site_key[:8]}..., page={page_url}"
    )

    # Получаем User-Agent браузера для лучшей совместимости с enterprise reCAPTCHA
    user_agent = None
    if page and not page.is_closed():
        try:
            user_agent = await page.evaluate("() => navigator.userAgent")
            logger.debug(f"Используется User-Agent браузера: {user_agent}")
        except Exception as e:
            logger.warning(f"Не удалось получить User-Agent браузера: {e}")
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            logger.debug(f"Используется запасной User-Agent: {user_agent}")
    else:
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        logger.debug(f"Используется User-Agent по умолчанию: {user_agent}")

    create_payload = {
        # "clientKey" будет добавлен в anticaptcha_request
        "task": {
            "type": "NoCaptchaTaskProxyless",
            "websiteURL": page_url,
            "websiteKey": site_key,
        },
        "softId": 8041,  # Пример softId, если есть
    }

    # Добавляем User-Agent, если он доступен
    if user_agent:
        create_payload["task"]["userAgent"] = user_agent

    task_resp = await anticaptcha_request("createTask", **create_payload)

    # Детальное логирование ответа API для отладки
    logger.debug(f"Ответ Anti-Captcha createTask: {task_resp}")

    task_id_raw = task_resp.get("taskId")
    task_id = None
    if task_id_raw is not None:
        if isinstance(task_id_raw, int):
            task_id = task_id_raw
        elif isinstance(task_id_raw, str) and task_id_raw.isdigit():
            task_id = int(task_id_raw)
        else:
            logger.error(
                f"Anti-Captcha returned task ID in unexpected format: {task_id_raw}. Type: {type(task_id_raw)}"
            )
            # task_id remains None, will be caught by 'if not task_id' check below

    error_id = task_resp.get("errorId", 0)

    if error_id > 0 or not task_id:
        error_code = task_resp.get("errorCode", "UNKNOWN_ERROR")
        error_desc = task_resp.get("errorDescription", "Не удалось создать задачу")
        logger.error(f"Ошибка Anti-Captcha createTask: {error_code} - {error_desc}")
        # Особая обработка критических ошибок
        if error_code in ["ERROR_KEY_DOES_NOT_EXIST", "ERROR_ZERO_BALANCE"]:
            logger.critical(
                f"Критическая ошибка Anti-Captcha: {error_code}. Остановка."
            )
            # Можно выбросить исключение, чтобы остановить весь процесс
            raise Exception(f"AntiCaptcha Error: {error_code}")
        return False, None

    logger.info(f"Задача Anti-Captcha успешно создана, ID задачи: {task_id}")
    return True, task_id


@postcondition(
    lambda result, *args, **kwargs: isinstance(result, tuple)
    and len(result) == 2
    and isinstance(result[0], bool)
    and (result[1] is None or isinstance(result[1], str)),
    "Результат должен быть кортежем (bool, Optional[str])",
)
async def get_captcha_result(
    task_id: int, max_attempts: int = 60, initial_wait: int = 5, max_wait_time: int = 10
) -> Tuple[bool, Optional[str]]:
    """Ожидает и получает результат решения капчи с адаптивным ожиданием."""
    assert (
        isinstance(task_id, int) and task_id > 0
    ), "task_id должен быть положительным целым числом"
    assert (
        isinstance(max_attempts, int) and max_attempts > 0
    ), "max_attempts должен быть положительным целым числом"
    assert (
        isinstance(initial_wait, int) and initial_wait > 0
    ), "initial_wait должен быть положительным целым числом"
    assert (
        isinstance(max_wait_time, int) and max_wait_time > 0
    ), "max_wait_time должен быть положительным целым числом"

    if not ANTICAPTCHA_KEY:
        logger.error(
            "ANTICAPTCHA_API_KEY не установлен. Невозможно получить результат задачи."
        )
        return False, None

    logger.info(
        f"Ожидание результата Anti-Captcha для задачи ID: {task_id} (макс. попыток: {max_attempts})"
    )
    start_time = time.time()

    await asyncio.sleep(initial_wait)  # Первоначальное ожидание перед первым запросом

    for attempt in range(max_attempts):
        logger.debug(
            f"Попытка {attempt + 1}/{max_attempts} получить результат для задачи {task_id}..."
        )

        try:
            res = await anticaptcha_request("getTaskResult", taskId=task_id)

            # Детальное логирование ответа API для отладки
            logger.debug(f"Ответ Anti-Captcha getTaskResult: {res}")

            status = res.get("status")
            error_id = res.get("errorId", 0)

            if error_id > 0:
                error_code = res.get("errorCode", "UNKNOWN_ERROR")
                error_desc = res.get(
                    "errorDescription", "Не удалось получить результат задачи"
                )
                logger.error(
                    f"Ошибка Anti-Captcha getTaskResult: {error_code} - {error_desc}"
                )

                # Обработка критических ошибок
                if error_code in ["ERROR_KEY_DOES_NOT_EXIST", "ERROR_ZERO_BALANCE"]:
                    logger.critical(
                        f"Критическая ошибка Anti-Captcha: {error_code}. Остановка."
                    )
                    raise Exception(
                        f"AntiCaptcha Error: {error_code}"
                    )  # Останавливаем процесс
                # Если задача не найдена или токен истек, возвращаем ошибку
                elif error_code in ["ERROR_TOKEN_EXPIRED", "ERROR_TASK_NOT_FOUND"]:
                    logger.warning(
                        f"Задача Anti-Captcha истекла или не найдена: {error_code}"
                    )
                    return False, None

                # Для других ошибок продолжаем попытки
                logger.warning(
                    f"Некритическая ошибка Anti-Captcha: {error_code}. Продолжаем..."
                )

            elif status == "ready":
                solution = res.get("solution")
                token = solution.get("gRecaptchaResponse") if solution else None
                if token:
                    elapsed = time.time() - start_time
                    logger.info(
                        f"Решение Anti-Captcha успешно получено для задачи {task_id} за {elapsed:.1f}с. Длина токена: {len(token)}"
                    )
                    logger.debug(f"Токен: {token[:20]}...")
                    # Дополнительная проверка токена
                    if len(token) < 50:
                        logger.warning(
                            "Получен подозрительно короткий токен от Anti-Captcha."
                        )
                    return True, token
                else:
                    logger.error(
                        f"Anti-Captcha вернул статус 'ready', но токен не найден для задачи {task_id}. Ответ: {res}"
                    )
                    return False, None
            elif status == "processing":
                logger.debug(f"Задача {task_id} все еще обрабатывается...")
            else:
                logger.warning(
                    f"Неизвестный статус Anti-Captcha для задачи {task_id}: {status}. Ответ: {res}"
                )

        except Exception as e:
            # Обработка исключений, выброшенных из anticaptcha_request или критических ошибок
            if "AntiCaptcha Error" in str(e):
                # Пробрасываем критические ошибки дальше
                raise
            logger.error(f"Ошибка при получении результата Anti-Captcha: {e}")

        # Адаптивное ожидание между запросами
        wait_time = min(initial_wait * (1.5 ** min(attempt, 5)), max_wait_time)
        logger.debug(f"Ожидание {wait_time:.1f}с перед следующей попыткой...")
        await asyncio.sleep(wait_time)

    logger.error(
        f"Превышено максимальное количество попыток ({max_attempts}) для получения результата задачи {task_id}"
    )
    return False, None


PROJECT_ROOT = Path(__file__).parent
OUTPUT_DIR = PROJECT_ROOT / "output"
OUTPUT_DIR.mkdir(exist_ok=True)

# Создаем директорию для отладочных файлов с датой
DEBUG_DIR = PROJECT_ROOT / "debug" / datetime.now().strftime("%Y%m%d")
DEBUG_DIR.mkdir(
    parents=True, exist_ok=True
)  # Создаем директорию с датой и все родительские директории

# Максимальное количество отладочных файлов в директории
MAX_DEBUG_FILES = 100

CLIENT_CATEGORIES = [
    "Individuals",  # Оставляем только одну категорию для тестирования
    "Large companies",
    "NFPOs",
    "Professional firms",
    "Public corporations",
    "Retailers",
    "Self-employed workers",
    "SMEs",
    "Start-ups",
    "Syndicates of co-owners",
]
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"

# ---------------------------------------------------------------------------
# Utility helpers
# ---------------------------------------------------------------------------


@postcondition(
    lambda result, *args, **kwargs: isinstance(result, str) and len(result) == 19,
    "Результат timestamp_str должен быть строкой длиной 19 символов",
)
def timestamp_str() -> str:
    """Возвращает текущую дату и время в формате строки для использования в именах файлов.

    Returns:
        str: Строка в формате 'YYYY-MM-DD_HH-MM-SS'

    Постусловия:
        - Результат должен быть строкой длиной 19 символов
    """
    return datetime.now().strftime("%Y-%m-%d_%H-%M-%S")


@precondition(
    lambda file_type: isinstance(file_type, str) and len(file_type) > 0,
    "file_type должен быть непустой строкой",
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, str)
    and len(result) > 0
    and Path(result).parent.exists(),
    "Результат должен быть непустой строкой и директория должна существовать",
)
def manage_debug_files(file_type: str) -> str:
    """Управляет отладочными файлами: создает имя файла в отладочной директории и
    ограничивает общее количество файлов в директории.

    Args:
        file_type: Тип файла (например, 'screenshot', 'html')

    Returns:
        Полный путь к новому файлу

    Предусловия:
        - file_type должен быть непустой строкой

    Постусловия:
        - Результат должен быть непустой строкой
        - Родительская директория результата должна существовать
    """
    # Проверяем общее количество файлов в директории
    all_debug_files = list(DEBUG_DIR.glob("*.*"))

    # Если превышен лимит, удаляем самые старые файлы
    if len(all_debug_files) >= MAX_DEBUG_FILES:
        # Сортируем файлы по времени создания (от старых к новым)
        all_debug_files.sort(key=lambda x: x.stat().st_mtime)

        # Определяем, сколько файлов нужно удалить
        files_to_remove = (
            len(all_debug_files) - MAX_DEBUG_FILES + 1
        )  # +1 для нового файла

        # Удаляем самые старые файлы
        for i in range(files_to_remove):
            # Используем missing_ok=True для Python 3.8+
            all_debug_files[i].unlink(missing_ok=True)
            logger.debug(f"Удален старый отладочный файл: {all_debug_files[i].name}")

    # Создаем имя нового файла
    filename = f"{file_type}_{timestamp_str()}"
    result_path = str(DEBUG_DIR / filename)

    # Дополнительная проверка для постусловия
    assert (
        isinstance(result_path, str) and len(result_path) > 0
    ), "Результат должен быть непустой строкой"
    assert Path(
        result_path
    ).parent.exists(), "Родительская директория результата должна существовать"

    return result_path


async def close_cookies_banner(page: Page) -> bool:
    """Закрывает баннер с cookie, если он присутствует.

    Args:
        page: Объект страницы Playwright

    Returns:
        bool: True, если баннер был закрыт, False если баннер не найден

    Предусловия:
        - page должен быть экземпляром Page
        - page не должен быть закрыт

    Постусловия:
        - Результат должен быть булевым значением
    """
    logger.debug("Проверяем наличие баннера с cookie...")

    # Проверка предусловия внутри функции
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    # Список возможных селекторов для кнопок закрытия баннера
    cookie_button_selectors = [
        "text=Tout refuser",  # Французский вариант "Отклонить все"
        "text=Reject All",  # Английский вариант
        "text=Decline All",  # Альтернативный английский вариант
        "button.cookie-decline",
        "button.cookie-reject",
        "[aria-label='Close cookie banner']",
        "#cookieRejectButton",
    ]

    for selector in cookie_button_selectors:
        try:
            button = page.locator(selector)
            if await button.count() > 0 and await button.is_visible(timeout=5000):
                logger.debug(
                    f"Найдена кнопка закрытия баннера с селектором: {selector}"
                )
                await button.click(timeout=1500)
                logger.debug("Баннер с cookie закрыт")
                return True
        except PlaywrightTimeoutError:
            continue  # Пробуем следующий селектор
        except Exception as e:
            logger.debug(
                f"Ошибка при попытке закрыть баннер с селектором {selector}: {e}"
            )

    logger.debug("Баннер с cookie не найден или уже закрыт")
    return False


# JavaScript-шаблоны для повторного использования
JS_CLICK_SEARCH_BUTTON = """
    // Функция для проверки, является ли кнопка кнопкой поиска, а не сброса
    function isSearchButton(btn) {
        const text = (btn.textContent || btn.value || '').toLowerCase();
        const id = (btn.id || '').toLowerCase();
        const className = (btn.className || '').toLowerCase();
        
        // Исключаем кнопки сброса
        if (text.includes('reset') || text.includes('clear') || 
            id.includes('reset') || className.includes('reset') ||
            btn.type === 'reset') {
            console.log('Исключена кнопка сброса:', btn);
            return false;
        }
        
        // Ищем кнопки поиска
        return text.includes('search') || text.includes('find') || 
               text.includes('rechercher') || text.includes('chercher') ||
               (btn.type === 'submit' && !text.includes('reset'));
    }

    // Функция для клика на кнопку с задержкой
    function clickWithDelay(button) {
        // Прокручиваем к кнопке
        button.scrollIntoView({behavior: 'smooth', block: 'center'});

        // Даем время на прокрутку
        setTimeout(() => {
            try {
                // Пробуем прямой клик
                button.click();
                console.log('Clicked search button via JS:', button);
            } catch (e) {
                console.error('Error clicking button:', e);

                // Если прямой клик не сработал, пробуем отправить форму
                try {
                    const form = button.closest('form');
                    if (form) {
                        form.submit();
                        console.log('Form submitted via JS');
                    }
                } catch (formError) {
                    console.error('Error submitting form:', formError);
                }
            }
        }, 500);
    }

    // Ищем все потенциальные кнопки
    const allButtons = Array.from(document.querySelectorAll('button, input[type="submit"], input[type="button"]'));
    console.log('Найдено всего кнопок:', allButtons.length);
    
    // Фильтруем кнопки поиска (исключаем кнопки сброса)
    const searchButtons = allButtons.filter(isSearchButton);
    console.log('Найдено кнопок поиска после фильтрации:', searchButtons.length);
    
    let buttonFound = false;
    
    if (searchButtons.length > 0) {
        // Если несколько кнопок поиска, выбираем самую правую
        let targetButton = searchButtons[0];
        if (searchButtons.length > 1) {
            console.log('Найдено несколько кнопок поиска, выбираем самую правую');
            targetButton = searchButtons.reduce((rightmost, current) => {
                const rightmostRect = rightmost.getBoundingClientRect();
                const currentRect = current.getBoundingClientRect();
                return currentRect.left > rightmostRect.left ? current : rightmost;
            });
        }
        
        console.log('Выбрана кнопка поиска:', targetButton.textContent || targetButton.value, 
                   'Position:', targetButton.getBoundingClientRect());
        
        // Проверяем, что кнопка видима и активна
        if (targetButton.offsetParent !== null && !targetButton.disabled) {
            clickWithDelay(targetButton);
            buttonFound = true;
        }
    }

    // Если кнопки поиска не найдены, пробуем найти кнопку справа от кнопки сброса
    if (!buttonFound) {
        console.log('Кнопки поиска не найдены, ищем кнопку справа от кнопки сброса');
        const resetButtons = Array.from(document.querySelectorAll('button[type="reset"], input[type="reset"], button:contains("Reset"), input[value*="Reset"]'));
        
        if (resetButtons.length > 0) {
            const resetButton = resetButtons[0];
            const resetRect = resetButton.getBoundingClientRect();

            // Ищем все кнопки справа от кнопки сброса
            const rightButtons = allButtons.filter(btn => {
                const rect = btn.getBoundingClientRect();
                return rect.left > resetRect.right && btn.offsetParent !== null && 
                       btn !== resetButton && !isResetButton(btn);
            });

            if (rightButtons.length > 0) {
                console.log('Найдена кнопка справа от кнопки сброса');
                clickWithDelay(rightButtons[0]);
                buttonFound = true;
            }
        }
    }

    // Если кнопка все еще не найдена, пробуем отправить первую форму
    if (!buttonFound) {
        const forms = document.querySelectorAll('form');
        if (forms.length > 0) {
            forms[0].submit();
            console.log('First form submitted as fallback');
        }
    }
    
    // Функция для проверки, является ли кнопка кнопкой сброса
    function isResetButton(btn) {
        const text = (btn.textContent || btn.value || '').toLowerCase();
        const id = (btn.id || '').toLowerCase();
        const className = (btn.className || '').toLowerCase();
        
        return text.includes('reset') || text.includes('clear') || 
               id.includes('reset') || className.includes('reset') ||
               btn.type === 'reset';
    }
"""

JS_SUBMIT_FORM = """
    const forms = document.querySelectorAll('form');
    if (forms.length > 0) {
        forms[0].submit();
        console.log('Form submitted directly');
    }
"""


async def find_search_button(page: Page) -> Optional[Locator]:
    """Находит кнопку поиска на странице.

    Args:
        page: Объект страницы Playwright

    Returns:
        Optional[Locator]: Локатор кнопки поиска или None, если кнопка не найдена
    """
    try:
        # Ищем кнопку по различным селекторам
        selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "button.btn-search",
            "button.search-button",
            "button:has-text('Search')",
            "button:has-text('Rechercher')",
        ]
        for selector in selectors:
            button = page.locator(selector).first
            if await button.count() > 0:
                logger.debug(f"Найдена кнопка поиска по селектору: {selector}")
                await button.scroll_into_view_if_needed()
                return button
        logger.debug("Кнопка поиска не найдена")
        return None
    except Exception as e:
        logger.error(f"Ошибка при поиске кнопки: {e}")
        return None


@precondition(
    lambda page: isinstance(page, Page) and not page.is_closed(),
    "page должен быть экземпляром Page и не должен быть закрыт",
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, bool),
    "Результат должен быть булевым значением",
)
async def submit_form(page: Page) -> bool:
    """Отправляет форму напрямую.

    Args:
        page: Объект страницы Playwright

    Returns:
        bool: True, если форма успешно отправлена, False в случае ошибки

    Предусловия:
        - page должен быть экземпляром Page
        - page не должен быть закрыт

    Постусловия:
        - Результат должен быть булевым значением
    """
    logger.debug("Пробуем отправить форму напрямую...")

    # Проверка предусловия внутри функции
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    try:
        form = page.locator("form").first
        count = await form.count()
        if count > 0:
            logger.debug("Форма найдена, отправляем...")

            # Используем expect_navigation для ожидания перехода после отправки формы
            async with page.expect_navigation(timeout=30000):
                await form.evaluate("form => form.submit()")

            logger.debug("Форма успешно отправлена")
            return True
        else:
            logger.warning("Форма не найдена")
            return False
    except Exception as e:
        logger.error(f"Ошибка при отправке формы: {e}")
        return False


async def analyze_page_structure(page: Page) -> None:
    """Анализирует структуру страницы и сохраняет информацию для отладки.

    Args:
        page: Объект страницы Playwright
    """
    logger.debug("Анализируем структуру страницы...")

    # Сохраняем скриншот всей страницы
    screenshot_path = f"{manage_debug_files('page_structure')}.png"
    await page.screenshot(path=screenshot_path, full_page=True)
    logger.debug(f"Сохранен скриншот всей страницы: {screenshot_path}")

    # Сохраняем HTML страницы
    html_path = f"{manage_debug_files('page_structure')}.html"
    html_content = await page.content()
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_content)
    logger.debug(f"Сохранен HTML страницы: {html_path}")

    # Анализируем формы на странице
    forms_info = await page.evaluate(
        """
    () => {
        const forms = document.querySelectorAll('form');
        const formsInfo = [];

        for (let i = 0; i < forms.length; i++) {
            const form = forms[i];
            const formInfo = {
                id: form.id || `form_${i}`,
                action: form.action,
                method: form.method,
                elements: []
            };

            // Анализируем элементы формы
            const elements = form.elements;
            for (let j = 0; j < elements.length; j++) {
                const element = elements[j];
                formInfo.elements.push({
                    name: element.name,
                    id: element.id,
                    type: element.type,
                    value: element.value,
                    isVisible: element.offsetParent !== null
                });
            }

            // Находим кнопки в форме
            const buttons = form.querySelectorAll('button, input[type="submit"], input[type="button"]');
            formInfo.buttons = [];
            for (let k = 0; k < buttons.length; k++) {
                const button = buttons[k];
                const rect = button.getBoundingClientRect();
                formInfo.buttons.push({
                    type: button.type,
                    text: button.textContent || button.value || '',
                    id: button.id,
                    name: button.name,
                    isVisible: button.offsetParent !== null,
                    position: {
                        x: rect.x,
                        y: rect.y,
                        width: rect.width,
                        height: rect.height
                    }
                });
            }

            formsInfo.push(formInfo);
        }

        return formsInfo;
    }
    """
    )

    # Анализируем капчу на странице
    captcha_info = await page.evaluate(
        """
    () => {
        const captchaInfo = {
            recaptchaDiv: null,
            recaptchaIframes: [],
            recaptchaResponse: null
        };

        // Проверяем наличие div с классом g-recaptcha
        const recaptchaDiv = document.querySelector('.g-recaptcha');
        if (recaptchaDiv) {
            captchaInfo.recaptchaDiv = {
                sitekey: recaptchaDiv.getAttribute('data-sitekey'),
                size: recaptchaDiv.getAttribute('data-size'),
                theme: recaptchaDiv.getAttribute('data-theme'),
                position: recaptchaDiv.getBoundingClientRect()
            };
        }

        // Проверяем наличие iframe с reCAPTCHA
        const recaptchaIframes = document.querySelectorAll('iframe[src*="recaptcha"]');
        for (let i = 0; i < recaptchaIframes.length; i++) {
            const iframe = recaptchaIframes[i];
            captchaInfo.recaptchaIframes.push({
                src: iframe.src,
                position: iframe.getBoundingClientRect(),
                isVisible: iframe.offsetParent !== null
            });
        }

        // Проверяем наличие поля g-recaptcha-response
        const recaptchaResponse = document.querySelector('#g-recaptcha-response');
        if (recaptchaResponse) {
            captchaInfo.recaptchaResponse = {
                value: recaptchaResponse.value ? recaptchaResponse.value.substring(0, 20) + '...' : '',
                length: recaptchaResponse.value ? recaptchaResponse.value.length : 0
            };
        }

        return captchaInfo;
    }
    """
    )

    # Анализируем кнопки на странице
    buttons_info = await page.evaluate(
        """
    () => {
        const allButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"], input[type="reset"]');
        const buttonsInfo = [];

        for (let i = 0; i < allButtons.length; i++) {
            const button = allButtons[i];
            const rect = button.getBoundingClientRect();

            buttonsInfo.push({
                index: i,
                type: button.type,
                text: button.textContent || button.value || '',
                id: button.id,
                name: button.name,
                classes: button.className,
                isVisible: button.offsetParent !== null,
                isEnabled: !button.disabled,
                position: {
                    x: rect.x,
                    y: rect.y,
                    width: rect.width,
                    height: rect.height,
                    inViewport: (
                        rect.top >= 0 &&
                        rect.left >= 0 &&
                        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                    )
                }
            });
        }

        return buttonsInfo;
    }
    """
    )

    # Записываем информацию в лог
    logger.debug(f"Найдено {len(forms_info)} форм на странице")
    for i, form in enumerate(forms_info):
        logger.debug(
            f"Форма #{i+1}: id={form['id']}, action={form['action']}, method={form['method']}"
        )
        logger.debug(f"  Элементов в форме: {len(form['elements'])}")
        logger.debug(f"  Кнопок в форме: {len(form['buttons'])}")

        # Выводим информацию о кнопках в форме
        for j, button in enumerate(form["buttons"]):
            logger.debug(
                f"    Кнопка #{j+1}: type={button['type']}, text='{button['text']}', visible={button['isVisible']}"
            )
            logger.debug(
                f"      Позиция: x={button['position']['x']}, y={button['position']['y']}"
            )

    # Выводим информацию о капче
    if captcha_info["recaptchaDiv"]:
        logger.debug(
            f"Найден div с reCAPTCHA: sitekey={captcha_info['recaptchaDiv']['sitekey']}, size={captcha_info['recaptchaDiv']['size']}"
        )
    else:
        logger.debug("Div с reCAPTCHA не найден")

    logger.debug(f"Найдено {len(captcha_info['recaptchaIframes'])} iframe с reCAPTCHA")

    if captcha_info["recaptchaResponse"]:
        logger.debug(
            f"Найдено поле g-recaptcha-response: длина={captcha_info['recaptchaResponse']['length']}"
        )
    else:
        logger.debug("Поле g-recaptcha-response не найдено")

    # Выводим информацию о кнопках на странице
    logger.debug(f"Найдено {len(buttons_info)} кнопок на странице")
    search_buttons = [
        b
        for b in buttons_info
        if "search" in b["text"].lower() and "reset" not in b["text"].lower()
    ]
    logger.debug(f"Из них кнопок поиска: {len(search_buttons)}")

    for i, button in enumerate(search_buttons):
        logger.debug(
            f"Кнопка поиска #{i+1}: type={button['type']}, text='{button['text']}', visible={button['isVisible']}, enabled={button['isEnabled']}"
        )
        logger.debug(
            f"  Позиция: x={button['position']['x']}, y={button['position']['y']}, в видимой области={button['position']['inViewport']}"
        )

    # Сохраняем информацию в файл для дальнейшего анализа
    debug_info = {"forms": forms_info, "captcha": captcha_info, "buttons": buttons_info}

    debug_path = f"{manage_debug_files('page_structure')}.json"
    with open(debug_path, "w", encoding="utf-8") as f:
        json.dump(debug_info, f, indent=2, ensure_ascii=False)

    logger.debug(f"Сохранена подробная информация о структуре страницы: {debug_path}")


async def click_search_button(page: Page) -> bool:
    """Надежный клик на кнопку поиска с использованием различных методов.

    Args:
        page: Объект страницы Playwright

    Returns:
        bool: True, если клик был успешным, иначе False
    """
    logger.info("[ЛОГ] Начинаем процесс нажатия кнопки поиска...")

    # Анализируем структуру страницы перед кликом
    await analyze_page_structure(page)

    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    if args and args.debug:
        screenshot_path = f"{manage_debug_files('before_search_click')}.png"
        await page.screenshot(path=screenshot_path)
        logger.debug(
            f"Сделан скриншот перед кликом на кнопку поиска: {screenshot_path}"
        )

    # Расширенный список селекторов для кнопки поиска
    search_button_selectors = [
        "button[type='submit']:has-text('Search')",
        "button:has-text('Search'):not(:has-text('Reset'))",
        "input[type='submit'][value*='Search']",
        "button.search-button",
        "button.btn-primary >> text=Search",
        "button:has-text('Rechercher')",  # Французский вариант
        "input[type='submit'][value*='Rechercher']",  # Французский вариант
        "button[type='submit']",  # Любая кнопка submit
        "input[type='submit']",  # Любой input submit
        "button:has-text('Find')",  # Альтернативный текст
        "button:has-text('Find a CPA')",  # Полный текст
        "button:has-text('Trouver')",  # Французский вариант Find
        "button:has-text('Trouver un CPA')",  # Французский вариант Find a CPA
    ]

    logger.info(
        f"[ЛОГ] Проверяем {len(search_button_selectors)} селекторов кнопки поиска..."
    )

    for selector in search_button_selectors:
        try:
            button = page.locator(selector).first
            if await button.count() > 0:
                logger.info(f"[ЛОГ] Найдена кнопка с селектором: {selector}")
                # Прокручиваем к кнопке и ждем
                await button.scroll_into_view_if_needed()
                await page.wait_for_timeout(2000)

                # Проверяем состояние кнопки
                is_visible = await button.is_visible()
                is_enabled = await button.is_enabled()
                is_hidden = await button.is_hidden()
                logger.info(
                    f"[ЛОГ] Состояние кнопки: visible={is_visible}, enabled={is_enabled}, hidden={is_hidden}"
                )

                if is_visible and is_enabled and not is_hidden:
                    logger.info(
                        f"[ЛОГ] Пытаемся кликнуть на кнопку с селектором: {selector}"
                    )
                    # Пробуем разные методы клика
                    try:
                        # 1. Стандартный клик
                        logger.info("[ЛОГ] Пробуем стандартный клик Playwright")
                        await button.click(delay=100, timeout=5000)
                        logger.info("[ЛОГ] Стандартный клик Playwright успешен")
                        return True
                    except Exception as click_error:
                        logger.warning(
                            f"[ЛОГ] Стандартный клик не удался: {click_error}"
                        )
                        try:
                            # 2. Принудительный клик
                            logger.info("[ЛОГ] Пробуем принудительный клик")
                            await button.click(force=True, timeout=5000)
                            logger.info("[ЛОГ] Принудительный клик успешен")
                            return True
                        except Exception as force_click_error:
                            logger.warning(
                                f"[ЛОГ] Принудительный клик не удался: {force_click_error}"
                            )
                            try:
                                # 3. JavaScript клик
                                logger.info("[ЛОГ] Пробуем JavaScript клик")
                                await page.evaluate(
                                    """
                                    (selector) => {
                                        const button = document.querySelector(selector);
                                        if (button) {
                                            button.click();
                                            return true;
                                        }
                                        return false;
                                    }
                                """,
                                    selector,
                                )
                                logger.info("[ЛОГ] JavaScript клик выполнен")
                                await page.wait_for_timeout(3000)
                                return True
                            except Exception as js_click_error:
                                logger.error(
                                    f"[ЛОГ] JavaScript клик не удался: {js_click_error}"
                                )
                                # Если JavaScript клик не сработал, пробуем отправить форму
                                try:
                                    form = page.locator("form").first
                                    if await form.count() > 0:
                                        await form.evaluate("form => form.submit()")
                                        logger.info("[ЛОГ] Форма отправлена через JavaScript")
                                        return True
                                except Exception as e_submit:
                                    logger.error(f"[ЛОГ] Ошибка при попытке отправки формы: {e_submit}")
            logger.error(f"[ЛОГ] Ошибка при обработке селектора {selector}: {e}")
            continue

    logger.warning(
        "[ЛОГ] Не удалось найти или кликнуть кнопку поиска стандартными методами"
    )
    return False


async def wait_for_captcha_solution(page: Page, max_wait_seconds: int = 180) -> bool:
    """Ожидает решения капчи с улучшенной проверкой состояния.

    Args:
        page: Объект страницы Playwright
        max_wait_seconds: Максимальное время ожидания в секундах

    Returns:
        bool: True если капча решена, False в противном случае
    """
    logger.info("Ожидание решения капчи...")
    start_time = time.time()
    consecutive_solved_checks = 0
    required_checks = 2  # Требуемое количество последовательных проверок

    while time.time() - start_time < max_wait_seconds:
        try:
            # Проверяем наличие токена
            token = await page.evaluate(
                """
                () => {
                    const response = document.querySelector('#g-recaptcha-response');
                    return response ? response.value : null;
                }
            """
            )

            if token and len(token) > 50:
                consecutive_solved_checks += 1
                logger.debug(
                    f"Найден токен капчи (проверка {consecutive_solved_checks}/{required_checks})"
                )

                if consecutive_solved_checks >= required_checks:
                    logger.info("Капча успешно решена")
                    return True
            else:
                consecutive_solved_checks = 0

            # Проверяем состояние кнопки поиска
            search_button_enabled = await page.evaluate(
                """
                () => {
                    const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                    for (const btn of buttons) {
                        if (!btn.disabled) return true;
                    }
                    return false;
                }
            """
            )

            if search_button_enabled:
                logger.info("Кнопка поиска активна - капча решена")
                return True

            # Ждем перед следующей проверкой
            await page.wait_for_timeout(2000)  # 2 секунды между проверками

        except Exception as e:
            logger.error(f"Ошибка при проверке состояния капчи: {e}")
            await page.wait_for_timeout(2000)

    logger.error("Время ожидания решения капчи истекло")
    return False


async def solve_captcha(page: Page) -> bool:
    """Решение reCAPTCHA v2 с помощью Anti‑Captcha, вставка токена.

    Args:
        page: Объект страницы Playwright

    Returns:
        bool: True, если капча успешно решена или не требуется, False в случае ошибки

    Предусловия:
        - page должен быть экземпляром Page
        - page не должен быть закрыт

    Постусловия:
        - Результат должен быть булевым значением
    """
    logger.info("===== НАЧАЛО РЕШЕНИЯ КАПЧИ =====")

    # Проверка предусловия внутри функции
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    # Перезагружаем ключ из .env на случай, если он был изменен
    load_dotenv()
    global ANTICAPTCHA_KEY
    ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")

    if ANTICAPTCHA_KEY:
        logger.debug(f"ANTICAPTCHA_KEY: {ANTICAPTCHA_KEY[:5]}...{ANTICAPTCHA_KEY[-5:]}")
    else:
        logger.debug("ANTICAPTCHA_KEY не установлен")

    # Делаем скриншот для отладки
    if args and args.debug:
        screenshot_path = f"{manage_debug_files('before_captcha')}.png"
        await page.screenshot(path=screenshot_path)
        logger.debug(f"Сделан скриншот перед решением капчи: {screenshot_path}")

    # Проверяем наличие элементов reCAPTCHA на странице
    recaptcha_iframe_count = await page.locator("iframe[src*='api2/anchor']").count()
    recaptcha_div_count = await page.locator("div.g-recaptcha").count()

    logger.debug(
        f"Найдено iframe с reCAPTCHA: {recaptcha_iframe_count}, div.g-recaptcha: {recaptcha_div_count}"
    )

    if recaptcha_iframe_count == 0 and recaptcha_div_count == 0:
        logger.info("reCAPTCHA не обнаружена на странице - капча не требуется")
        return True

    # Проверяем, может быть капча уже решена
    captcha_solved = await is_captcha_solved(page)
    if captcha_solved:
        logger.info("Капча уже решена, пропускаем решение")
        return True
    else:
        logger.info("Капча не решена, начинаем решение")

    if not ANTICAPTCHA_KEY:
        logger.warning(
            "ANTICAPTCHA_API_KEY not set – solve manually or use --no-captcha"
        )
        input("Решите капчу вручную и нажмите Enter для продолжения...")

        # Проверяем, решил ли пользователь капчу
        if await is_captcha_solved(page):
            logger.info("Капча решена пользователем вручную")
            return True
        else:
            logger.warning("Капча не решена даже после ручного ввода")
            return False

    # Проверяем наличие и извлекаем sitekey reCAPTCHA
    success, site_key = await extract_recaptcha_sitekey(page)
    if not success or not site_key:
        logger.error("Не удалось извлечь sitekey reCAPTCHA")

        # Пробуем найти sitekey через JavaScript
        try:
            site_key = await page.evaluate(
                """
            () => {
                // Ищем в div.g-recaptcha
                const recaptchaDiv = document.querySelector('.g-recaptcha');
                if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                    return recaptchaDiv.getAttribute('data-sitekey');
                }

                // Ищем в форме
                const form = document.querySelector('form[data-sitekey]');
                if (form && form.getAttribute('data-sitekey')) {
                    return form.getAttribute('data-sitekey');
                }

                // Ищем в любом элементе с атрибутом data-sitekey
                const anyElement = document.querySelector('[data-sitekey]');
                if (anyElement) {
                    return anyElement.getAttribute('data-sitekey');
                }

                return null;
            }
            """
            )

            if site_key:
                logger.debug(f"Найден sitekey через JavaScript: {site_key[:10]}...")
                success = True
            else:
                logger.error("Не удалось найти sitekey даже через JavaScript")
                return False
        except Exception as js_error:
            logger.error(f"Ошибка при поиске sitekey через JavaScript: {js_error}")
            return False

    logger.info(f"Извлечен sitekey: {site_key[:10]}...")

    # Проверяем тип reCAPTCHA (обычная или Invisible)
    is_invisible = await page.evaluate(
        """
        () => {
            // Проверяем наличие атрибутов, указывающих на Invisible reCAPTCHA
            const recaptchaDiv = document.querySelector('.g-recaptcha');
            if (recaptchaDiv) {
                const dataSize = recaptchaDiv.getAttribute('data-size');
                return dataSize === 'invisible';
            }

            // Проверяем наличие атрибута data-size='invisible' в любом элементе
            const invisibleElement = document.querySelector('[data-size="invisible"]');
            if (invisibleElement) {
                return true;
            }

            return false;
        }
    """
    )

    if is_invisible:
        logger.info("Обнаружена Invisible reCAPTCHA - пропускаем клик на чекбокс")
    else:
        # Для обычной reCAPTCHA пробуем кликнуть на чекбокс
        checkbox_checked = await is_recaptcha_checkbox_checked(page)
        if not checkbox_checked:
            logger.debug("Чекбокс reCAPTCHA не отмечен, пробуем кликнуть")
            await click_recaptcha_checkbox(page)

            # Ждем немного и проверяем, решилась ли капча автоматически
            await page.wait_for_timeout(5000)
            if await is_captcha_solved(page):
                logger.info("Капча решена автоматически после клика на чекбокс")
                return True
        else:
            logger.debug("Чекбокс reCAPTCHA уже отмечен")

            # Проверяем, может быть капча уже решена
            if await is_captcha_solved(page):
                logger.info("Капча уже решена")
                return True

    # Если предыдущие методы не сработали, используем Anti-Captcha
    try:
        logger.info("Начинаем решение капчи через Anti-Captcha...")

        # Создаем задачу в Anti-Captcha
        success, task_id = await create_captcha_task(site_key, BASE_URL)
        if not success or task_id is None:
            logger.error("Не удалось создать задачу в Anti-Captcha")
            return False

        logger.info(f"Создана задача в Anti-Captcha, ID: {task_id}")

        # Ожидаем результат решения капчи
        success, token = await get_captcha_result(task_id)
        if not success or token is None:
            logger.error("Не удалось получить токен решения капчи")
            return False

        # Проверяем валидность токена
        if len(token) < 50:
            logger.error(
                f"Получен слишком короткий токен от Anti-Captcha: {len(token)} символов. Токен должен быть длиннее 50 символов."
            )
            return False

        # Проверяем, что токен имеет правильный формат (начинается с '03')
        if not token.startswith("03"):
            logger.warning(
                f"Токен имеет необычный формат (не начинается с '03'): {token[:5]}..."
            )

        # Делаем скриншот перед вставкой токена
        if args and args.debug:
            screenshot_path = f"{manage_debug_files('before_token_insert')}.png"
            await page.screenshot(path=screenshot_path)
            logger.debug(f"Сделан скриншот перед вставкой токена: {screenshot_path}")

        # Проверяем, есть ли поле для вставки токена
        has_response_field = await page.evaluate(
            """
        () => {
            const response = document.querySelector('#g-recaptcha-response');
            return response !== null;
        }
        """
        )

        if not has_response_field:
            logger.warning(
                "Поле #g-recaptcha-response не найдено перед вставкой токена"
            )

            # Проверяем наличие div.g-recaptcha
            has_recaptcha_div = await page.evaluate(
                """
            () => {
                const div = document.querySelector('.g-recaptcha');
                return div !== null;
            }
            """
            )

            if not has_recaptcha_div:
                logger.error("Не найден div.g-recaptcha на странице")
                # Делаем скриншот для отладки
                if args and args.debug:
                    screenshot_path = f"{manage_debug_files('no_recaptcha_div')}.png"
                    await page.screenshot(path=screenshot_path)
                    logger.debug(f"Сделан скриншот: {screenshot_path}")

                    # Сохраняем HTML для анализа
                    html_path = f"{manage_debug_files('no_recaptcha_div')}.html"
                    html_content = await page.content()
                    with open(html_path, "w", encoding="utf-8") as f:
                        f.write(html_content)
                    logger.debug(f"Сохранен HTML страницы: {html_path}")

                # Пробуем найти любые элементы, связанные с reCAPTCHA
                recaptcha_elements = await page.evaluate(
                    """
                () => {
                    return {
                        iframes: document.querySelectorAll('iframe[src*="recaptcha"]').length,
                        divs: document.querySelectorAll('div[class*="recaptcha"], div[id*="recaptcha"]').length,
                        scripts: document.querySelectorAll('script[src*="recaptcha"]').length
                    };
                }
                """
                )

                logger.debug(f"Найдены элементы reCAPTCHA: {recaptcha_elements}")

                if (
                    recaptcha_elements["iframes"] == 0
                    and recaptcha_elements["divs"] == 0
                ):
                    logger.error("Не найдены элементы reCAPTCHA на странице")
                    return False

        # Вставляем токен и вызываем callback
        logger.info(
            f"Получен токен решения капчи длиной {len(token)} символов, вставляем его..."
        )
        if not await insert_captcha_token(page, token):
            logger.error("Не удалось вставить токен решения капчи")
            return False

        # Делаем скриншот после вставки токена
        if args and args.debug:
            screenshot_path = f"{manage_debug_files('after_token_insert')}.png"
            await page.screenshot(path=screenshot_path)
            logger.debug(f"Сделан скриншот после вставки токена: {screenshot_path}")

            # Проверяем, что токен действительно вставлен
            token_check = await page.evaluate(
                """
            () => {
                const response = document.querySelector('#g-recaptcha-response');
                return response ? response.value : null;
            }
            """
            )

            if token_check:
                logger.debug(
                    f"Токен успешно вставлен в поле g-recaptcha-response, длина: {len(token_check)}"
                )
            else:
                logger.error(
                    "Токен не найден в поле g-recaptcha-response после вставки"
                )

                # Пробуем вставить токен еще раз, используя другой метод
                logger.debug(
                    "Пробуем вставить токен еще раз, используя прямой JavaScript"
                )
                await page.evaluate(
                    """
                (token) => {
                    // Создаем поле g-recaptcha-response, если его нет
                    let response = document.querySelector('#g-recaptcha-response');
                    if (!response) {
                        console.log('Creating g-recaptcha-response field');
                        response = document.createElement('textarea');
                        response.id = 'g-recaptcha-response';
                        response.name = 'g-recaptcha-response';
                        response.className = 'g-recaptcha-response';
                        response.style.display = 'none';
                        document.body.appendChild(response);
                    }

                    // Вставляем токен
                    response.value = token;
                    console.log('Token inserted, length:', token.length);

                    // Вызываем событие изменения
                    const event = new Event('change', { bubbles: true });
                    response.dispatchEvent(event);
                }
                """,
                    token,
                )

                # Проверяем еще раз
                token_check = await page.evaluate(
                    """
                () => {
                    const response = document.querySelector('#g-recaptcha-response');
                    return response ? response.value : null;
                }
                """
                )

                if token_check:
                    logger.debug(
                        f"Токен успешно вставлен при повторной попытке, длина: {len(token_check)}"
                    )
                else:
                    logger.error(
                        "Токен не найден в поле g-recaptcha-response даже после повторной вставки"
                    )

        # Ждем, пока капча будет решена
        if not await wait_for_captcha_solution(page, max_wait_seconds=60):
            logger.error("Капча не решена даже после вставки токена")

            # Анализируем состояние капчи
            captcha_state = await page.evaluate(
                """
            () => {
                return {
                    hasToken: document.querySelector('#g-recaptcha-response') &&
                              document.querySelector('#g-recaptcha-response').value &&
                              document.querySelector('#g-recaptcha-response').value.length > 50,
                    hasCallback: typeof window.grecaptcha !== 'undefined' &&
                                 typeof window.___grecaptcha_cfg !== 'undefined',
                    hasIframe: document.querySelector('iframe[src*="recaptcha"]') !== null,
                    buttonDisabled: Array.from(document.querySelectorAll('button[type="submit"]')).some(btn => btn.disabled)
                };
            }
            """
            )

            logger.debug(f"Состояние капчи: {captcha_state}")

            # Если токен есть, но кнопка все еще отключена, пробуем активировать ее
            if captcha_state.get("hasToken") and captcha_state.get("buttonDisabled"):
                logger.debug(
                    "Токен есть, но кнопка отключена. Пробуем активировать кнопку..."
                )
                await page.evaluate(
                    """
                () => {
                    const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                    for (const btn of buttons) {
                        btn.disabled = false;
                        btn.removeAttribute('disabled');
                        console.log('Enabled button:', btn);
                    }
                }
                """
                )

                # Проверяем еще раз
                button_enabled = await page.evaluate(
                    """
                () => {
                    const buttons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
                    return Array.from(buttons).every(btn => !btn.disabled);
                }
                """
                )

                if button_enabled:
                    logger.debug("Кнопки успешно активированы")
                else:
                    logger.error("Не удалось активировать кнопки")

            # Делаем скриншот для отладки
            if args and args.debug:
                screenshot_path = f"{manage_debug_files('captcha_timeout')}.png"
                await page.screenshot(path=screenshot_path)
                logger.debug(f"Сделан скриншот ошибки: {screenshot_path}")

            # Последняя попытка - принудительно активировать кнопку через JS
            logger.debug("Пробуем принудительно активировать кнопку поиска...")
            await page.evaluate(
                """
                const buttons = document.querySelectorAll('form button[type="submit"], form input[type="submit"]');
                for (const btn of buttons) {
                    btn.disabled = false;
                    btn.removeAttribute('disabled');
                    console.log('Forced enabled button:', btn);
                }
            """
            )

            # Проверяем еще раз
            await page.wait_for_timeout(5000)
            if await is_captcha_solved(page):
                logger.info("Капча решена после принудительной активации кнопки")
                return True
            else:
                logger.error("Не удалось решить капчу даже после всех попыток")
                return False

        logger.info("Капча успешно решена")

        # Делаем скриншот после решения капчи
        if args and args.debug:
            screenshot_path = f"{manage_debug_files('captcha_solved')}.png"
            await page.screenshot(path=screenshot_path)
            logger.debug(f"Сделан скриншот после решения капчи: {screenshot_path}")

        logger.debug("===== ЗАВЕРШЕНИЕ РЕШЕНИЯ КАПЧИ =====")
        return True

    except Exception as e:
        logger.error(f"Ошибка при решении капчи: {e}")
        # Делаем скриншот для отладки
        if args and args.debug:
            screenshot_path = f"{manage_debug_files('captcha_error')}.png"
            await page.screenshot(path=screenshot_path)
            logger.debug(f"Сделан скриншот ошибки: {screenshot_path}")
        logger.debug("===== ЗАВЕРШЕНИЕ РЕШЕНИЯ КАПЧИ С ОШИБКОЙ =====")
        return False


# ---------------------------------------------------------------------------
# Core parsing routines
# ---------------------------------------------------------------------------


async def collect_result_cards(page: Page) -> Tuple[bool, Optional[Locator], int]:
    """Ищет карточки результатов поиска на странице, используя различные селекторы.

    Args:
        page: Объект страницы Playwright

    Returns:
        Tuple[bool, Optional[Locator], int]:
            - Успех поиска
            - Локатор с карточками результатов (или None, если не найдены)
            - Количество найденных карточек

    Предусловия:
        - page должен быть экземпляром Page
        - page не должен быть закрыт

    Постусловия:
        - Результат должен быть кортежем (bool, Optional[Locator], int)
        - Третий элемент кортежа (количество) должен быть неотрицательным целым числом
    """
    logger.debug("Ищем карточки результатов поиска...")

    # Проверка предусловия внутри функции
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"

    # Пробуем разные селекторы для результатов поиска
    result_selectors = [
        ".search-result",
        ".result-item",
        ".result",
        ".cpa-result",
        ".directory-result",
        "article",
        ".card",
        "div.listing-item",
        "div.directory-item",
        "div.member",
        "div.profile",
        "li.result",
        "tr",  # Возможно, результаты в таблице
        "div > h3",  # Заголовки могут быть частью результатов
        "div.row",  # Строки в сетке Bootstrap
    ]

    found_results = False
    cards = None
    count = 0

    for selector in result_selectors:
        logger.debug(f"Пробуем селектор результатов: {selector}")
        try:
            elements = page.locator(selector)
            count = await elements.count()

            if count > 0:
                logger.debug(f"Найдено {count} элементов с селектором: {selector}")

                # Проверяем, что это действительно результаты поиска
                # Берем первый элемент и проверяем, есть ли в нем текст
                first_element = elements.first
                text = await first_element.inner_text()

                if text.strip():
                    logger.debug(f"Текст первого элемента: {text[:50]}...")

                    # Проверяем, что это не пустые результаты
                    if "0 of 0 result" in text or "no results" in text.lower():
                        logger.info("Найдено сообщение о пустых результатах поиска")
                        return False, None, 0  # Пустые результаты

                    cards = elements
                    found_results = True
                    break
                else:
                    logger.debug(f"Элемент с селектором {selector} не содержит текста")
        except Exception as e:
            logger.debug(f"Ошибка при проверке селектора {selector}: {e}")

    if not found_results:
        # Последняя попытка - ищем любые элементы, которые могут быть результатами
        logger.debug("Пробуем найти результаты по общим селекторам...")

        # Получаем HTML страницы для анализа
        html = await page.content()
        logger.debug(f"Длина HTML страницы: {len(html)} символов")

        # Сохраняем HTML для анализа
        if args.debug:
            html_path = f"{manage_debug_files('results_page')}.html"
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html)
            logger.debug(f"Сохранен HTML страницы: {html_path}")

        # Проверяем, есть ли сообщение о пустых результатах
        logger.debug("Проверяем наличие сообщения о пустых результатах...")
        has_no_results = await page.evaluate(
            """
            () => {
                const pageText = document.body.innerText.toLowerCase();
                const noResultsIndicators = [
                    '0 of 0 result', 'no results', 'no result found',
                    '0 result', 'nothing found', 'no matches'
                ];

                for (const indicator of noResultsIndicators) {
                    if (pageText.includes(indicator)) {
                        return true;
                    }
                }

                return false;
            }
        """
        )

        if has_no_results:
            logger.info("Найдено сообщение о пустых результатах поиска")
            return False, None, 0  # Пустые результаты

        # Пробуем найти результаты с помощью JavaScript
        logger.debug("Пробуем найти результаты с помощью JavaScript...")
        js_results = await page.evaluate(
            """
            () => {
                // Ищем все div, которые могут содержать результаты
                const divs = Array.from(document.querySelectorAll('div'));

                // Фильтруем div, которые содержат хотя бы один заголовок и ссылку
                const possibleResults = divs.filter(div => {
                    const hasHeading = div.querySelector('h1, h2, h3, h4, h5, h6, strong');
                    const hasLink = div.querySelector('a');
                    // Исключаем элементы навигации и меню
                    const isNavigation = div.closest('nav, header, footer') !== null;
                    // Проверяем, что текст не содержит индикаторов пустых результатов
                    const text = div.innerText.toLowerCase();
                    const hasNoResultsText = text.includes('0 of 0 result') ||
                                            text.includes('no results') ||
                                            text.includes('no result found');

                    return hasHeading && hasLink && !isNavigation && !hasNoResultsText;
                });

                return possibleResults.length;
            }
        """
        )

        logger.debug(f"Найдено {js_results} возможных результатов с помощью JavaScript")

        if js_results > 0:
            logger.debug("Используем результаты, найденные с помощью JavaScript")
            # Используем div, содержащие заголовки и ссылки
            cards = page.locator("div:has(h1, h2, h3, h4, h5, h6, strong):has(a)")
            count = await cards.count()
            found_results = True
        else:
            logger.error("Не удалось найти результаты поиска")
            return False, None, 0

    logger.debug(f"Найдено {count} карточек результатов")
    return found_results, cards, count


async def process_category(
    page: Page, category: str, get_details: bool
) -> List[Dict[str, Any]]:
    """Обрабатывает одну категорию: выбирает чекбокс, решает капчу, выполняет поиск и собирает результаты.

    Args:
        page: Объект страницы Playwright
        category: Название категории для обработки
        get_details: Флаг, указывающий, нужно ли собирать детальную информацию

    Returns:
        Список словарей с данными о найденных CPA

    Предусловия:
        - page должен быть экземпляром Page
        - page не должен быть закрыт
        - category должна быть непустой строкой
        - get_details должен быть булевым значением

    Постусловия:
        - Результат должен быть списком словарей
    """
    print(
        f"DEBUG: process_category вызвана с параметрами: category={category}, get_details={get_details}"
    )

    # Проверка предусловий вручную
    if not isinstance(page, Page):
        logger.error("page должен быть экземпляром Page")
        return []

    if page.is_closed():
        logger.error("page не должен быть закрыт")
        return []

    if not isinstance(category, str) or len(category) == 0:
        logger.error("category должна быть непустой строкой")
        return []

    if not isinstance(get_details, bool):
        logger.error("get_details должен быть булевым значением")
        return []
    logger.info(f"Processing category: {category}")
    logger.debug(f"===== НАЧАЛО ОБРАБОТКИ КАТЕГОРИИ: {category} =====")

    # Проверка предусловия внутри функции
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"
    assert (
        isinstance(category, str) and len(category) > 0
    ), "category должна быть непустой строкой"
    assert isinstance(get_details, bool), "get_details должен быть булевым значением"

    # Проверяем, нужно ли переходить на страницу
    current_url = page.url
    if not current_url.startswith(BASE_URL):
        logger.debug(f"Переходим на страницу {BASE_URL}...")
        try:
            await page.goto(BASE_URL, timeout=60000)
            logger.debug("Страница загружена")
        except Exception as e:
            logger.error(f"Ошибка при переходе на страницу: {e}")
            logger.debug("Пробуем еще раз с другими параметрами...")
            try:
                await page.goto(BASE_URL, timeout=60000, wait_until="domcontentloaded")
                logger.debug("Страница загружена (domcontentloaded)")
            except Exception as e2:
                logger.error(f"Повторная ошибка при переходе на страницу: {e2}")
                # Делаем скриншот для отладки
                if args.debug:
                    screenshot_path = f"{manage_debug_files('navigation_error')}.png"
                    await page.screenshot(path=screenshot_path)
                    logger.debug(f"Сделан скриншот ошибки: {screenshot_path}")
                raise
    else:
        logger.debug(f"Уже находимся на нужной странице: {current_url}")

    logger.debug("Закрываем баннер с cookie...")
    await close_cookies_banner(page)

    # ШАГ 1: Выбор категории (чекбокса)
    logger.debug("ШАГ 1: Выбор категории (чекбокса)...")

    # Сначала снимаем все чекбоксы категорий
    try:
        logger.debug("Снимаем все чекбоксы категорий...")
        await page.evaluate(
            """
            // Находим все чекбоксы на странице
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            // Снимаем все чекбоксы
            for (const checkbox of checkboxes) {
                if (checkbox.checked) {
                    checkbox.checked = false;
                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('Снят чекбокс:', checkbox);
                }
            }
        """
        )
        logger.debug("Все чекбоксы сняты")
        await page.wait_for_timeout(2000)  # Даем время на обработку изменений
    except Exception as e:
        logger.error(f"Ошибка при снятии чекбоксов: {e}")

    # Если категория не "All", устанавливаем соответствующий чекбокс
    if category != "All":
        logger.debug(f"Устанавливаем чекбокс для категории '{category}'...")

        # Делаем скриншот перед выбором категории
        if args.debug:
            screenshot_path = f"{manage_debug_files('before_category')}.png"
            await page.screenshot(path=screenshot_path)
            logger.debug(f"Сделан скриншот перед выбором категории: {screenshot_path}")

        try:
            # Сначала пробуем найти чекбокс по тексту метки
            logger.debug(f"Ищем чекбокс по тексту метки '{category}'...")

            # Используем более надежный способ поиска чекбокса
            checkbox_found = False

            # Метод 1: Поиск по тексту метки
            try:
                checkbox_label = page.locator(f"label:has-text('{category}')").first
                if await checkbox_label.is_visible(timeout=1000):
                    logger.debug(f"Найден лейбл для категории: {category}")

                    # Прокручиваем к лейблу
                    await checkbox_label.scroll_into_view_if_needed()
                    await page.wait_for_timeout(1000)  # Ждем завершения прокрутки

                    # Кликаем на лейбл
                    await checkbox_label.click(force=True)
                    logger.debug(f"Клик на лейбл категории выполнен")

                    # Ждем обновления DOM
                    await page.wait_for_timeout(3000)

                    checkbox_found = True
            except Exception as label_error:
                logger.debug(f"Ошибка при поиске по лейблу: {label_error}")

            # Метод 2: Поиск по get_by_label, если первый метод не сработал
            if not checkbox_found:
                try:
                    logger.debug(f"Пробуем найти чекбокс через get_by_label...")
                    checkbox = page.get_by_label(category, exact=True)

                    await checkbox.wait_for(state="attached", timeout=10000)
                    logger.debug(f"Чекбокс найден через get_by_label")

                    # Прокручиваем к чекбоксу
                    await checkbox.scroll_into_view_if_needed()
                    await page.wait_for_timeout(1000)  # Ждем завершения прокрутки

                    # Отмечаем чекбокс
                    await checkbox.check(force=True)
                    logger.debug(f"Чекбокс отмечен через check()")

                    # Ждем обновления DOM
                    await page.wait_for_timeout(3000)

                    # Проверяем, что чекбокс отмечен
                    is_checked = await checkbox.is_checked()
                    logger.debug(f"Проверка: чекбокс отмечен = {is_checked}")

                    if is_checked:
                        checkbox_found = True
                except Exception as check_error:
                    logger.debug(
                        f"Ошибка при использовании get_by_label: {check_error}"
                    )

            # Метод 3: Поиск по ID или имени, если предыдущие методы не сработали
            if not checkbox_found:
                try:
                    logger.debug(f"Пробуем найти чекбокс по ID или имени...")
                    checkboxes = page.locator("input[type='checkbox']")
                    count = await checkboxes.count()
                    logger.debug(f"Найдено {count} чекбоксов")

                    # Делаем скриншот всех чекбоксов
                    if args.debug:
                        screenshot_path = f"{manage_debug_files('checkboxes')}.png"
                        await page.screenshot(path=screenshot_path)
                        logger.debug(f"Сделан скриншот чекбоксов: {screenshot_path}")

                    # Ищем чекбокс, содержащий название категории в ID или имени
                    for i in range(count):
                        checkbox = checkboxes.nth(i)
                        checkbox_id = await checkbox.get_attribute("id") or ""
                        checkbox_name = await checkbox.get_attribute("name") or ""
                        checkbox_value = await checkbox.get_attribute("value") or ""

                        logger.debug(
                            f"Чекбокс #{i+1}: id={checkbox_id}, name={checkbox_name}, value={checkbox_value}"
                        )

                        # Проверяем, содержит ли ID, имя или значение название категории
                        if (
                            category.lower() in checkbox_id.lower()
                            or category.lower() in checkbox_name.lower()
                            or category.lower() in checkbox_value.lower()
                            or "clientele" in checkbox_id.lower()
                        ):

                            logger.debug(
                                f"Найден подходящий чекбокс: id={checkbox_id}, name={checkbox_name}"
                            )

                            # Прокручиваем к чекбоксу
                            await checkbox.scroll_into_view_if_needed()
                            await page.wait_for_timeout(
                                1000
                            )  # Ждем завершения прокрутки

                            # Отмечаем чекбокс
                            await checkbox.check(force=True)
                            logger.debug(f"Чекбокс отмечен")

                            # Ждем обновления DOM
                            await page.wait_for_timeout(3000)

                            checkbox_found = True
                            break
                except Exception as id_error:
                    logger.error(f"Ошибка при поиске по ID или имени: {id_error}")

            # Метод 4: JavaScript как последний вариант
            if not checkbox_found:
                logger.debug(f"Пробуем отметить чекбокс через JavaScript...")
                try:
                    # Используем JavaScript для поиска и отметки чекбокса
                    await page.evaluate(
                        f"""
                        // Функция для поиска чекбокса по тексту метки
                        function findCheckboxByLabel(labelText) {{
                            // Ищем все метки
                            const labels = Array.from(document.querySelectorAll('label'));

                            // Ищем метку, содержащую нужный текст
                            for (const label of labels) {{
                                if (label.textContent.includes('{category}')) {{
                                    console.log('Найдена метка с текстом:', label.textContent);

                                    // Если метка имеет атрибут for, используем его для поиска чекбокса
                                    if (label.htmlFor) {{
                                        const checkbox = document.getElementById(label.htmlFor);
                                        if (checkbox) {{
                                            return checkbox;
                                        }}
                                    }}

                                    // Если метка не имеет атрибута for, ищем чекбокс внутри метки
                                    const checkbox = label.querySelector('input[type="checkbox"]');
                                    if (checkbox) {{
                                        return checkbox;
                                    }}
                                }}
                            }}

                            // Если не нашли по метке, ищем по ID, имени или значению
                            const checkboxes = Array.from(document.querySelectorAll('input[type="checkbox"]'));
                            for (const checkbox of checkboxes) {{
                                if (checkbox.id.toLowerCase().includes('{category.lower()}') ||
                                    checkbox.name.toLowerCase().includes('{category.lower()}') ||
                                    checkbox.value.toLowerCase().includes('{category.lower()}') ||
                                    checkbox.id.toLowerCase().includes('clientele')) {{
                                    return checkbox;
                                }}
                            }}

                            return null;
                        }}

                        // Ищем чекбокс
                        const checkbox = findCheckboxByLabel('{category}');

                        if (checkbox) {{
                            // Отмечаем чекбокс
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('Чекбокс отмечен через JavaScript');
                            return true;
                        }} else {{
                            console.error('Чекбокс не найден');
                            return false;
                        }}
                    """
                    )

                    logger.debug(f"JavaScript-скрипт для отметки чекбокса выполнен")
                    await page.wait_for_timeout(3000)  # Ждем обновления DOM
                except Exception as js_error:
                    logger.error(f"Ошибка при использовании JavaScript: {js_error}")

            # Делаем скриншот после выбора категории
            if args.debug:
                screenshot_path = f"{manage_debug_files('after_category')}.png"
                await page.screenshot(path=screenshot_path)
                logger.debug(
                    f"Сделан скриншот после выбора категории: {screenshot_path}"
                )

        except Exception as e:
            logger.error(f"Ошибка при установке чекбокса: {e}")
            # Делаем скриншот для отладки
            if args.debug:
                screenshot_path = f"{manage_debug_files('category_error')}.png"
                await page.screenshot(path=screenshot_path)
                logger.debug(f"Сделан скриншот ошибки: {screenshot_path}")
    else:
        logger.debug("Категория 'All', пропускаем выбор чекбокса")

    # ШАГ 2: Решаем капчу
    logger.debug("ШАГ 2: Решаем капчу...")
    try:
        captcha_solved = await solve_captcha(page)

        if not captcha_solved:
            logger.error("Не удалось решить капчу. Останавливаем обработку категории.")
            logger.info(f"[ЛОГ] Капча НЕ решена для категории: {category}")
            if args.debug:
                screenshot_path = (
                    f"{manage_debug_files(f'captcha_failed_{category}')}.png"
                )
                await page.screenshot(path=screenshot_path)
                logger.debug(f"Сделан скриншот ошибки: {screenshot_path}")
            return []  # Возвращаем пустой список результатов

        # Дополнительная проверка, что капча действительно решена
        is_solved = await is_captcha_solved(page)
        logger.info(
            f"[ЛОГ] solve_captcha вернула: {captcha_solved}, is_captcha_solved: {is_solved} для категории: {category}"
        )
        if not is_solved:
            logger.error(
                "Функция solve_captcha вернула True, но проверка показывает, что капча не решена"
            )
            logger.info(
                f"[ЛОГ] Капча НЕ подтверждена после solve_captcha для категории: {category}"
            )
            if args.debug:
                screenshot_path = f"{manage_debug_files(f'captcha_verification_failed_{category}')}.png"
                await page.screenshot(path=screenshot_path)
                logger.debug(f"Сделан скриншот ошибки: {screenshot_path}")
            return []  # Возвращаем пустой список результатов

        logger.info(
            f"[ЛОГ] Капча успешно решена и подтверждена для категории: {category}"
        )
    except Exception as e:
        # Проверяем, не связана ли ошибка с нулевым балансом
        if "ERROR_ZERO_BALANCE" in str(e):
            logger.critical("Нулевой баланс в Anti-Captcha. Парсинг будет остановлен.")
            raise  # Пробрасываем исключение дальше, чтобы остановить весь парсинг

        logger.error(f"Ошибка при решении капчи: {e}")
        logger.info(
            f"[ЛОГ] Исключение при решении капчи для категории: {category}: {e}"
        )
        if args and args.debug:
            screenshot_path = f"{manage_debug_files('captcha_error')}.png"
            await page.screenshot(path=screenshot_path)
            logger.debug(f"Сделан скриншот ошибки: {screenshot_path}")
        return []  # Возвращаем пустой список результатов в случае ошибки

    # ШАГ 4: Кликаем кнопку поиска
    logger.debug("ШАГ 4: Кликаем кнопку поиска...")
    await click_search_button(page)

    results = []
    page_num = 1

    # Ждем, пока страница загрузится после клика на кнопку поиска
    logger.debug("Ждем загрузки страницы после клика на кнопку поиска...")
    try:
        # Ждем появления любого из возможных селекторов результатов
        await page.wait_for_selector(
            "div.results, div.search-results, .result-item, .search-result, article, .card, div.listing-item, div.directory-item",
            timeout=15000,
        )
        logger.debug("Найден селектор результатов поиска")
    except PlaywrightTimeoutError:
        logger.debug("Тайм-аут ожидания селектора результатов, продолжаем выполнение")

    # Проверяем URL страницы
    current_url = page.url
    logger.debug(f"Текущий URL: {current_url}")

    # Делаем скриншот для отладки
    if args and args.debug:
        screenshot_path = f"{manage_debug_files('after_search')}.png"
        await page.screenshot(path=screenshot_path)
        logger.debug(f"Сделан скриншот после поиска: {screenshot_path}")

    # Проверяем, есть ли на странице индикаторы результатов поиска
    has_results_indicators = await page.evaluate(
        """
        () => {
            // Проверяем наличие текста, указывающего на результаты
            const pageText = document.body.innerText;
            const resultsIndicators = [
                'results found', 'search results', 'no results',
                'found', 'results', 'directory', 'listing'
            ];

            for (const indicator of resultsIndicators) {
                if (pageText.toLowerCase().includes(indicator)) {
                    return true;
                }
            }

            // Проверяем наличие элементов, которые могут содержать результаты
            const possibleContainers = [
                'div.results', 'div.search-results', 'div.directory',
                'section.results', 'ul.results', 'div.listing'
            ];

            for (const container of possibleContainers) {
                if (document.querySelector(container)) {
                    return true;
                }
            }

            return false;
        }
    """
    )

    if has_results_indicators:
        logger.debug("Найдены индикаторы результатов поиска на странице")
    else:
        logger.warning("Индикаторы результатов поиска не найдены")

    while True:
        logger.debug(f"Ожидаем загрузки результатов поиска (страница {page_num})...")
        try:
            # Используем функцию для поиска карточек результатов
            found_results, cards, count = await collect_result_cards(page)

            if not found_results or not cards:
                logger.info("Результаты поиска не найдены")
                return []  # Возвращаем пустой список, так как результатов нет

            logger.debug(f"Найдено {count} карточек на странице {page_num}")
        except PlaywrightTimeoutError:
            logger.error("Timeout при ожидании результатов поиска")
            break

        if not cards or count == 0:
            logger.warning("Карточки результатов не найдены или их количество равно 0")
            break

        for idx in range(count):
            logger.debug(f"Обрабатываем карточку {idx+1}/{count}...")
            card = cards.nth(idx)

            # Пробуем разные селекторы для имени
            name = f"Unknown-{idx}"
            try:
                # Пробуем разные селекторы для заголовка
                name_selectors = [
                    "h3",
                    "h2",
                    "h4",
                    ".name",
                    ".title",
                    "strong",
                    ".cpa-name",
                ]
                for name_selector in name_selectors:
                    name_element = card.locator(name_selector).first
                    if await name_element.is_visible(timeout=1000):
                        name = (await name_element.inner_text()).strip()
                        logger.debug(
                            f"Имя найдено с селектором {name_selector}: {name}"
                        )
                        break

                if name.startswith("Unknown-"):
                    # Если имя не найдено, берем весь текст карточки
                    name = (await card.inner_text()).strip().split("\n")[0]
                    logger.debug(f"Имя из текста карточки: {name}")
            except Exception as e:
                logger.error(f"Ошибка при получении имени: {e}")

            # Пробуем разные селекторы для URL профиля
            profile_url = None
            try:
                # Пробуем разные селекторы для ссылки на профиль
                link_selectors = [
                    "a:text('Profile')",
                    "a:text('View Profile')",
                    "a:text('Details')",
                    "a:has-text('Profile')",
                    "a:has-text('View')",
                    "a.profile-link",
                    "a.details-link",
                    "a",  # В крайнем случае берем любую ссылку
                ]

                for link_selector in link_selectors:
                    links = card.locator(link_selector)
                    first_link = links.first
                    if await first_link.is_visible(timeout=1000):
                        links_count = await links.count()
                        for i in range(links_count):
                            link = links.nth(i)
                            href = await link.get_attribute("href")
                            if href and (
                                href.startswith("http") or href.startswith("/")
                            ):
                                profile_url = href
                                logger.debug(
                                    f"URL профиля найден с селектором {link_selector}: {profile_url}"
                                )
                                break

                        if profile_url:
                            break
            except Exception as e:
                logger.error(f"Ошибка при получении URL профиля: {e}")

            # Создаем запись с найденными данными
            item = {"name": name, "category": category, "profile_url": profile_url}

            # Добавляем запись в результаты (детали добавим позже)
            results.append(item)
            logger.debug(f"Карточка {idx+1} обработана")

        # пагинация
        logger.debug("Проверяем наличие следующей страницы...")

        # Проверяем наличие кнопки пагинации с таймаутом 5 секунд
        try:
            next_btn = page.locator("a[aria-label='Next']")
            next_btn_count = await next_btn.count(timeout=5000)

            if next_btn_count == 0:
                logger.debug(
                    "Кнопка 'Next' не найдена, пагинация отсутствует или достигнута последняя страница"
                )
                break

            # Проверяем, не отключена ли кнопка
            try:
                is_disabled = await next_btn.is_disabled(timeout=5000)
                logger.debug(f"Кнопка 'Next' disabled={is_disabled}")

                if is_disabled:
                    logger.debug(
                        "Достигнута последняя страница результатов (кнопка отключена)"
                    )
                    break
            except Exception as disable_error:
                logger.debug(f"Не удалось проверить состояние кнопки: {disable_error}")
                # Продолжаем, так как кнопка может быть активна

            # Проверяем видимость кнопки
            is_visible = await next_btn.is_visible(timeout=5000)
            if not is_visible:
                logger.debug(
                    "Кнопка 'Next' не видима, пагинация отсутствует или достигнута последняя страница"
                )
                break

            logger.debug(f"Переходим на следующую страницу {page_num+1}...")
            await next_btn.click(timeout=10000)
            page_num += 1
            logger.debug(f"Переход на страницу {page_num} выполнен")
            await page.wait_for_timeout(3000)  # Даем больше времени для загрузки

        except Exception as e:
            logger.error(f"Ошибка при переходе на следующую страницу: {e}")
            break

    # Параллельный сбор деталей для всех найденных профилей
    if get_details:
        logger.info(
            f"Начинаем параллельный сбор деталей для {len(results)} профилей..."
        )

        # Фильтруем только записи с URL профилей
        profiles_with_urls = [item for item in results if item.get("profile_url")]
        logger.debug(f"Профилей с URL: {len(profiles_with_urls)}/{len(results)}")

        if profiles_with_urls:
            # Создаем список задач для параллельного выполнения
            detail_tasks = []
            for item in profiles_with_urls:
                profile_url = item.get("profile_url")
                if profile_url:
                    detail_tasks.append(
                        asyncio.create_task(scrape_details(page, profile_url))
                    )

            # Запускаем все задачи параллельно с ограничением через семафор
            if detail_tasks:
                logger.debug(
                    f"Запускаем {len(detail_tasks)} задач для сбора деталей..."
                )
                details_results = await asyncio.gather(
                    *detail_tasks, return_exceptions=True
                )

                # Обновляем результаты деталями
                for i, detail_result in enumerate(details_results):
                    if isinstance(detail_result, Exception):
                        logger.error(
                            f"Ошибка при получении деталей профиля #{i+1}: {detail_result}"
                        )
                    else:
                        profiles_with_urls[i].update(detail_result)
                        logger.debug(
                            f"Обновлен профиль #{i+1} с деталями: {detail_result}"
                        )

                logger.info(
                    f"Параллельный сбор деталей завершен для {len(detail_tasks)} профилей"
                )

    logger.debug(
        f"Обработка категории '{category}' завершена. Собрано {len(results)} записей"
    )
    logger.debug(f"===== ЗАВЕРШЕНИЕ ОБРАБОТКИ КАТЕГОРИИ: {category} =====")
    return results


# Семафор для ограничения количества параллельных запросов
details_semaphore = asyncio.Semaphore(6)


@precondition(
    lambda page, url: isinstance(page, Page)
    and not page.is_closed()
    and isinstance(url, str)
    and len(url) > 0
    and (url.startswith("http") or url.startswith("/")),
    "page должен быть экземпляром Page и не должен быть закрыт, url должен быть непустой строкой и начинаться с 'http' или '/'",
)
@postcondition(
    lambda result, *args, **kwargs: isinstance(result, dict),
    "Результат должен быть словарем",
)
async def scrape_details(page: Page, url: str) -> Dict[str, Any]:
    """Получает детальную информацию о профиле.

    Args:
        page: Объект страницы Playwright
        url: URL профиля

    Returns:
        Словарь с данными профиля

    Предусловия:
        - page должен быть экземпляром Page
        - page не должен быть закрыт
        - url должен быть непустой строкой
        - url должен начинаться с 'http' или '/'

    Постусловия:
        - Результат должен быть словарем
    """
    # Проверка предусловия внутри функции
    assert isinstance(page, Page), "page должен быть экземпляром Page"
    assert not page.is_closed(), "page не должен быть закрыт"
    assert isinstance(url, str) and len(url) > 0, "url должен быть непустой строкой"
    assert url.startswith("http") or url.startswith(
        "/"
    ), "url должен начинаться с 'http' или '/'"

    # Используем глобальный семафор
    sem = details_semaphore

    async with sem:  # Ограничиваем количество параллельных запросов
        logger.debug(f"===== НАЧАЛО ПОЛУЧЕНИЯ ДЕТАЛЕЙ ПРОФИЛЯ: {url} =====")

        # Используем context.expect_page() для надежного отслеживания новых вкладок
        async with page.context.expect_page() as new_page_info:
            # Открываем новую вкладку через JavaScript
            await page.evaluate(f"window.open('{url}', '_blank')")

        # Получаем новую вкладку
        new_tab = await new_page_info.value

        try:
            # Ждем загрузки страницы
            await new_tab.wait_for_load_state("domcontentloaded", timeout=60000)
            logger.debug("Страница профиля загружена")

            # Ждем загрузки заголовка h1 или другого основного элемента
            try:
                await new_tab.wait_for_selector(
                    "h1, .profile-header, .main-content", timeout=15000
                )
                logger.debug("Основной контент загружен")
            except PlaywrightTimeoutError:
                logger.debug(
                    "Тайм-аут ожидания основного контента, продолжаем выполнение"
                )

            # Ищем email
            email = ""
            try:
                email_elements = new_tab.locator("a[href^='mailto']")
                if await email_elements.first.is_visible(timeout=1000):
                    email = await email_elements.first.inner_text()
                    logger.debug(f"Email: {email}")
                else:
                    # Пробуем найти email в тексте страницы
                    page_text = await new_tab.content()
                    email_matches = re.findall(r"[\w.+-]+@[\w-]+\.[\w.-]+", page_text)
                    if email_matches:
                        email = email_matches[0]
                        logger.debug(f"Email найден в тексте: {email}")
                    else:
                        logger.debug("Email не найден")
            except Exception as e:
                logger.debug(f"Ошибка при поиске email: {e}")

            # Ищем телефон
            phone = ""
            try:
                phone_elements = new_tab.locator("a[href^='tel']")
                if await phone_elements.first.is_visible(timeout=1000):
                    phone = await phone_elements.first.inner_text()
                    logger.debug(f"Телефон: {phone}")
                else:
                    # Пробуем найти телефон в тексте страницы
                    page_text = await new_tab.content()
                    phone_matches = re.findall(
                        r"(\+\d{1,2}\s?)?(\(\d{3}\)|\d{3})[\s.-]?\d{3}[\s.-]?\d{4}",
                        page_text,
                    )
                    if phone_matches:
                        phone = "".join(phone_matches[0])
                        logger.debug(f"Телефон найден в тексте: {phone}")
                    else:
                        logger.debug("Телефон не найден")
            except Exception as e:
                logger.debug(f"Ошибка при поиске телефона: {e}")

        except Exception as e:
            logger.error(f"Ошибка при получении деталей профиля: {e}")
            email = ""
            phone = ""
        finally:
            # Гарантированно закрываем вкладку
            try:
                logger.debug("Закрываем вкладку профиля")
                await new_tab.close()
            except Exception as close_error:
                logger.warning(f"Ошибка при закрытии вкладки: {close_error}")

        result = {"email": email, "phone": phone}
        logger.debug(f"Результат получения деталей: {result}")
        logger.debug(f"===== ЗАВЕРШЕНИЕ ПОЛУЧЕНИЯ ДЕТАЛЕЙ ПРОФИЛЯ: {url} =====")
        return result


# ---------------------------------------------------------------------------
# CLI & main
# ---------------------------------------------------------------------------


def setup_logging(log_level: str) -> None:
    """Настройка логирования с указанным уровнем."""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Неверный уровень логирования: {log_level}")

    # Определяем путь к лог-файлу в той же директории, что и скрипт
    log_file_path = Path(__file__).parent / "parser_debug.log"

    # Форматы для логов
    file_log_format = "%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d %(message)s"
    console_log_format = "%(asctime)s [%(levelname)s] %(message)s"

    # Получаем корневой логгер
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Удаляем существующие обработчики, если они были добавлены basicConfig или предыдущими вызовами
    if root_logger.hasHandlers():
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

    # Настройка FileHandler для записи в файл (перезапись при каждом запуске)
    file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
    file_formatter = logging.Formatter(file_log_format, datefmt="%Y-%m-%d %H:%M:%S")
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

    # Настройка StreamHandler для вывода в консоль (с colorlog, если доступно и это терминал)
    if sys.stdout.isatty() and 'colorlog' in sys.modules:
        console_handler = colorlog.StreamHandler()
        console_formatter = colorlog.ColoredFormatter(
            f'%(log_color)s{console_log_format}',
            datefmt="%Y-%m-%d %H:%M:%S",
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_formatter)
    else:
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = logging.Formatter(console_log_format, datefmt="%Y-%m-%d %H:%M:%S")
        console_handler.setFormatter(console_formatter)
    
    root_logger.addHandler(console_handler)

    # Настройка логгера нашего приложения (если 'logger' - это специфический экземпляр logging.Logger)
    # Если 'logger' - это просто logging.getLogger(__name__), то root_logger.setLevel уже покрывает это.
    # Предполагая, что 'logger' это глобальный экземпляр: logger = logging.getLogger(__name__)
    # или logger = logging.getLogger('my_app_logger_name')
    # Если logger это просто псевдоним для logging, то эта строка не нужна или должна быть logger.getLogger().setLevel
    # Для безопасности, если logger это экземпляр, установим его уровень.
    # Если logger это модуль logging, то это вызовет ошибку.
    # Проверим, что logger - это экземпляр Logger
    if isinstance(logger, logging.Logger):
        logger.setLevel(numeric_level)

    # Отключаем лишние логи от библиотек
    logging.getLogger("playwright").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


def build_cli() -> argparse.Namespace:
    """Улучшенная конфигурация CLI"""
    parser = argparse.ArgumentParser(
        description="CPA Quebec Directory Parser",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    # Основные параметры
    parser.add_argument(
        "--by-category", action="store_true", help="Парсинг по категориям"
    )
    parser.add_argument(
        "--category", type=str, help="Конкретная категория для парсинга"
    )
    parser.add_argument(
        "--visible", action="store_true", help="Запуск в видимом режиме"
    )

    # Параметры отладки
    parser.add_argument(
        "--debug", action="store_true", help="Включить отладочный режим"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Уровень логирования",
    )

    # Параметры капчи
    parser.add_argument(
        "--no-captcha", action="store_true", help="Пропустить решение капчи"
    )
    parser.add_argument(
        "--captcha-timeout",
        type=int,
        default=180,
        help="Таймаут ожидания решения капчи в секундах",
    )

    # Параметры браузера
    parser.add_argument(
        "--viewport-width", type=int, default=1280, help="Ширина окна браузера"
    )
    parser.add_argument(
        "--viewport-height", type=int, default=800, help="Высота окна браузера"
    )
    parser.add_argument(
        "--timeout", type=int, default=30, help="Таймаут операций в секундах"
    )

    # Параметры вывода
    parser.add_argument(
        "--output-dir",
        type=str,
        default="output",
        help="Директория для сохранения результатов",
    )

    args = parser.parse_args()

    # Создаем директорию для вывода, если она не существует
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    return args


async def run() -> None:
    """Улучшенный основной цикл программы"""
    browser = None
    context = None

    try:
        # Инициализация аргументов
        global args
        args = build_cli()

        # Настройка логирования
        setup_logging(args.log_level)

        # Инициализация конфигураций
        browser_config = BrowserConfig(
            headless=not args.visible,
            viewport_width=args.viewport_width,
            viewport_height=args.viewport_height,
            timeout=args.timeout * 1000,  # конвертируем в миллисекунды
        )

        captcha_config = CaptchaConfig(max_wait_seconds=args.captcha_timeout)

        parser_config = ParserConfig(
            debug=args.debug,
            by_category=args.by_category,
            category=args.category,
            get_details=True,
            output_dir=Path(args.output_dir),
        )

        # Создание браузера
        async with async_playwright() as pw:
            browser, context = await create_browser_context(pw, browser_config)
            all_results: List[Dict[str, Any]] = []
            try:
                if parser_config.by_category:
                    if parser_config.category:
                        log_with_context(
                            "Режим парсинга одной категории",
                            category=parser_config.category,
                        )
                        page = await context.new_page()
                        try:
                            cat_results = await process_category(
                                page, parser_config.category, parser_config.get_details
                            )
                            log_with_context(
                                "Категория обработана",
                                category=parser_config.category,
                                records_count=len(cat_results),
                            )
                            all_results.extend(cat_results)
                        finally:
                            await page.close()
                    else:
                        log_with_context(
                            "Режим парсинга по категориям",
                            categories_count=len(CLIENT_CATEGORIES),
                        )
                        for cat in CLIENT_CATEGORIES:
                            page = await context.new_page()
                            try:
                                cat_results = await process_category(
                                    page, cat, parser_config.get_details
                                )
                                log_with_context(
                                    "Категория обработана",
                                    category=cat,
                                    records_count=len(cat_results),
                                )
                                all_results.extend(cat_results)
                            except ZeroBalanceError:
                                logger.critical("Нулевой баланс в Anti-Captcha")
                                break
                            except Exception as e:
                                logger.error(
                                    f"Ошибка при обработке категории {cat}: {e}"
                                )
                            finally:
                                await page.close()
                else:
                    log_with_context("Режим парсинга без фильтров")
                    page = await context.new_page()
                    try:
                        await page.goto(BASE_URL)
                        if not await handle_captcha(page, captcha_config):
                            logger.error("Не удалось решить капчу")
                            return
                        await click_search_button(page)
                        results = await process_category(
                            page, "All", parser_config.get_details
                        )
                        log_with_context(
                            "Получены результаты", records_count=len(results)
                        )
                        all_results.extend(results)
                    finally:
                        await page.close()
                if all_results:
                    await save_results(all_results, parser_config.output_dir)
                else:
                    logger.warning("Нет результатов для сохранения")
            except Exception as e:
                logger.error(f"Ошибка на уровне браузерного цикла: {e}")
    except Exception as e:
        logger.critical(f"Критическая ошибка: {e}")
        if "parser_config" in locals() and parser_config.debug:
            await take_debug_screenshot(page, "critical_error")
    finally:
        await cleanup(browser, context)


async def save_results(results: List[Dict[str, Any]], output_dir: Path) -> Path:
    """Сохранение результатов с обработкой ошибок"""
    if not results:
        logger.warning("Нет результатов для сохранения")
        return None

    try:
        timestamp = timestamp_str()
        outfile = output_dir / f"cpa_results_{timestamp}.json"
        outfile.write_text(json.dumps(results, ensure_ascii=False, indent=2))
        log_with_context(
            "Результаты сохранены", file=str(outfile), records_count=len(results)
        )
        return outfile
    except Exception as e:
        logger.error(f"Ошибка при сохранении результатов: {e}")
        return None


async def extract_recaptcha_sitekey(page: Page) -> Tuple[bool, Optional[str]]:
    """Извлекает sitekey из reCAPTCHA на странице."""
    try:
        # Сначала ищем в div.g-recaptcha
        recaptcha_div = page.locator("div.g-recaptcha")
        if await recaptcha_div.count() > 0:
            sitekey = await recaptcha_div.get_attribute("data-sitekey")
            if sitekey:
                logger.info(f"Извлечен sitekey из div: {sitekey[:10]}...")
                return True, sitekey

        # Если не нашли в div, ищем в iframe
        recaptcha_iframes = page.locator("iframe[src*='recaptcha']")
        count = await recaptcha_iframes.count()
        
        if count > 0:
            # Проверяем каждый iframe
            for i in range(count):
                iframe = recaptcha_iframes.nth(i)
                src = await iframe.get_attribute("src")
                if src:
                    # Извлекаем sitekey из URL
                    match = re.search(r"k=([^&]+)", src)
                    if match:
                        sitekey = match.group(1)
                        logger.info(f"Извлечен sitekey из iframe: {sitekey[:10]}...")
                        return True, sitekey

        # Если не нашли в iframe, пробуем найти в любом элементе с data-sitekey
        sitekey_element = page.locator("[data-sitekey]")
        if await sitekey_element.count() > 0:
            sitekey = await sitekey_element.get_attribute("data-sitekey")
            if sitekey:
                logger.info(f"Извлечен sitekey из элемента: {sitekey[:10]}...")
                return True, sitekey

        logger.warning("Sitekey не найден ни в одном месте")
        return False, None
    except Exception as e:
        logger.error(f"Ошибка при извлечении sitekey: {e}")
        return False, None

async def is_captcha_solved(page: Page) -> bool:
    """Проверяет, решена ли капча на странице."""
    logger.debug(f"is_captcha_solved: Started. Type of page: {type(page)}")
    try:
        # Part 1: page.evaluate
        logger.debug("is_captcha_solved: Attempting page.evaluate for token")
        token = await page.evaluate("""
            () => {
                const response = document.querySelector('#g-recaptcha-response');
                return response && response.value && response.value.length > 50;
            }
        """)
        logger.debug(f"is_captcha_solved: page.evaluate result for token: {token}")
        if token:
            logger.debug("is_captcha_solved: Token found, captcha is solved.")
            return True

        # Part 2: iframe interaction
        logger.debug("is_captcha_solved: Attempting page.locator for recaptcha_iframes")
        recaptcha_iframes = page.locator("iframe[src*='api2/anchor']")
        logger.debug(f"is_captcha_solved: Type of recaptcha_iframes: {type(recaptcha_iframes)}")
        
        count = await recaptcha_iframes.count()
        logger.debug(f"is_captcha_solved: recaptcha_iframes count: {count}")
        
        if count > 0:
            for i in range(count):
                logger.debug(f"is_captcha_solved: Processing iframe index {i}")
                iframe_locator_single = recaptcha_iframes.nth(i)
                logger.debug(f"is_captcha_solved: Type of iframe_locator_single (iframe {i}): {type(iframe_locator_single)}")
                
                logger.debug(f"is_captcha_solved: Attempting content_frame() for iframe {i}")
                frame_object = await iframe_locator_single.content_frame()
                logger.debug(f"is_captcha_solved: Type of frame_object (iframe {i}): {type(frame_object)}")
                
                if frame_object:
                    logger.debug(f"is_captcha_solved: frame_object for iframe {i} exists, attempting frame_object.locator for '.recaptcha-checkbox'")
                    checkbox_locator = frame_object.locator(".recaptcha-checkbox")
                    logger.debug(f"is_captcha_solved: Type of checkbox_locator (iframe {i}): {type(checkbox_locator)}")
                    
                    checkbox_count = await checkbox_locator.count()
                    logger.debug(f"is_captcha_solved: checkbox_locator count for iframe {i}: {checkbox_count}")
                    if checkbox_count > 0:
                        logger.debug(f"is_captcha_solved: Checkbox found in iframe {i}, attempting get_attribute 'aria-checked'")
                        is_checked = await checkbox_locator.get_attribute("aria-checked")
                        logger.debug(f"is_captcha_solved: Checkbox in iframe {i} aria-checked: {is_checked}")
                        if is_checked == "true":
                            logger.debug(f"is_captcha_solved: Checkbox in iframe {i} is checked, captcha is solved.")
                            return True

        logger.debug("is_captcha_solved: No token and no checked checkbox found, captcha is not solved.")
        return False
    except Exception as e:
        logger.error(f"Ошибка при проверке состояния капчи (is_captcha_solved): {e}", exc_info=True)
        return False


async def close_anticaptcha_session() -> None:
    """Закрывает сессию Anti-Captcha."""
    global anticaptcha_session
    if anticaptcha_session:
        await anticaptcha_session.close()
        anticaptcha_session = None


# ---------------------------------------------------------------------------
# Улучшенная работа с браузером
# ---------------------------------------------------------------------------


async def create_browser_context(
    pw, config: BrowserConfig
) -> Tuple[Browser, BrowserContext]:
    """Создание и настройка браузера с обработкой ошибок"""
    try:
        browser = await pw.chromium.launch(headless=config.headless)
        context = await browser.new_context(
            viewport={"width": config.viewport_width, "height": config.viewport_height},
            user_agent=config.user_agent,
        )
        return browser, context
    except Exception as e:
        logger.error(f"Ошибка при создании браузера: {e}")
        try:
            browser = await pw.chromium.connect_over_cdp("http://localhost:9222")
            context = await browser.new_context(
                viewport={
                    "width": config.viewport_width,
                    "height": config.viewport_height,
                },
                user_agent=config.user_agent,
            )
            return browser, context
        except Exception as e2:
            raise BrowserError(f"Не удалось создать браузер: {e2}")


async def take_debug_screenshot(page: Page, name: str) -> None:
    """Создание отладочных скриншотов"""
    try:
        screenshot_path = f"{manage_debug_files(name)}.png"
        await page.screenshot(path=screenshot_path)
        logger.debug(f"Сделан скриншот: {screenshot_path}")
    except Exception as e:
        logger.error(f"Ошибка при создании скриншота: {e}")


async def cleanup(browser: Browser, context: BrowserContext) -> None:
    """Очистка ресурсов"""
    await close_anticaptcha_session()

    if context:
        try:
            await context.close()
        except Exception as e:
            logger.warning(f"Ошибка при закрытии контекста: {e}")

    if browser:
        try:
            await browser.close()
        except Exception as e:
            logger.warning(f"Ошибка при закрытии браузера: {e}")


# ---------------------------------------------------------------------------
# Улучшенная обработка капчи
# ---------------------------------------------------------------------------


def with_retry(max_retries: int = 3, delay: int = 2):
    """Декоратор для повторных попыток выполнения функции"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    logger.warning(f"Попытка {attempt + 1} не удалась: {e}")
                    await asyncio.sleep(delay)
            return None

        return wrapper

    return decorator


def validate_token(token: str) -> bool:
    """Валидация токена капчи"""
    if not token or len(token) < 50:
        return False
    if not token.startswith("03"):
        logger.warning(f"Токен имеет необычный формат: {token[:5]}...")
    return True


@with_retry()
async def handle_captcha(page: Page, config: CaptchaConfig) -> bool:
    """Улучшенная обработка капчи"""
    try:
        log_with_context("Начинаем обработку капчи...")
        captcha_solved = await solve_captcha(page)

        if not captcha_solved:
            logger.error("Не удалось решить капчу")
            if args and args.debug:
                await take_debug_screenshot(page, "captcha_failed")
            return False

        logger.info("Капча успешно решена")
        return True
    except ZeroBalanceError:
        logger.critical("Нулевой баланс в Anti-Captcha")
        raise
    except Exception as e:
        logger.error(f"Ошибка при обработке капчи: {e}")
        return False


async def wait_for_captcha_solution(page: Page, config: CaptchaConfig) -> bool:
    """Улучшенное ожидание решения капчи"""
    start_time = time.time()
    consecutive_solved_checks = 0

    while time.time() - start_time < config.max_wait_seconds:
        if page.is_closed():
            logger.error("Страница была закрыта во время ожидания решения капчи")
            return False

        has_token = await page.evaluate(
            """
        () => {
            const response = document.querySelector('#g-recaptcha-response');
            return response && response.value && response.value.length > 50;
        }
        """
        )

        if has_token:
            consecutive_solved_checks += 1
            if consecutive_solved_checks >= config.required_consecutive_checks:
                logger.info("Капча успешно решена")
                return True
        else:
            consecutive_solved_checks = 0

        await asyncio.sleep(config.check_interval)

    logger.error("Время ожидания решения капчи истекло")
    return False


async def is_recaptcha_checkbox_checked(page: Page) -> bool:
    """Проверяет, отмечен ли чекбокс reCAPTCHA.

    Args:
        page: Объект страницы Playwright

    Returns:
        bool: True если чекбокс отмечен, False в противном случае
    """
    try:
        # Переключаемся в iframe капчи
        iframe = page.locator("iframe[src*='api2/anchor']").first
        if not await iframe.count():
            logger.debug("Чекбокс reCAPTCHA не найден")
            return False

        frame = await iframe.content_frame()
        if not frame:
            logger.debug("Не удалось получить frame капчи")
            return False

        # Проверяем состояние чекбокса
        checkbox = frame.locator("#recaptcha-anchor")
        if not await checkbox.count():
            logger.debug("Чекбокс не найден в frame")
            return False

        # Проверяем атрибут aria-checked
        is_checked = await checkbox.get_attribute("aria-checked")
        return is_checked == "true"

    except Exception as e:
        logger.error(f"Ошибка при проверке состояния чекбокса (is_recaptcha_checkbox_checked): {e}", exc_info=True)
        return False


async def click_recaptcha_checkbox(page: Page) -> bool:
    """Кликает по чекбоксу reCAPTCHA.

    Args:
        page: Объект страницы Playwright

    Returns:
        bool: True если клик выполнен успешно, False в противном случае
    """
    try:
        # Находим iframe капчи
        iframe = page.locator("iframe[src*='api2/anchor']").first
        if not await iframe.count():
            logger.debug("Чекбокс reCAPTCHA не найден")
            return False

        # Переключаемся в frame
        frame = await iframe.content_frame()
        if not frame:
            logger.debug("Не удалось получить frame капчи")
            return False

        # Находим и кликаем по чекбоксу
        checkbox = frame.locator("#recaptcha-anchor")
        if not await checkbox.count():
            logger.debug("Чекбокс не найден в frame")
            return False

        await checkbox.click()
        logger.info("Клик по чекбоксу reCAPTCHA выполнен")

        # Ждем немного после клика
        await page.wait_for_timeout(2000)
        return True

    except Exception as e:
        logger.error(f"Ошибка при клике по чекбоксу (click_recaptcha_checkbox): {e}", exc_info=True)
        return False


if __name__ == "__main__":
    asyncio.run(run())
