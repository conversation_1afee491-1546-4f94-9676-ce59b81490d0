2025-04-04 19:51:59,791 - C<PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_195159.log
2025-04-04 19:51:59,793 - <PERSON><PERSON><PERSON><PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:51:59,794 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:51:59,794 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:51:59,794 - C<PERSON>Q<PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:51:59,795 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:51:59,795 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:52:07,444 - CpaQuebecParser - DEBUG - Пауза на 1.21 сек. (после навигации)
2025-04-04 19:52:21,620 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 19:52:22,272 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
