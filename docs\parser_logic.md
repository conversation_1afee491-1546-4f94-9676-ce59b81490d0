# Логика Работы Парсера CPA Quebec (Selenium + undetected-chromedriver)

## Общая Идея

Парсер автоматизирует взаимодействие с веб-сайтом CPA Quebec (<https://cpaquebec.ca/en/find-a-cpa/cpa-directory/>) для сбора информации о бухгалтерах. **В этой версии используется Selenium совместно с `undetected-chromedriver` для управления браузером Chrome, что помогает обходить системы обнаружения автоматизации.** Логика разделена на несколько специализированных классов (компонентов), которые координируются основным классом-оркестратором.

## Пошаговый Процесс

1.  **Запуск (`cpa_quebec/main.py`)**
    *   Пользователь запускает скрипт из командной строки, например: `python3 cpa_quebec/main.py --by-category --visible --debug`.
    *   Скрипт `main.py` первым делом настраивает `sys.path`, чтобы Python мог найти пакет `cpa_quebec` и его подмодули.
    *   Импортируется основной класс `CpaQuebecParser` из **`cpa_quebec.selenium_parser.orchestrator`**, функция `setup_logging` и конфигурационные переменные. При ошибке импорта выводится сообщение и скрипт завершается.
    *   Вызывается `setup_logging()` для инициализации логгера, который будет записывать информацию в консоль и в файл в папке `logs/`.
    *   С помощью `argparse` разбираются аргументы командной строки. **Аргументы, специфичные для Playwright (например, таймауты), удалены.**
    *   Логгируется информация о запуске и переданных аргументах.
    *   Определяется режим работы (по категориям, по критериям или без критериев) и логгируется.

2.  **Инициализация Парсера и Браузера (`CpaQuebecParser.__init__` и `__enter__`)**
    *   Создается экземпляр класса `CpaQuebecParser` (оркестратора). В конструктор (`__init__`) передаются флаги `debug`, `headless` (противоположный `visible`) и `output_dir`.
    *   Конструктор устанавливает базовые атрибуты, повторно настраивает уровень логгера в зависимости от флага `debug`, загружает переменные окружения (`.env`) и инициализирует `CaptchaSolver` (если найден API ключ).
    *   Выполнение входит в блок `with CpaQuebecParser(...) as cpa_parser:`, что вызывает метод `cpa_parser.__enter__()`.
    *   В `__enter__`:
        *   Создается экземпляр `BrowserManager` (**`cpa_quebec/selenium_parser/browser.py`**), отвечающий за управление `undetected-chromedriver`.
        *   Вызывается `browser_manager.__enter__()`, который, в свою очередь, вызывает `browser_manager.start()`.
        *   `browser_manager.start()`:
            *   Импортирует `undetected_chromedriver`.
            *   Создает объект `webdriver.ChromeOptions`.
            *   Настраивает опции Chrome (headless, user-agent, отключение изображений, расширения и т.д.).
            *   **Инициализирует `uc.Chrome(options=options, version_main=None)`**, позволяя `undetected-chromedriver` автоматически определить версию установленного Chrome.
            *   Устанавливает таймауты (`page_load_timeout`, `implicitly_wait`).
            *   Сохраняет экземпляр `driver`.
        *   Если драйвер успешно запущен, `__enter__` вызывает `cpa_parser._initialize_components()`.
        *   `_initialize_components()`:
            *   Получает объект `driver` от `BrowserManager`.
            *   Создает экземпляры всех остальных компонентов, передавая им необходимые зависимости (**объект `driver`**, `logger`, другие компоненты, `debug`):
                *   `Navigator` (`navigation.py`): Для переходов по URL (`driver.get`), закрытия баннеров, сохранения отладки (**`driver.page_source`, `driver.save_screenshot`**).
                *   `FormHandler` (`form_handler_new.py`): Для работы с полями формы (**`find_elements`, `send_keys`, `clear`**), чекбоксами (поиск по XPath, **`is_selected`, `click`**), кнопками (**`element_to_be_clickable`, `click`**).
                *   `CaptchaHandler` (`captcha.py`): Для поиска iframe reCAPTCHA, извлечения sitekey, вызова решателя, **внедрения токена через `driver.execute_script` и переключения контекста (`driver.switch_to.frame`, `driver.switch_to.default_content`)**.
                *   `DetailsParser` (`details.py`): Для извлечения данных со страницы профиля (**`find_elements`, `.text`, `.get_attribute`**).
                *   `ResultsProcessor` (`results.py`): Для обработки страниц с результатами (**`find_elements`, XPath для поиска по тексту**), пагинации (**`element_to_be_clickable`, `click`**), вызова `DetailsParser`.
                *   `DataSaver` (`saver.py`): Для сохранения результатов в файлы (без изменений).
            *   **Вызывает `navigator.save_debug_info` для сохранения начального состояния.**
        *   `__enter__` возвращает сам объект `cpa_parser`, готовый к работе.

3.  **Выполнение Основной Логики (`CpaQuebecParser.parse`)**
    *   Внутри блока `with` вызывается `cpa_parser.parse(...)`, передавая ему аргументы командной строки (критерии поиска, `by_category`, `get_details`).
    *   `parse()` проверяет, инициализированы ли компоненты.
    *   **Очищает список `self.results`, но НЕ `self.processed_urls`, чтобы обеспечить дедупликацию между несколькими вызовами `parse` в рамках одного запуска парсера.**
    *   В зависимости от флага `by_category`, вызывает либо `_parse_by_categories()`, либо `_search_cpa()`.

4.  **Режим "Парсинг по Категориям" (`CpaQuebecParser._parse_by_categories`)**
    *   Итерирует по списку `CLIENT_CATEGORIES_TO_PARSE` из `config.py`.
    *   **Для каждой категории:**
        *   Запускается цикл повторных попыток (`while retries < MAX_RETRY_ATTEMPTS`).
        *   **Шаг 4.1: Навигация и подготовка:**
            *   `navigator.goto(BASE_URL)`: Переход на основную страницу каталога.
            *   `form_handler.clear()`: Очистка полей формы и чекбоксов (используя кнопку Reset или вручную).
            *   `form_handler.select_category(category)`: Выбор чекбокса для *текущей* категории (поиск по XPath, клик).
            *   `captcha_handler.handle()`: Проверка наличия и попытка решения reCAPTCHA (с использованием Selenium API).
            *   `form_handler.click_search_button()`: Нажатие кнопки "Search" (с ожиданием кликабельности).
            *   `random_sleep()`: Паузы для имитации действий пользователя и ожидания загрузки.
        *   **Шаг 4.2: Обработка результатов (если подготовка успешна):**
            *   Вызывается `results_processor.process_pages(get_details)`. (См. Шаг 6).
            *   Результаты, полученные от `process_pages`, добавляются в общий список `all_category_results`.
            *   Цикл повторных попыток для данной категории прерывается (`break`).
        *   **Шаг 4.3: Обработка ошибок/повторы:** Если любой из шагов подготовки (4.1) или обработки результатов (4.2) не удался (перехватываются `ConnectionError`, `TimeoutException`, `StaleElementReferenceException` и другие `Exception`), увеличивается счетчик `retries`, и цикл повторяется. Если все попытки исчерпаны, категория пропускается.
        *   `random_sleep()`: Пауза между обработкой разных категорий.
    *   Метод возвращает `all_category_results`.

5.  **Режим "Поиск по Критериям" (`CpaQuebecParser._search_cpa`)**
    *   Запускается цикл повторных попыток (`while retries < MAX_RETRY_ATTEMPTS`).
    *   **Шаг 5.1: Навигация и подготовка:**
        *   `navigator.goto(BASE_URL)`.
        *   `form_handler.clear()`.
        *   `form_handler.fill_search_criteria(...)`: Заполнение полей формы (**`send_keys`**).
        *   `captcha_handler.handle()`.
        *   `form_handler.click_search_button()`.
        *   `random_sleep()`.
    *   **Шаг 5.2: Обработка результатов (если подготовка успешна):**
        *   Вызывается `results_processor.process_pages(get_details)`. (См. Шаг 6).
        *   Цикл повторных попыток прерывается (`break`).
    *   **Шаг 5.3: Обработка ошибок/повторы:** Аналогично режиму по категориям, при ошибках выполняются повторные попытки. Если все попытки исчерпаны, метод возвращает пустой список.
    *   Метод возвращает результаты, полученные от `process_pages`.

6.  **Обработка Страниц Результатов (`ResultsProcessor.process_pages`)**
    *   Запускается цикл пагинации (`while True`).
    *   **Шаг 6.1: Извлечение данных с текущей страницы:**
        *   Сохраняется отладочная информация (`navigator.save_debug_info`).
        *   Проверяется наличие сообщения "No CPA found..." **(по XPath)**. Если есть, цикл пагинации прерывается.
        *   Вызывается `results_processor._extract_list_from_page()`:
            *   Находятся все карточки CPA на странице **(по CSS)** с помощью `driver.find_elements()`.
            *   Для каждой карточки (**`WebElement`**) извлекаются базовые данные (**`.text`, `.get_attribute('href')`**) с помощью `card.find_elements()`.
            *   URL профиля преобразуется в абсолютный с помощью `urllib.parse.urljoin`.
            *   Возвращается список словарей с данными для текущей страницы.
    *   **Шаг 6.2: Дедупликация и получение деталей:**
        *   Итерирует по списку `current_page_items`, полученному на шаге 6.1.
        *   Для каждого элемента (`item`) создается ключ дедупликации.
        *   Проверяется, нет ли этого ключа в общем множестве `self.processed_urls` и в локальном `processed_urls_in_run`.
        *   **Если запись новая:**
            *   Если `get_details is True` и есть URL профиля:
                *   Вызывается `self.details_parser.parse(profile_url)`. (См. Шаг 7).
                *   Если `parse` вернул словарь с деталями, он добавляется/обновляет данные в `item`.
            *   Обработанный `item` добавляется в итоговый список `all_page_results`.
            *   Ключ дедупликации добавляется в `processed_urls_in_run` и `self.processed_urls`.
    *   **Шаг 6.3: Переход на следующую страницу:**
        *   Вызывается `results_processor._click_next_page()`:
            *   Находится кнопка "Next" **(по XPath)** с помощью `driver.find_elements()`.
            *   Проверяется, активна ли кнопка (**`.is_enabled()`, проверка атрибута `class`**).
            *   Если активна, ожидается кликабельность (**`WebDriverWait + EC.element_to_be_clickable`**) и выполняется клик (**`.click()`**).
            *   **Опционально ожидается обновление контейнера результатов.**
            *   Возвращает `True`, если переход успешен, иначе `False`.
        *   Если `_click_next_page()` вернул `False`, цикл пагинации прерывается.
    *   Метод возвращает `all_page_results`.

7.  **Извлечение Деталей (`DetailsParser.parse`)**
    *   Вызывается из `ResultsProcessor.process_pages`.
    *   Принимает `profile_url`.
    *   Запускает цикл повторных попыток.
    *   `navigator.goto(profile_url)`: Переход на страницу профиля.
    *   Если переход успешен:
        *   **Опционально ожидается появление контейнера профиля (`WebDriverWait + EC.presence_of_element_located`).**
        *   С помощью селекторов **(CSS)** и `driver.find_elements()` извлекаются детальные данные со страницы.
        *   Используются `.text`, `.get_attribute()`, `clean_text`, `extract_emails`, `extract_phones`.
        *   Возвращается словарь с извлеченными деталями.
    *   При ошибках загрузки или извлечения (**`StaleElementReferenceException`** обрабатывается) выполняются повторные попытки. Если все попытки неудачны, возвращается `None`.

8.  **Сохранение Результатов (`CpaQuebecParser.parse` -> `DataSaver.save`)**
    *   После завершения `_parse_by_categories` или `_search_cpa`, метод `parse` вызывает `self.saver.save(self.results)`.
    *   `DataSaver.save()`:
        *   (Без изменений) Сохраняет результаты в `.xlsx` и `.json` файлы.

9.  **Завершение Работы (`__exit__` и `main.py`)**
    *   После выполнения кода внутри блока `with`, вызывается метод `cpa_parser.__exit__()`.
    *   `__exit__` вызывает `self.browser_manager.__exit__()`, который, в свою очередь, вызывает `browser_manager.stop()`.
    *   `browser_manager.stop()`: **Аккуратно закрывает драйвер (`driver.quit()`)**. 
    *   Метод `parse` возвращает список `results` в `main.py`.
    *   `main.py` логгирует сообщение о завершении парсинга.
    *   Устанавливается `exit_code = 0` (если не было критических ошибок).
    *   Выполняется блок `finally`, логгируя код завершения.
    *   Скрипт завершается с помощью `sys.exit(exit_code)`.

## Ключевые Компоненты и их Роли (Selenium)

*   **`main.py`**: Точка входа, парсинг аргументов, общая обработка ошибок, запуск оркестратора.
*   **`orchestrator.py` (`CpaQuebecParser`)**: Координирует весь процесс, инициализирует компоненты, вызывает методы для парсинга, вызывает сохранение.
*   **`browser.py` (`BrowserManager`)**: Управляет жизненным циклом **`undetected-chromedriver`**.
*   **`navigation.py` (`Navigator`)**: Отвечает за переходы по URL, закрытие баннеров, сохранение отладочной информации (**Selenium API**).
*   **`form_handler_new.py` (`FormHandler`)**: Взаимодействует с элементами формы (**Selenium API**).
*   **`captcha.py` (`CaptchaHandler`)**: Обрабатывает reCAPTCHA (**Selenium API, включая переключение iframe**).
*   **`results.py` (`ResultsProcessor`)**: Обрабатывает страницы с результатами, управляет пагинацией, вызывает извлечение деталей (**Selenium API**).
*   **`details.py` (`DetailsParser`)**: Извлекает данные с детальной страницы профиля (**Selenium API**).
*   **`saver.py` (`DataSaver`)**: Сохраняет результаты в файлы (без изменений).
*   **`utils.py`**: Содержит вспомогательные функции (без изменений, кроме, возможно, специфичных для Playwright, если они были).
*   **`config.py`**: Хранит константы и настройки (**добавлены таймауты для Selenium**).

Этот разделенный подход сохраняет свою ценность и в версии с Selenium, позволяя изолированно модифицировать компоненты при изменениях на сайте или в логике парсинга. Использование `undetected-chromedriver` повышает шансы на успешное выполнение без блокировок. 