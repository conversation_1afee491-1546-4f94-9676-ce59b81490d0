2025-03-26 19:52:32,321 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195232.log
2025-03-26 19:52:32,324 - <PERSON><PERSON><PERSON><PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-03-26 19:52:32,324 - C<PERSON>QuebecParser - INFO - ==================================================
2025-03-26 19:52:32,324 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-03-26 19:52:32,324 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 30, 'page_timeout': 60, 'get_details': True, 'by_category': True}
2025-03-26 19:52:32,324 - CpaQuebecParser - INFO - ==================================================
2025-03-26 19:52:32,324 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-03-26 19:52:32,325 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195232.log
2025-03-26 19:52:32,325 - CpaQuebecParser - INFO - Инициализация парсера CPA Quebec (Playwright)
2025-03-26 19:52:32,326 - CpaQuebecParser - WARNING - API ключ для 2Captcha не найден. Решение капчи будет пропущено.
2025-03-26 19:52:32,326 - CpaQuebecParser - INFO - Запуск браузера Chromium (Headless: False) через Playwright...
2025-03-26 19:52:33,488 - CpaQuebecParser - INFO - Браузер Chromium запущен.
2025-03-26 19:52:33,489 - CpaQuebecParser - INFO - Используется User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-03-26 19:52:33,516 - CpaQuebecParser - INFO - Контекст браузера создан.
2025-03-26 19:52:33,797 - CpaQuebecParser - INFO - Новая страница создана.
2025-03-26 19:52:33,845 - CpaQuebecParser - INFO - Добавлен init script для скрытия navigator.webdriver.
2025-03-26 19:52:33,846 - CpaQuebecParser - INFO - ============================== ЗАПУСК ПАРСИНГА (Playwright) ==============================
2025-03-26 19:52:33,846 - CpaQuebecParser - INFO - Режим: Парсинг по категориям клиентов.
2025-03-26 19:52:33,846 - CpaQuebecParser - INFO - Запуск парсинга по категориям (Playwright): Arts and culture organizations, Co-ownership syndicates, Elderly individuals, Executives, Farmers, Indigenous communities, Individuals, Large businesses, Medium-sized businesses, Non-profit organizations, Professionals, Self-employed workers, Small businesses, Sports organizations, Start-ups
2025-03-26 19:52:33,847 - CpaQuebecParser - INFO - --- Обработка категории: Arts and culture organizations (1/15) ---
2025-03-26 19:52:33,848 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:43,153 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 19:52:43,154 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 19:52:45,260 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: '#onetrust-accept-btn-handler')
2025-03-26 19:52:45,622 - CpaQuebecParser - INFO - Клик по кнопке cookie выполнен.
2025-03-26 19:52:45,622 - CpaQuebecParser - INFO - Баннер cookie обработан.
2025-03-26 19:52:46,624 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 19:52:46,639 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 19:52:48,954 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Arts and culture organizations' через JS.
2025-03-26 19:52:48,958 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Arts and culture organizations' при проверке.
2025-03-26 19:52:48,958 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Arts and culture organizations'. Пропускаем.
2025-03-26 19:52:49,804 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Arts and culture organizations_select_failed_20250326_195248.png
2025-03-26 19:52:49,831 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Arts and culture organizations_select_failed_20250326_195248.html
2025-03-26 19:52:49,832 - CpaQuebecParser - INFO - --- Обработка категории: Co-ownership syndicates (2/15) ---
2025-03-26 19:52:49,832 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:52:52,117 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 19:52:52,117 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 19:52:54,321 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 19:53:00,311 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 19:53:00,316 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 19:53:00,316 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 19:53:00,316 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 19:53:00,321 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 19:53:02,647 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Co-ownership syndicates' через JS.
2025-03-26 19:53:02,650 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Co-ownership syndicates' при проверке.
2025-03-26 19:53:02,650 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Co-ownership syndicates'. Пропускаем.
2025-03-26 19:53:03,385 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Co-ownership syndicates_select_failed_20250326_195302.png
2025-03-26 19:53:03,410 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Co-ownership syndicates_select_failed_20250326_195302.html
2025-03-26 19:53:03,410 - CpaQuebecParser - INFO - --- Обработка категории: Elderly individuals (3/15) ---
2025-03-26 19:53:03,411 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:53:05,310 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 19:53:05,310 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 19:53:07,565 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 19:53:13,505 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 19:53:13,510 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 19:53:13,510 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 19:53:13,510 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 19:53:13,514 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 19:53:15,680 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Elderly individuals' через JS.
2025-03-26 19:53:15,684 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Elderly individuals' при проверке.
2025-03-26 19:53:15,684 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Elderly individuals'. Пропускаем.
2025-03-26 19:53:16,522 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Elderly individuals_select_failed_20250326_195315.png
2025-03-26 19:53:16,545 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Elderly individuals_select_failed_20250326_195315.html
2025-03-26 19:53:16,545 - CpaQuebecParser - INFO - --- Обработка категории: Executives (4/15) ---
2025-03-26 19:53:16,545 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:53:18,316 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 19:53:18,319 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 19:53:20,565 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 19:53:26,417 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 19:53:26,423 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 19:53:26,423 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 19:53:26,424 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 19:53:26,433 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 19:53:28,656 - CpaQuebecParser - WARNING - Не удалось найти/выбрать чекбокс для 'Executives' через JS.
2025-03-26 19:53:28,659 - CpaQuebecParser - WARNING - Не найден чекбокс для 'Executives' при проверке.
2025-03-26 19:53:28,659 - CpaQuebecParser - ERROR - Не удалось выбрать чекбокс для категории 'Executives'. Пропускаем.
2025-03-26 19:53:29,402 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Executives_select_failed_20250326_195328.png
2025-03-26 19:53:29,425 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/category_Executives_select_failed_20250326_195328.html
2025-03-26 19:53:29,425 - CpaQuebecParser - INFO - --- Обработка категории: Farmers (5/15) ---
2025-03-26 19:53:29,425 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:53:31,314 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 200.
2025-03-26 19:53:31,315 - CpaQuebecParser - INFO - Проверка и закрытие баннера cookies (Playwright)...
2025-03-26 19:53:33,539 - CpaQuebecParser - INFO - Найдена кнопка cookie (Selector: 'a:text-matches('Accept|Agree|Allow', 'i')')
2025-03-26 19:53:39,515 - CpaQuebecParser - WARNING - Не удалось кликнуть кнопку cookie, пробуем скрыть баннер через JS...
2025-03-26 19:53:39,520 - CpaQuebecParser - ERROR - Ошибка при скрытии баннеров через JS: '>' not supported between instances of 'NoneType' and 'int'
2025-03-26 19:53:39,520 - CpaQuebecParser - INFO - Баннер cookie не найден или не обработан.
2025-03-26 19:53:39,520 - CpaQuebecParser - INFO - Очистка формы поиска (Playwright JS)...
2025-03-26 19:53:39,524 - CpaQuebecParser - INFO - Поля и чекбоксы формы очищены через JavaScript.
2025-03-26 19:53:40,342 - CpaQuebecParser - INFO - Общее время парсинга (Playwright): 66.50 секунд.
2025-03-26 19:53:40,343 - CpaQuebecParser - INFO - ============================== ЗАВЕРШЕНИЕ ПАРСИНГА (Playwright) ==============================
2025-03-26 19:53:40,344 - CpaQuebecParser - INFO - Закрытие браузера и Playwright...
2025-03-26 19:53:40,497 - CpaQuebecParser - WARNING - Ошибка при закрытии контекста Playwright: BrowserContext.close: Target page, context or browser has been closed
Browser logs:

<launching> /home/<USER>/.cache/ms-playwright/chromium-1161/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-blink-features=AutomationControlled --user-data-dir=/tmp/playwright_chromiumdev_profile-CJuzcA --remote-debugging-pipe --no-startup-window
<launched> pid=59027
[pid=59027][err] [59066:59066:0326/195233.530226:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.530435:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.530550:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.530640:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.530755:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.530831:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.530947:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531032:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531131:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531210:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531308:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531381:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531483:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531560:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531662:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531736:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531855:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.531959:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532065:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532141:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532251:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532327:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532426:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532499:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532668:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532875:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.532988:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.533172:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.533346:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.533517:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.533637:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.533723:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.533833:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.533948:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534066:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534148:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534251:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534329:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534451:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534545:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534747:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534832:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.534956:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535038:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535170:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535269:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535378:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535454:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535552:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535625:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535738:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535813:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.535924:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536000:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536107:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536184:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536282:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536365:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536479:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536556:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536679:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536759:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536862:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.536954:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537051:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537162:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537262:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537351:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537444:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537534:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537637:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537718:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537819:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.537902:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.538011:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.538093:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.538197:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.538282:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.538540:ERROR:gbm_wrapper.cc(79)] Failed to get fd for plane.: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195233.538624:ERROR:gbm_wrapper.cc(261)] Failed to export buffer to dma_buf: Нет такого файла или каталога (2)
[pid=59027][err] [59066:59066:0326/195248.206627:ERROR:gl_surface_presentation_helper.cc(260)] GetVSyncParametersIfAvailable() failed for 1 times!
[pid=59027][err] [59066:59066:0326/195248.208260:ERROR:gl_surface_presentation_helper.cc(260)] GetVSyncParametersIfAvailable() failed for 2 times!
[pid=59027][err] [59066:59066:0326/195254.602401:ERROR:gl_surface_presentation_helper.cc(260)] GetVSyncParametersIfAvailable() failed for 3 times!
[pid=59027] <gracefully close start>
2025-03-26 19:53:40,562 - CpaQuebecParser - INFO - Playwright остановлен.
2025-03-26 19:53:40,563 - CpaQuebecParser - ERROR - PlaywrightParser завершился с ошибкой: KeyboardInterrupt: 
2025-03-26 19:53:40,563 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 1
