2025-04-23 17:25:18,704 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250423_172518.log
2025-04-23 17:25:18,708 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-23 17:25:18,708 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-23 17:25:18,709 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'SMEs', 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-23 17:25:18,709 - CpaQuebecParser - INFO - ==================================================
2025-04-23 17:25:18,709 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: SMEs.
2025-04-23 17:27:39,315 - CpaQuebecParser - INFO - Парсинг (Selenium) завершен. Найдено и сохранено записей: 0
2025-04-23 17:27:39,454 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 0
