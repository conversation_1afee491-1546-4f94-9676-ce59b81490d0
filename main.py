#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from cpa_quebec.selenium_parser.form_handler_new import FormHandler
import time

# Упрощенная проверка пути: Добавляем корневую директорию проекта, если нужно.
# Обычно не требуется при запуске из корня.
project_root = os.path.dirname(os.path.abspath(__file__))
parent_root = os.path.dirname(project_root) # Поднимаемся на уровень выше
if parent_root not in sys.path:
    sys.path.insert(0, parent_root)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Удален отладочный вывод sys.path

try:
    # Импорт парсера из orchestrator
    from cpa_quebec.selenium_parser.orchestrator import CpaQuebecParser
    from cpa_quebec.utils import setup_logging
    # Импортируем константы, используемые в аргументах по умолчанию, из config.py
    from cpa_quebec.config import (
        BASE_URL,                 # URL каталога CPA Quebec
        OUTPUT_DIR,
        IMPLICIT_WAIT_SEC,         # Используем это для --wait-timeout
        PAGE_LOAD_TIMEOUT_SEC,     # Используем это для --page-timeout
        MEDIUM_TIMEOUT_SEC         # Используем это для --element-timeout
    )
except ImportError as e:
     print(f"Ошибка импорта: {e}")
     print("Убедитесь, что скрипт запускается из корневой директории проекта.")
     print("Установите зависимости: pip install -r requirements.txt")
     sys.exit(1)


def run_parser():
    """Основная функция для настройки и запуска парсера Selenium."""
    logger = setup_logging()

    # --- Настройка аргументов командной строки ---
    # Используем описание из предыдущей версии, т.к. undetected-chromedriver это деталь реализации
    parser = argparse.ArgumentParser(description='Парсер для CPA Quebec Directory (Selenium)')
    parser.add_argument('--last-name', type=str, default="", help='Фамилия для поиска')
    parser.add_argument('--first-name', type=str, default="", help='Имя для поиска')
    parser.add_argument('--region', type=str, default="", help='Регион для поиска')
    parser.add_argument('--city', type=str, default="", help='Город для поиска')
    parser.add_argument('--visible', action='store_true', help='Запустить Chrome в видимом режиме (не headless)')
    parser.add_argument('--debug', action='store_true', help='Включить режим отладки (DEBUG логирование, скриншоты)')
    parser.add_argument('--no-captcha', action='store_true', help='Отключить попытки решения reCAPTCHA') # Обновил текст help
    parser.add_argument('--output-dir', type=str, default=OUTPUT_DIR, help=f'Директория для сохранения результатов (по умолчанию: {OUTPUT_DIR})')
    # Таймауты Selenium с обновленными значениями по умолчанию из config.py
    parser.add_argument('--wait-timeout', type=int, default=IMPLICIT_WAIT_SEC, help=f'Неявное ожидание Selenium (секунды, по умолчанию: {IMPLICIT_WAIT_SEC})')
    parser.add_argument('--page-timeout', type=int, default=PAGE_LOAD_TIMEOUT_SEC, help=f'Таймаут загрузки страницы Selenium (секунды, по умолчанию: {PAGE_LOAD_TIMEOUT_SEC})')
    parser.add_argument('--element-timeout', type=int, default=MEDIUM_TIMEOUT_SEC, help=f'Явное ожидание элемента Selenium (секунды, по умолчанию: {MEDIUM_TIMEOUT_SEC})')
    parser.add_argument('--get-details', action='store_true', default=True, help='Получать детальную информацию (по умолчанию включено)')
    parser.add_argument('--no-details', action='store_false', dest='get_details', help='Не получать детальную информацию')
    parser.add_argument('--by-category', action='store_true', help='Запуск парсинга по категориям')
    # Добавляем новый аргумент для указания конкретной категории
    parser.add_argument('--category', type=str, default=None, help='Парсить только указанную категорию клиентов')
    # Добавляем новый аргумент для запуска тестового сценария FormHandler
    parser.add_argument('--test-form-handler', action='store_true', help='Запустить тестовый сценарий FormHandler и выйти')
    parser.add_argument('--test-category', type=str, default='Individuals', help='Категория для теста FormHandler')
    # Удален аргумент --accepting-new-clients, так как его нет в логике parse

    args = parser.parse_args()

    # Добавляем проверку на взаимоисключающие аргументы
    if args.by_category and args.category:
        parser.error("Аргументы --by-category и --category не могут использоваться вместе.")
    if args.category and any([args.last_name, args.first_name, args.region, args.city]):
        parser.error("Аргумент --category не может использоваться вместе с критериями поиска (last-name, first-name, region, city).")

    if args.debug:
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
             handler.setLevel(logging.DEBUG)
        logger.debug("Режим отладки DEBUG включен.")

    logger.info("=" * 50)
    logger.info("Запуск CPA Quebec Parser (Selenium)")
    logger.info(f"Аргументы: {vars(args)}")
    logger.info("=" * 50)

    # Обновляем логику определения режима работы
    if args.category:
         logger.info(f"Режим работы: Парсинг одной категории: {args.category}.")
    elif args.by_category: 
        logger.info("Режим работы: Парсинг по всем категориям.")
    elif any([args.last_name, args.first_name, args.region, args.city]): 
        logger.info("Режим работы: Поиск по критериям.")
    else: 
        logger.warning("Режим работы: Поиск без критериев.")

    # Обрабатываем тестовый сценарий FormHandler, если флаг установлен
    if args.test_form_handler:
        logger = setup_logging()
        if args.debug:
            logger.setLevel(logging.DEBUG)
            for handler in logger.handlers:
                handler.setLevel(logging.DEBUG)
        logger.info("Запуск тестового сценария FormHandler")
        # Создаем директорию для скриншотов теста
        os.makedirs('test_screenshots', exist_ok=True)
        # Настраиваем опции Chrome
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)
        # Инициализируем драйвер
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        try:
            logger.info(f"Переход на страницу: {BASE_URL}")
            driver.get(BASE_URL)
            form_handler = FormHandler(driver, logger, debug=args.debug)
            logger.info("Тест: Закрытие баннера с куки")
            form_handler.close_cookie_banner()
            logger.info(f"Тест: Выбор категории клиентов: {args.test_category}")
            form_handler.select_category(args.test_category)
            driver.save_screenshot("test_screenshots/after_select_category.png")
            logger.info("Тест: Клик по reCAPTCHA")
            try:
                form_handler.click_captcha()
                driver.save_screenshot("test_screenshots/after_click_captcha.png")
            except Exception as e:
                logger.warning(f"Не удалось кликнуть по reCAPTCHA: {e}")
            logger.info("Ожидание 3 секунды для решения капчи...")
            time.sleep(3)
            logger.info("Тест: Нажатие кнопки поиска")
            try:
                form_handler.click_search_button()
                driver.save_screenshot("test_screenshots/after_click_search.png")
            except Exception as e:
                logger.error(f"Не удалось нажать кнопку поиска: {e}")
            logger.info("Ожидание 10 секунд для просмотра результатов...")
            time.sleep(10)
        finally:
            driver.quit()
            logger.info("Тест FormHandler завершен")
        sys.exit(0)

    # --- Запуск парсера Selenium ---
    exit_code = 1
    # Используем CpaQuebecParser с контекстным менеджером, если он его поддерживает
    # Если нет - нужно будет вызвать cpa_parser.close() в finally
    try:
        with CpaQuebecParser(
            debug=args.debug,
            headless=not args.visible,
            output_dir=args.output_dir,
            # Передаем таймауты
            wait_timeout=args.wait_timeout,
            page_load_timeout=args.page_timeout,
            element_timeout=args.element_timeout,
            # Передаем опцию решения капчи
            solve_captcha=not args.no_captcha
        ) as cpa_parser: # Предполагаем, что класс поддерживает 'with'

            # Закомментировано, так как управление капчей теперь в конструкторе
            # if args.no_captcha:
            #      logger.info("Решение капчи отключено (--no-captcha).")
            #      cpa_parser.captcha_solver = None # Неправильно, нужно передавать в конструктор

            # Обновляем вызов parse, передавая новую информацию
            results = cpa_parser.parse(
                last_name=args.last_name,
                first_name=args.first_name,
                region=args.region,
                city=args.city,
                by_category=args.by_category, # Для парсинга всех категорий
                category_to_parse=args.category, # Для парсинга одной категории
                get_details=args.get_details
            )

            logger.info(f"Парсинг (Selenium) завершен. Найдено и сохранено записей: {len(results) if results is not None else 0}") # Добавлена проверка на None
            exit_code = 0

    except ImportError:
         # Маловероятно здесь, но оставим
         exit_code = 1
    except Exception as e: # Ловим общие ошибки, включая потенциальные ошибки парсера
        logger.critical(f"Критическая ошибка выполнения (Selenium): {e}")
        import traceback
        logger.error(traceback.format_exc())
        # Определяем разные коды ошибок в зависимости от типа
        if isinstance(e, FileNotFoundError):
            exit_code = 2 # Например, ошибка с chromedriver
        elif isinstance(e, ConnectionError):
             exit_code = 3 # Ошибка сети
        else:
            exit_code = 4 # Другая ошибка парсинга/Selenium
    finally:
        logger.info(f"Завершение работы скрипта (Selenium) с кодом: {exit_code}")
        # Если CpaQuebecParser не использует 'with', нужно добавить cpa_parser.close() здесь

    if exit_code != 0:
        sys.exit(exit_code)


if __name__ == "__main__":
    run_parser()
