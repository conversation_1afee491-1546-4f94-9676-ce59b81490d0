2025-04-18 14:55:11,011 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_145511.log
2025-04-18 14:55:11,017 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 14:55:11,021 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-18 14:55:11,021 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 14:55:11,022 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-18 14:55:11,023 - CpaQuebecParser - INFO - ==================================================
2025-04-18 14:55:11,024 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-18 14:56:03,135 - CpaQuebecParser - DEBUG - Пауза на 2.27 сек. (после driver.get)
2025-04-18 14:56:05,826 - CpaQuebecParser - DEBUG - Пауза на 0.38 сек. (после scroll к кнопке cookie)
2025-04-18 14:56:25,290 - CpaQuebecParser - DEBUG - Пауза на 0.79 сек. (после клика Reset (XPath))
2025-04-18 14:57:01,049 - CpaQuebecParser - DEBUG - Пауза на 0.78 сек. (после очистки формы)
2025-04-18 14:58:14,105 - CpaQuebecParser - DEBUG - Пауза на 3.49 сек. (между категориями, после Individuals)
2025-04-18 14:58:22,884 - CpaQuebecParser - DEBUG - Пауза на 2.01 сек. (после driver.get)
2025-04-18 14:59:57,128 - CpaQuebecParser - DEBUG - Пауза на 0.93 сек. (после клика Reset (XPath))
2025-04-18 15:00:02,075 - CpaQuebecParser - DEBUG - Пауза на 0.91 сек. (после очистки формы)
2025-04-18 15:01:16,349 - CpaQuebecParser - DEBUG - Пауза на 4.14 сек. (между категориями, после Large companies)
2025-04-18 15:01:34,080 - CpaQuebecParser - DEBUG - Пауза на 1.03 сек. (после driver.get)
