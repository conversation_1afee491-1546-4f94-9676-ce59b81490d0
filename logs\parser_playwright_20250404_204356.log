2025-04-04 20:43:56,182 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_204356.log
2025-04-04 20:43:56,187 - C<PERSON>Q<PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:43:56,187 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 20:43:56,188 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:43:56,188 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True}
2025-04-04 20:43:56,188 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:43:56,188 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:44:09,533 - CpaQuebecParser - DEBUG - Пауза на 1.44 сек. (после driver.get)
2025-04-04 20:44:11,091 - CpaQuebecParser - DEBUG - Пауза на 0.65 сек. (после scroll к кнопке cookie)
2025-04-04 20:44:23,643 - CpaQuebecParser - DEBUG - Пауза на 0.66 сек. (после клика Reset (XPath))
2025-04-04 20:44:27,298 - CpaQuebecParser - DEBUG - Пауза на 0.56 сек. (после очистки формы)
2025-04-04 20:45:29,715 - CpaQuebecParser - DEBUG - Пауза на 3.03 сек. (между категориями, после Individuals)
2025-04-04 20:45:38,199 - CpaQuebecParser - DEBUG - Пауза на 1.63 сек. (после driver.get)
2025-04-04 20:46:18,686 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
