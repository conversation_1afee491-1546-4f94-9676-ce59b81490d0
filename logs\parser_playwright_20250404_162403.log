2025-04-04 16:24:03,361 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_162403.log
2025-04-04 16:24:03,363 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 16:24:03,363 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:24:03,363 - C<PERSON>Que<PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:24:03,364 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:24:03,364 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:24:25,137 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): name 'RETRY_DELAY' is not defined
2025-04-04 16:24:25,148 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 188, in _parse_by_categories
    self.form_handler.select_category(category)
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/form_handler.py", line 397, in select_category
    raise ValueError(f"Чекбокс для категории '{category_name}' не найден.")
ValueError: Чекбокс для категории 'Individuals' не найден.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 96, in run_parser
    results = cpa_parser.parse(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 165, in parse
    self.results = self._parse_by_categories(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/playwright_parser/orchestrator.py", line 193, in _parse_by_categories
    time.sleep(RETRY_DELAY)
NameError: name 'RETRY_DELAY' is not defined

2025-04-04 16:24:25,148 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
