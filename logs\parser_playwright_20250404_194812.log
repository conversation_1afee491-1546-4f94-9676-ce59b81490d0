2025-04-04 19:48:12,276 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_194812.log
2025-04-04 19:48:12,280 - <PERSON><PERSON><PERSON><PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:48:12,281 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 19:48:12,281 - C<PERSON>Q<PERSON>becParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:48:12,281 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:48:12,281 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:48:12,282 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:48:44,602 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 19:48:44,825 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
