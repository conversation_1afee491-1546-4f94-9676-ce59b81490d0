2025-04-04 20:47:01,401 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_204701.log
2025-04-04 20:47:01,404 - CpaQuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:47:01,404 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:47:01,404 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:47:01,404 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True}
2025-04-04 20:47:01,404 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:47:01,405 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 20:47:12,844 - CpaQuebecParser - DEBUG - Пауза на 2.48 сек. (после driver.get)
2025-04-04 20:47:15,468 - CpaQuebecParser - DEBUG - Пауза на 0.63 сек. (после scroll к кнопке cookie)
2025-04-04 20:47:28,145 - CpaQuebecParser - DEBUG - Пауза на 0.54 сек. (после клика Reset (XPath))
2025-04-04 20:47:34,126 - CpaQuebecParser - DEBUG - Пауза на 0.84 сек. (после очистки формы)
2025-04-04 20:48:37,891 - CpaQuebecParser - DEBUG - Пауза на 2.88 сек. (между категориями, после Individuals)
2025-04-04 20:48:46,628 - CpaQuebecParser - DEBUG - Пауза на 2.37 сек. (после driver.get)
2025-04-04 20:48:55,516 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
