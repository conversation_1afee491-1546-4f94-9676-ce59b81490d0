#!/usr/bin/env python3
from bs4 import BeautifulSoup

# Загружаем ответ API
with open('api_response.html', 'r', encoding='utf-8') as f:
    html = f.read()

soup = BeautifulSoup(html, 'html.parser')

# Ищем контейнеры с результатами
print("=== Поиск контейнеров с результатами ===")
containers = [
    ('SectionResultats', soup.find(id='SectionResultats')),
    ('AfficherResultatsListContainer', soup.find(id='AfficherResultatsListContainer')),
    ('AfficherResultatsListStaticContainer', soup.find(id='AfficherResultatsListStaticContainer')),
    ('results-list', soup.find(class_='results-list')),
    ('results-container', soup.find(class_='results-container')),
    ('search-results', soup.find(class_='search-results'))
]

for name, container in containers:
    if container:
        print(f"Контейнер '{name}' найден: {container.name}")
        links = container.find_all('a')
        print(f"  Содержит ссылок: {len(links)}")

        # Показываем первые 3 ссылки
        for i, link in enumerate(links[:3]):
            print(f"  Ссылка #{i+1}: {link.get('href')} - {link.get_text(strip=True)}")
    else:
        print(f"Контейнер '{name}' не найден")

# Ищем все div с классами, содержащими 'result' или 'list'
print("\n=== Поиск div с классами, содержащими 'result' или 'list' ===")
result_divs = soup.find_all('div', class_=lambda c: c and ('result' in c.lower() or 'list' in c.lower()))
for i, div in enumerate(result_divs):
    print(f"Div #{i+1}: class='{div.get('class')}', id='{div.get('id')}'")
    links = div.find_all('a')
    print(f"  Содержит ссылок: {len(links)}")

    # Показываем первые 3 ссылки
    for j, link in enumerate(links[:3]):
        print(f"  Ссылка #{j+1}: {link.get('href')} - {link.get_text(strip=True)}")

# Ищем все ссылки, содержащие '/find-a-cpa/' в href
print("\n=== Поиск ссылок, содержащих '/find-a-cpa/' в href ===")
cpa_links = soup.find_all('a', href=lambda h: h and '/find-a-cpa/' in h)
print(f"Найдено ссылок: {len(cpa_links)}")

# Показываем первые 10 ссылок
for i, link in enumerate(cpa_links[:10]):
    print(f"Ссылка #{i+1}: {link.get('href')} - {link.get_text(strip=True)}")
    # Показываем родительские элементы до 3 уровней вверх
    parent = link.parent
    parent_info = []
    for _ in range(3):
        if parent:
            class_str = f".{parent.get('class')}" if parent.get('class') else ''
            id_str = f"#{parent.get('id')}" if parent.get('id') else ''
            parent_info.append(f"{parent.name}{class_str}{id_str}")
            parent = parent.parent
    print(f"  Родители: {' > '.join(parent_info)}")

# Ищем пагинацию
print("\n=== Поиск пагинации ===")
pagination_elements = [
    soup.find(class_='pagination'),
    soup.find(class_='pager'),
    soup.find(id='pagination'),
    soup.find(id='pager')
]

for i, pagination in enumerate(pagination_elements):
    if pagination:
        class_str = f".{pagination.get('class')}" if pagination.get('class') else ''
        print(f"Пагинация #{i+1} найдена: {pagination.name}{class_str}")
        links = pagination.find_all('a')
        print(f"  Содержит ссылок: {len(links)}")

        # Показываем все ссылки
        for j, link in enumerate(links):
            print(f"  Ссылка #{j+1}: {link.get('href')} - {link.get_text(strip=True)}")
    else:
        print(f"Пагинация #{i+1} не найдена")
