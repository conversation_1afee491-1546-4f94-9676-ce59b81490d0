import logging
import traceback
import re
from typing import Optional
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from ..utils import CaptchaSolver, random_sleep
from .navigation import Navigator # Для сохранения отладки
from ..config import SHORT_TIMEOUT_SEC # Таймаут для поиска iframe

class CaptchaHandler:
    """Обрабатывает reCAPTCHA v2 (Selenium)."""

    # --- Селекторы ---
    # Расширенные селекторы для поиска iframe капчи
    CAPTCHA_IFRAME_SELECTOR = 'iframe[src*="recaptcha"], iframe[src*="google.com/recaptcha"], iframe[title*="reCAPTCHA"]'
    # Селекторы для чекбокса внутри iframe
    CAPTCHA_CHECKBOX_SELECTOR = "div.recaptcha-checkbox-border, span.recaptcha-checkbox-checkmark, #recaptcha-anchor"
    # Селекторы для div с data-sitekey вне iframe
    G_RECAPTCHA_DIV_SELECTOR = 'div.g-recaptcha[data-sitekey], div[data-sitekey*="recaptcha"], div[class*="g-recaptcha"]'
    # Селекторы для textarea с ответом капчи вне iframe
    RESPONSE_TEXTAREA_SELECTOR = 'textarea#g-recaptcha-response, textarea[name="g-recaptcha-response"], textarea[id*="recaptcha"]'

    # --- Обновляем __init__ ---
    def __init__(self, driver: WebDriver, logger: logging.Logger,
                 solver: Optional[CaptchaSolver], navigator: Navigator, debug: bool = False):
        self.driver = driver
        self.logger = logger
        self.solver = solver
        self.navigator = navigator
        self.debug = debug
        # Короткое ожидание для поиска iframe
        self.wait_short = WebDriverWait(self.driver, SHORT_TIMEOUT_SEC)

    # --- Адаптируем handle ---
    def handle(self) -> Optional[bool]:
        """
        Проверяет и решает reCAPTCHA v2 (Selenium).
        Возвращает True (решена), False (ошибка), None (нет капчи/решателя).
        """
        if not self.driver: return False
        if not self.solver:
            self.logger.debug("Решатель капчи не настроен, проверка пропускается.")
            return None

        self.logger.debug("Проверка наличия reCAPTCHA v2 (Selenium)...")
        iframe_element: Optional[WebElement] = None
        try:
            # --- Поиск iframe капчи ---
            # Сначала просто ищем элемент без ожидания, чтобы не тратить время, если его нет
            iframes = self.driver.find_elements(By.CSS_SELECTOR, self.CAPTCHA_IFRAME_SELECTOR)
            if not iframes:
                 self.logger.debug(f"iframe reCAPTCHA ({self.CAPTCHA_IFRAME_SELECTOR}) не найден.")
                 return None # Капчи нет
            iframe_element = iframes[0]

            # Проверяем видимость iframe (не всегда надежно, но стоит попробовать)
            if not iframe_element.is_displayed():
                self.logger.debug(f"Найден iframe reCAPTCHA, но он не видим.")
                return None # Считаем, что капчи нет

            self.logger.info(f"Обнаружен видимый iframe reCAPTCHA ({self.CAPTCHA_IFRAME_SELECTOR}).")
            self.navigator.save_debug_info("captcha_iframe_found")

            # --- Извлечение Site Key ---
            # Передаем найденный iframe элемент
            site_key = self._extract_site_key(iframe_element)
            if not site_key:
                self.logger.error("Не удалось извлечь sitekey для reCAPTCHA.")
                self.navigator.save_debug_info("captcha_sitekey_not_found")
                return False

            # --- Решение капчи через сервис ---
            page_url = self.driver.current_url
            self.logger.info(f"Отправка reCAPTCHA v2 на решение (sitekey={site_key}, url={page_url})")
            captcha_token = self.solver.solve_recaptcha_v2(site_key, page_url)

            # --- Внедрение токена ---
            if captcha_token:
                if self._inject_token(captcha_token):
                    # --- Опциональный клик по чекбоксу после решения ---
                    # Иногда после внедрения токена нужно кликнуть чекбокс,
                    # чтобы сработал callback или форма стала активной.
                    # Это зависит от реализации сайта.
                    self.logger.debug("Попытка кликнуть по чекбоксу reCAPTCHA после внедрения токена...")
                    try:
                         # Ожидаем доступности iframe и переключаемся
                         self.wait_short.until(EC.frame_to_be_available_and_switch_to_it(
                              (By.CSS_SELECTOR, self.CAPTCHA_IFRAME_SELECTOR)
                         ))
                         self.logger.debug("Переключился в iframe reCAPTCHA для клика.")
                         checkbox = self.wait_short.until(
                              EC.element_to_be_clickable((By.CSS_SELECTOR, self.CAPTCHA_CHECKBOX_SELECTOR))
                         )
                         checkbox.click()
                         self.logger.info("Клик по чекбоксу reCAPTCHA выполнен.")
                         self.driver.switch_to.default_content() # ВОЗВРАЩАЕМСЯ ИЗ IFRAME
                         self.logger.debug("Вернулся из iframe reCAPTCHA.")
                         random_sleep(1, 2, "после клика по чекбоксу капчи")
                         return True # Считаем успех
                    except TimeoutException:
                         self.logger.warning("Не удалось найти/кликнуть чекбокс reCAPTCHA после решения.")
                         # Не считаем это фатальной ошибкой, токен внедрен
                         self.driver.switch_to.default_content() # Убедимся, что вышли из iframe
                         return True # Все равно возвращаем True, т.к. токен есть
                    except Exception as checkbox_err:
                         self.logger.error(f"Ошибка при клике по чекбоксу reCAPTCHA: {checkbox_err}", exc_info=self.debug)
                         self.driver.switch_to.default_content() # Убедимся, что вышли из iframe
                         return False # Ошибка при клике
                else:
                    # Ошибка при внедрении токена
                    return False
            else:
                self.logger.error("Не удалось получить токен от сервиса решения капчи.")
                self.navigator.save_debug_info("captcha_token_not_received")
                return False

        except NoSuchElementException:
            # Это может случиться, если iframe исчез между find_elements и is_displayed
             self.logger.debug(f"iframe reCAPTCHA ({self.CAPTCHA_IFRAME_SELECTOR}) исчез после обнаружения.")
             return None # Считаем, что капчи нет
        except Exception as e:
            self.logger.error(f"Ошибка при обработке reCAPTCHA: {e}", exc_info=self.debug)
            self.navigator.save_debug_info("captcha_handling_error")
            # Убедимся, что мы не застряли в iframe, если ошибка произошла после переключения
            try:
                self.driver.switch_to.default_content()
            except Exception:
                 pass
            return False

    # --- Адаптируем _extract_site_key ---
    def _extract_site_key(self, iframe_element: WebElement) -> Optional[str]:
        """Извлекает sitekey из элементов страницы или URL iframe (Selenium)."""
        site_key = None
        try:
            # --- Поиск в div.g-recaptcha (вне iframe) ---
            self.logger.debug("Поиск sitekey в div.g-recaptcha...")
            g_divs = self.driver.find_elements(By.CSS_SELECTOR, self.G_RECAPTCHA_DIV_SELECTOR)
            if g_divs:
                site_key = g_divs[0].get_attribute('data-sitekey')
                if site_key:
                    self.logger.info(f"Найден sitekey в div.g-recaptcha: {site_key}")
                    return site_key
                else:
                     self.logger.debug("Найден div.g-recaptcha, но атрибут data-sitekey пуст или отсутствует.")
            else:
                 self.logger.debug("div.g-recaptcha не найден.")

            # --- Поиск в URL iframe ---
            self.logger.debug("Поиск sitekey в URL iframe...")
            iframe_src = iframe_element.get_attribute('src')
            if iframe_src:
                match = re.search(r'k=([^&]+)', iframe_src)
                if match:
                    site_key = match.group(1)
                    self.logger.info(f"Найден sitekey в URL iframe: {site_key}")
                    return site_key
                else:
                     self.logger.debug("Параметр 'k' (sitekey) не найден в URL iframe.")
            else:
                 self.logger.debug("Атрибут 'src' у iframe пуст.")

        except Exception as e:
             self.logger.error(f"Ошибка при извлечении sitekey: {e}", exc_info=self.debug)

        # Если sitekey так и не найден
        if not site_key:
            self.logger.warning("Sitekey не найден ни в div.g-recaptcha, ни в URL iframe.")

        return site_key

    # --- Адаптируем _inject_token ---
    def _inject_token(self, token: str) -> bool:
        """Внедряет токен капчи в страницу с помощью JavaScript (Selenium)."""
        self.logger.info("Токен reCAPTCHA получен. Внедрение токена (Selenium)...")
        try:
            # --- Ищем textarea (вне iframe) ---
            response_textareas = self.driver.find_elements(By.CSS_SELECTOR, self.RESPONSE_TEXTAREA_SELECTOR)
            if not response_textareas:
                self.logger.error(f"Не найдено поле textarea {self.RESPONSE_TEXTAREA_SELECTOR} для внедрения токена.")
                self.navigator.save_debug_info("captcha_response_field_not_found")
                return False

            target_textarea = response_textareas[0]
            textarea_id = target_textarea.get_attribute('id')
            if not textarea_id:
                 textarea_id = 'g-recaptcha-response' # Используем стандартный ID, если атрибут пуст
                 self.logger.warning(f"Атрибут id у textarea {self.RESPONSE_TEXTAREA_SELECTOR} пуст, используем '{textarea_id}'.")

            # --- Внедряем токен через JS ---
            self.logger.debug(f"Внедрение токена в элемент с ID '{textarea_id}'...")
            # Используем setAttribute или innerHTML/value
            # setAttribute('value', token) может быть надежнее
            js_code_inject = f"document.getElementById('{textarea_id}').innerHTML = arguments[0]; document.getElementById('{textarea_id}').value = arguments[0];"
            self.driver.execute_script(js_code_inject, token)
            self.logger.info(f"Токен внедрен в textarea#{textarea_id}.")

            # --- Ищем div для вызова callback (вне iframe) ---
            callback_func_name: Optional[str] = None
            g_divs = self.driver.find_elements(By.CSS_SELECTOR, self.G_RECAPTCHA_DIV_SELECTOR)
            if g_divs:
                callback_func_name = g_divs[0].get_attribute('data-callback')

            # --- Вызываем callback, если он есть ---
            if callback_func_name:
                self.logger.info(f"Попытка вызова callback функции: {callback_func_name}('{token[:10]}...')")
                try:
                    # Передаем токен как аргумент в JS
                    js_code_callback = f"if(typeof {callback_func_name} === 'function') {{ {callback_func_name}(arguments[0]); }} else {{ console.warn('Callback function {callback_func_name} not found or not a function.'); }}"
                    self.driver.execute_script(js_code_callback, token)
                    self.logger.info("Попытка вызова callback функции через execute_script выполнена.")
                except Exception as callback_err:
                    self.logger.warning(f"Не удалось вызвать callback функцию {callback_func_name} через execute_script: {callback_err}")
            else:
                self.logger.debug("Callback функция для reCAPTCHA не найдена в атрибуте data-callback.")

            random_sleep(1, 2, "после внедрения токена и вызова callback")
            return True
        except Exception as e:
            self.logger.error(f"Ошибка при внедрении токена reCAPTCHA: {e}", exc_info=self.debug)
            self.navigator.save_debug_info("captcha_injection_error")
            return False
