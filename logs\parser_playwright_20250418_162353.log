2025-04-18 16:23:53,073 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_162353.log
2025-04-18 16:23:53,078 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 16:23:53,080 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-18 16:23:53,080 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 16:23:53,081 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': None}
2025-04-18 16:23:53,082 - CpaQuebecParser - INFO - ==================================================
2025-04-18 16:23:53,082 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-18 16:24:11,720 - CpaQuebecParser - DEBUG - Пауза на 2.20 сек. (после driver.get)
2025-04-18 16:24:19,359 - CpaQuebecParser - DEBUG - Пауза на 0.47 сек. (после scroll к кнопке cookie)
2025-04-18 16:24:32,362 - CpaQuebecParser - DEBUG - Пауза на 0.98 сек. (после клика Reset (XPath))
2025-04-18 16:24:35,689 - CpaQuebecParser - DEBUG - Пауза на 0.85 сек. (после очистки формы)
2025-04-18 16:25:50,067 - CpaQuebecParser - DEBUG - Пауза на 3.18 сек. (между категориями, после Individuals)
2025-04-18 16:25:56,072 - CpaQuebecParser - DEBUG - Пауза на 1.66 сек. (после driver.get)
2025-04-18 16:27:29,277 - CpaQuebecParser - DEBUG - Пауза на 0.95 сек. (после клика Reset (XPath))
2025-04-18 16:27:34,756 - CpaQuebecParser - DEBUG - Пауза на 0.58 сек. (после очистки формы)
2025-04-18 16:28:09,876 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
