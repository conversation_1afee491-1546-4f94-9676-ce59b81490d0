# Интеграция Stagehand в CPA Quebec Parser

---

## Что такое Stagehand

[Stagehand](https://github.com/browserbase/stagehand) — это прокси-сервер для Playwright, который позволяет:

- Подключаться к локальным или облачным браузерам через Chrome DevTools Protocol (CDP)
- Масштабировать и изолировать браузеры
- Использовать Browserbase (облако браузеров) или работать без него

---

## Чем интеграция Stagehand лучше прямого запуска Playwright

### Преимущества

1. **Гибкость подключения**

   - Можно легко переключаться между локальным браузером и облачным, меняя только URL
   - Не нужно менять код

2. **Изоляция и стабильность**

   - Браузеры запускаются отдельно от основного процесса Python
   - Меньше сбоев и зависаний

3. **Упрощение отладки**

   - Stagehand можно запускать отдельно, с логами
   - Проще отслеживать ошибки и трафик

4. **Масштабирование**

   - Можно запускать десятки браузеров в облаке Browserbase
   - Или использовать несколько локальных браузеров

5. **Совместимость**

   - Используется стандартный CDP
   - Можно подключать Chrome DevTools, Puppeteer, Playwright

---

### Сравнение с прежней реализацией

| Старый подход                     | Новый с Stagehand                                     |
|----------------------------------|-------------------------------------------------------|
| Локальный запуск браузера        | Подключение к Stagehand (локальный или облачный)      |
| Жестко зашит `launch()`          | Гибко: `connect_over_cdp()` при наличии URL           |
| Нет возможности масштабирования  | Возможность масштабировать через Browserbase          |
| Сложнее отлаживать               | Проще отлаживать, можно запускать Stagehand отдельно  |
| Браузер и Python в одном процессе| Изоляция браузера через Stagehand                     |

---

## Использование без API-ключа

Stagehand можно запускать **без** API-ключа Browserbase:

```bash
npx stagehand --port=9222
```

В `.env`:

```
STAGEHAND_URL=http://localhost:9222
```

В этом случае Stagehand работает как локальный прокси для браузера.

---

## Использование с Browserbase (облако)

Чтобы запускать браузеры в облаке:

```bash
npx stagehand --api-key=YOUR_BROWSERBASE_API_KEY --port=9222
```

В `.env`:

```
STAGEHAND_URL=http://localhost:9222
```

---

## Итог

- Интеграция Stagehand повышает гибкость, стабильность и масштабируемость
- Можно работать как с локальными, так и с облачными браузерами
- Переключение — без изменения кода, только меняя параметры запуска Stagehand
