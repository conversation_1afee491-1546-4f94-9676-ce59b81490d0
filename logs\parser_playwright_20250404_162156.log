2025-04-04 16:21:56,760 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_162156.log
2025-04-04 16:21:56,763 - C<PERSON><PERSON><PERSON>becParser - INFO - ==================================================
2025-04-04 16:21:56,763 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 16:21:56,764 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 16:21:56,764 - CpaQuebecParser - INFO - ==================================================
2025-04-04 16:21:56,764 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 16:21:56,764 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): CpaQuebecParser.__init__() got an unexpected keyword argument 'user_agent'
2025-04-04 16:21:56,765 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 82, in run_parser
    with CpaQuebecParser(
TypeError: CpaQuebecParser.__init__() got an unexpected keyword argument 'user_agent'

2025-04-04 16:21:56,765 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
