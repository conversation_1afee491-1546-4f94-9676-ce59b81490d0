2025-04-19 13:42:01,470 - C<PERSON>Q<PERSON><PERSON>cParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250419_134201.log
2025-04-19 13:42:01,475 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-19 13:42:01,476 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-19 13:42:01,476 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-19 13:42:01,476 - <PERSON><PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': 'Individuals'}
2025-04-19 13:42:01,477 - CpaQuebecParser - INFO - ==================================================
2025-04-19 13:42:01,477 - CpaQuebecParser - INFO - Режим работы: Парсинг одной категории: Individuals.
2025-04-19 13:42:12,305 - CpaQuebecParser - DEBUG - Пауза на 2.46 сек. (после driver.get)
2025-04-19 13:42:14,923 - CpaQuebecParser - DEBUG - Пауза на 0.41 сек. (после scroll к кнопке cookie)
2025-04-19 13:42:57,756 - CpaQuebecParser - DEBUG - Пауза на 0.95 сек. (после клика Reset (XPath))
2025-04-19 13:43:00,748 - CpaQuebecParser - DEBUG - Пауза на 1.00 сек. (после очистки формы)
2025-04-19 13:44:13,503 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
