2025-04-04 19:50:03,360 - C<PERSON>Q<PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_195003.log
2025-04-04 19:50:03,362 - <PERSON><PERSON>Q<PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 19:50:03,363 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-04 19:50:03,363 - C<PERSON>Q<PERSON>becParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-04-04 19:50:03,363 - C<PERSON>Q<PERSON>becParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 10000, 'page_timeout': 60000, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-04-04 19:50:03,363 - CpaQuebecParser - INFO - ==================================================
2025-04-04 19:50:03,363 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-04-04 19:50:08,218 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-04-04 19:50:08,481 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
