2025-04-04 20:56:41,888 - C<PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_205641.log
2025-04-04 20:56:41,894 - <PERSON><PERSON>Q<PERSON>becParser - DEBUG - Режим отладки DEBUG включен.
2025-04-04 20:56:41,895 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:56:41,896 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:56:41,896 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None}
2025-04-04 20:56:41,897 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:56:41,897 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-04 20:56:55,151 - CpaQuebecParser - DEBUG - Пауза на 1.55 сек. (после driver.get)
2025-04-04 20:56:56,882 - CpaQuebecParser - DEBUG - Пауза на 0.58 сек. (после scroll к кнопке cookie)
2025-04-04 20:57:09,076 - CpaQuebecParser - DEBUG - Пауза на 0.96 сек. (после клика Reset (XPath))
2025-04-04 20:57:13,992 - CpaQuebecParser - DEBUG - Пауза на 0.97 сек. (после очистки формы)
2025-04-04 20:57:59,617 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
