2025-03-26 19:50:40,860 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195040.log
2025-03-26 19:50:40,862 - C<PERSON><PERSON><PERSON>becParser - INFO - ==================================================
2025-03-26 19:50:40,862 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-03-26 19:50:40,863 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 30, 'page_timeout': 60, 'get_details': True, 'by_category': True}
2025-03-26 19:50:40,863 - CpaQuebecParser - INFO - ==================================================
2025-03-26 19:50:40,863 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-03-26 19:50:40,863 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250326_195040.log
2025-03-26 19:50:40,863 - CpaQuebecParser - INFO - Инициализация парсера CPA Quebec (Playwright)
2025-03-26 19:50:40,864 - CpaQuebecParser - WARNING - API ключ для 2Captcha не найден. Решение капчи будет пропущено.
2025-03-26 19:50:40,864 - CpaQuebecParser - INFO - Запуск браузера Chromium (Headless: True) через Playwright...
2025-03-26 19:50:41,481 - CpaQuebecParser - INFO - Браузер Chromium запущен.
2025-03-26 19:50:41,484 - CpaQuebecParser - INFO - Используется User-Agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-03-26 19:50:41,515 - CpaQuebecParser - INFO - Контекст браузера создан.
2025-03-26 19:50:41,585 - CpaQuebecParser - INFO - Новая страница создана.
2025-03-26 19:50:41,590 - CpaQuebecParser - INFO - Добавлен init script для скрытия navigator.webdriver.
2025-03-26 19:50:41,590 - CpaQuebecParser - INFO - ============================== ЗАПУСК ПАРСИНГА (Playwright) ==============================
2025-03-26 19:50:41,591 - CpaQuebecParser - INFO - Режим: Парсинг по категориям клиентов.
2025-03-26 19:50:41,591 - CpaQuebecParser - INFO - Запуск парсинга по категориям (Playwright): Arts and culture organizations, Co-ownership syndicates, Elderly individuals, Executives, Farmers, Indigenous communities, Individuals, Large businesses, Medium-sized businesses, Non-profit organizations, Professionals, Self-employed workers, Small businesses, Sports organizations, Start-ups
2025-03-26 19:50:41,591 - CpaQuebecParser - INFO - --- Обработка категории: Arts and culture organizations (1/15) ---
2025-03-26 19:50:41,591 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:42,062 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:42,063 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:42,372 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195042.png
2025-03-26 19:50:42,379 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195042.html
2025-03-26 19:50:42,379 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Arts and culture organizations'. Пропускаем.
2025-03-26 19:50:42,379 - CpaQuebecParser - INFO - --- Обработка категории: Co-ownership syndicates (2/15) ---
2025-03-26 19:50:42,379 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:42,541 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:42,541 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:42,760 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195042.png
2025-03-26 19:50:42,766 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195042.html
2025-03-26 19:50:42,766 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Co-ownership syndicates'. Пропускаем.
2025-03-26 19:50:42,766 - CpaQuebecParser - INFO - --- Обработка категории: Elderly individuals (3/15) ---
2025-03-26 19:50:42,766 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:42,916 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:42,916 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:43,179 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195042.png
2025-03-26 19:50:43,186 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195042.html
2025-03-26 19:50:43,186 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Elderly individuals'. Пропускаем.
2025-03-26 19:50:43,187 - CpaQuebecParser - INFO - --- Обработка категории: Executives (4/15) ---
2025-03-26 19:50:43,187 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:43,339 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:43,340 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:43,599 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195043.png
2025-03-26 19:50:43,604 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195043.html
2025-03-26 19:50:43,605 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Executives'. Пропускаем.
2025-03-26 19:50:43,605 - CpaQuebecParser - INFO - --- Обработка категории: Farmers (5/15) ---
2025-03-26 19:50:43,605 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:43,758 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:43,758 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:44,013 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195043.png
2025-03-26 19:50:44,018 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195043.html
2025-03-26 19:50:44,019 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Farmers'. Пропускаем.
2025-03-26 19:50:44,019 - CpaQuebecParser - INFO - --- Обработка категории: Indigenous communities (6/15) ---
2025-03-26 19:50:44,020 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:44,173 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:44,173 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:44,399 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195044.png
2025-03-26 19:50:44,404 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195044.html
2025-03-26 19:50:44,404 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Indigenous communities'. Пропускаем.
2025-03-26 19:50:44,404 - CpaQuebecParser - INFO - --- Обработка категории: Individuals (7/15) ---
2025-03-26 19:50:44,404 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:44,568 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:44,568 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:44,837 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195044.png
2025-03-26 19:50:44,846 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195044.html
2025-03-26 19:50:44,846 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Individuals'. Пропускаем.
2025-03-26 19:50:44,846 - CpaQuebecParser - INFO - --- Обработка категории: Large businesses (8/15) ---
2025-03-26 19:50:44,847 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:45,000 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:45,000 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:45,213 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195045.png
2025-03-26 19:50:45,217 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195045.html
2025-03-26 19:50:45,217 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Large businesses'. Пропускаем.
2025-03-26 19:50:45,217 - CpaQuebecParser - INFO - --- Обработка категории: Medium-sized businesses (9/15) ---
2025-03-26 19:50:45,217 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:45,376 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:45,377 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:45,609 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195045.png
2025-03-26 19:50:45,615 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195045.html
2025-03-26 19:50:45,615 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Medium-sized businesses'. Пропускаем.
2025-03-26 19:50:45,615 - CpaQuebecParser - INFO - --- Обработка категории: Non-profit organizations (10/15) ---
2025-03-26 19:50:45,615 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:45,772 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:45,773 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:46,016 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195045.png
2025-03-26 19:50:46,024 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195045.html
2025-03-26 19:50:46,025 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Non-profit organizations'. Пропускаем.
2025-03-26 19:50:46,025 - CpaQuebecParser - INFO - --- Обработка категории: Professionals (11/15) ---
2025-03-26 19:50:46,025 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:46,178 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:46,179 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:46,398 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195046.png
2025-03-26 19:50:46,403 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195046.html
2025-03-26 19:50:46,403 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Professionals'. Пропускаем.
2025-03-26 19:50:46,403 - CpaQuebecParser - INFO - --- Обработка категории: Self-employed workers (12/15) ---
2025-03-26 19:50:46,403 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:46,563 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:46,563 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:46,785 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195046.png
2025-03-26 19:50:46,793 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195046.html
2025-03-26 19:50:46,794 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Self-employed workers'. Пропускаем.
2025-03-26 19:50:46,794 - CpaQuebecParser - INFO - --- Обработка категории: Small businesses (13/15) ---
2025-03-26 19:50:46,794 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:46,947 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:46,947 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:47,169 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195046.png
2025-03-26 19:50:47,175 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195046.html
2025-03-26 19:50:47,175 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Small businesses'. Пропускаем.
2025-03-26 19:50:47,175 - CpaQuebecParser - INFO - --- Обработка категории: Sports organizations (14/15) ---
2025-03-26 19:50:47,175 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:47,339 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:47,340 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:47,561 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195047.png
2025-03-26 19:50:47,567 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195047.html
2025-03-26 19:50:47,568 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Sports organizations'. Пропускаем.
2025-03-26 19:50:47,568 - CpaQuebecParser - INFO - --- Обработка категории: Start-ups (15/15) ---
2025-03-26 19:50:47,568 - CpaQuebecParser - INFO - Переход на URL: https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ (wait: load)
2025-03-26 19:50:47,731 - CpaQuebecParser - INFO - Страница https://cpaquebec.ca/en/find-a-cpa/cpa-directory/ загружена со статусом 403.
2025-03-26 19:50:47,732 - CpaQuebecParser - ERROR - Ошибка загрузки страницы: статус 403
2025-03-26 19:50:47,961 - CpaQuebecParser - INFO - Сохранен отладочный скриншот: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195047.png
2025-03-26 19:50:47,966 - CpaQuebecParser - INFO - Сохранен отладочный HTML: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/debug/navigate_error_403_https_cpaquebec.ca_en_find-a-cpa_cpa-directory_20250326_195047.html
2025-03-26 19:50:47,967 - CpaQuebecParser - ERROR - Не удалось загрузить базовую страницу для категории 'Start-ups'. Пропускаем.
2025-03-26 19:50:47,967 - CpaQuebecParser - INFO - Парсинг по категориям завершен. Всего найдено 0 уникальных записей.
2025-03-26 19:50:47,967 - CpaQuebecParser - WARNING - Поиск не дал результатов или произошла ошибка.
2025-03-26 19:50:47,968 - CpaQuebecParser - WARNING - Нет данных для сохранения.
2025-03-26 19:50:47,968 - CpaQuebecParser - INFO - Общее время парсинга (Playwright): 6.38 секунд.
2025-03-26 19:50:47,969 - CpaQuebecParser - INFO - ============================== ЗАВЕРШЕНИЕ ПАРСИНГА (Playwright) ==============================
2025-03-26 19:50:47,969 - CpaQuebecParser - INFO - Парсинг (Playwright) завершен. Найдено и сохранено записей: 0
2025-03-26 19:50:47,970 - CpaQuebecParser - INFO - Закрытие браузера и Playwright...
2025-03-26 19:50:48,049 - CpaQuebecParser - INFO - Браузер Playwright закрыт.
2025-03-26 19:50:48,063 - CpaQuebecParser - INFO - Playwright остановлен.
2025-03-26 19:50:48,064 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 0
