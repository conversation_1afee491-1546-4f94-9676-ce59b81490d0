2025-04-04 20:26:06,177 - C<PERSON>QuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_202606.log
2025-04-04 20:26:06,182 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 20:26:06,183 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:26:06,183 - C<PERSON>QuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False}
2025-04-04 20:26:06,183 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:26:06,183 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-04 20:26:06,184 - CpaQuebecParser - CRITICAL - Критическая ошибка выполнения (Selenium): CpaQuebecParser.__init__() got an unexpected keyword argument 'wait_timeout'
2025-04-04 20:26:06,185 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/main.py", line 82, in run_parser
    with CpaQuebecParser(
TypeError: CpaQuebecParser.__init__() got an unexpected keyword argument 'wait_timeout'

2025-04-04 20:26:06,185 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 4
