2025-03-27 12:20:50,206 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250327_122050.log
2025-03-27 12:20:50,210 - C<PERSON>QuebecParser - INFO - ==================================================
2025-03-27 12:20:50,212 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Playwright)
2025-03-27 12:20:50,212 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'user_agent': '', 'wait_timeout': 30, 'page_timeout': 60, 'get_details': True, 'by_category': True, 'accepting_new_clients': False}
2025-03-27 12:20:50,212 - CpaQuebecParser - INFO - ==================================================
2025-03-27 12:20:50,212 - CpaQuebecParser - INFO - Режим работы: Парсинг по категориям.
2025-03-27 12:20:50,212 - CpaQuebecParser - CRITICAL - Непредвиденная критическая ошибка (Playwright): CpaQuebecParser() takes no arguments
2025-03-27 12:20:50,213 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/main.py", line 77, in run_parser
    with CpaQuebecParser(
TypeError: CpaQuebecParser() takes no arguments

2025-03-27 12:20:50,214 - CpaQuebecParser - INFO - Завершение работы скрипта (Playwright) с кодом: 4
