2025-04-18 16:35:00,561 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_163500.log
2025-04-18 16:35:00,564 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 16:35:00,565 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-18 16:35:00,566 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 16:35:00,568 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': None}
2025-04-18 16:35:00,568 - CpaQuebecParser - INFO - ==================================================
2025-04-18 16:35:00,569 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-18 16:35:19,703 - CpaQuebecParser - DEBUG - Пауза на 2.39 сек. (после driver.get)
2025-04-18 16:35:22,302 - CpaQuebecParser - DEBUG - Пауза на 0.68 сек. (после scroll к кнопке cookie)
2025-04-18 16:35:34,960 - CpaQuebecParser - DEBUG - Пауза на 0.91 сек. (после клика Reset (XPath))
2025-04-18 16:35:38,389 - CpaQuebecParser - DEBUG - Пауза на 0.80 сек. (после очистки формы)
2025-04-18 16:36:52,980 - CpaQuebecParser - DEBUG - Пауза на 3.36 сек. (между категориями, после Individuals)
2025-04-18 16:37:03,404 - CpaQuebecParser - DEBUG - Пауза на 2.29 сек. (после driver.get)
2025-04-18 16:38:41,171 - CpaQuebecParser - DEBUG - Пауза на 0.60 сек. (после клика Reset (XPath))
2025-04-18 16:38:49,370 - CpaQuebecParser - DEBUG - Пауза на 0.89 сек. (после очистки формы)
2025-04-18 16:39:13,040 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
