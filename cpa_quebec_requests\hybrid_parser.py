#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import logging
import json
from typing import Dict, List, Any, Optional, Set
from urllib.parse import urljoin
from dotenv import load_dotenv

# Импорты для Selenium
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.common.exceptions import TimeoutException, WebDriverException

# Импорты из существующих модулей
from cpa_quebec.config import (
    CLIENT_CATEGORIES_TO_PARSE, OUTPUT_DIR, DEBUG_DIR,
    BASE_URL, MAX_RETRY_ATTEMPTS, RETRY_DELAY
)
from cpa_quebec.utils import setup_logging, random_sleep, CaptchaSolver
from cpa_quebec.selenium_parser.browser import BrowserManager
from cpa_quebec.selenium_parser.navigation import Navigator
from cpa_quebec.selenium_parser.form_handler_new import <PERSON>H<PERSON><PERSON>
from cpa_quebec.selenium_parser.captcha import <PERSON><PERSON><PERSON><PERSON><PERSON>
from cpa_quebec.selenium_parser.saver import DataSaver

# Импорты из HTTP-парсера
from .updated_parser import CpaQuebecUpdatedParser
from .client import CpaQuebecClient

class HybridCpaQuebecParser:
    """
    Гибридный парсер для CPA Quebec Directory.
    Использует Selenium для обхода капчи, а затем переключается на HTTP-запросы
    для более быстрого сбора данных.
    """
    def __init__(
        self,
        debug: bool = False,
        headless: bool = True,
        output_dir: str = OUTPUT_DIR,
        solve_captcha: bool = True
    ):
        """
        Инициализирует гибридный парсер.

        Args:
            debug: Включить ли режим отладки.
            headless: Запускать ли браузер в фоновом режиме.
            output_dir: Директория для сохранения результатов.
            solve_captcha: Пытаться ли решать капчу.
        """
        self.debug = debug
        self.headless = headless
        self.output_dir = output_dir
        self.solve_captcha = solve_captcha

        # Настройка логгера
        self.logger = logging.getLogger(__name__)
        log_level = logging.DEBUG if self.debug else logging.INFO
        self.logger.setLevel(log_level)

        # Создаем директории для отладки
        self.debug_dir = os.path.join(DEBUG_DIR, "hybrid_parser")
        self.screenshots_dir = os.path.join(self.debug_dir, "screenshots")
        os.makedirs(self.debug_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)

        # Загрузка API ключа капчи
        load_dotenv()
        api_key = os.getenv("ANTICAPTCHA_API_KEY")
        self.captcha_solver: Optional[CaptchaSolver] = None
        if solve_captcha:
            if api_key:
                self.captcha_solver = CaptchaSolver(api_key, self.logger)
                self.logger.info("Anti-captcha.com solver инициализирован.")
            else:
                self.logger.warning("Решение капчи включено, но ключ ANTICAPTCHA_API_KEY не найден в .env файле.")
        else:
            self.logger.info("Решение капчи отключено.")

        # Компоненты Selenium
        self.browser_manager: Optional[BrowserManager] = None
        self.driver: Optional[WebDriver] = None
        self.navigator: Optional[Navigator] = None
        self.form_handler: Optional[FormHandler] = None
        self.captcha_handler: Optional[CaptchaHandler] = None

        # HTTP-компоненты
        self.http_client: Optional[CpaQuebecClient] = None
        self.http_parser: Optional[CpaQuebecUpdatedParser] = None

        # Компонент для сохранения данных
        self.saver: Optional[DataSaver] = None

        # Результаты
        self.results: List[Dict] = []
        self.processed_urls: Set[str] = set()

        self.logger.debug("HybridCpaQuebecParser инициализирован.")

    def __enter__(self):
        """Запускает драйвер и инициализирует компоненты."""
        self.logger.info("Запуск менеджера браузера...")
        self.browser_manager = BrowserManager(
            headless=self.headless,
            logger=self.logger,
            page_load_timeout=60,
            implicit_wait=5
        )
        try:
            self.browser_manager.__enter__()
            self._initialize_components()
            self.logger.info("Менеджер браузера запущен, компоненты инициализированы.")
            return self
        except Exception as e:
            self.logger.critical(f"Ошибка при запуске драйвера или инициализации компонентов: {e}", exc_info=True)
            if self.browser_manager:
                self.browser_manager.__exit__(type(e), e, e.__traceback__)
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Останавливает драйвер."""
        self.logger.info("Остановка менеджера браузера...")
        if self.browser_manager:
            self.browser_manager.__exit__(exc_type, exc_val, exc_tb)
            self.logger.info("Менеджер браузера остановлен.")

    def _initialize_components(self):
        """Инициализирует все компоненты парсера после запуска драйвера."""
        if not self.browser_manager or not self.browser_manager.driver:
            self.logger.error("Невозможно инициализировать компоненты: драйвер Selenium не запущен.")
            raise RuntimeError("Драйвер Selenium не инициализирован.")

        self.driver = self.browser_manager.get_driver()
        if not self.driver:
            self.logger.error("Не удалось получить активный драйвер от BrowserManager.")
            raise RuntimeError("Не удалось получить драйвер Selenium.")

        self.logger.debug("Инициализация компонентов парсера...")

        # Инициализация компонентов Selenium
        self.navigator = Navigator(
            self.driver,
            self.logger,
            self.debug,
            self.debug_dir,
            self.screenshots_dir,
            30  # navigation_timeout
        )
        self.form_handler = FormHandler(self.driver, self.logger, self.debug)
        self.captcha_handler = CaptchaHandler(
            self.driver,
            self.logger,
            self.captcha_solver,
            self.navigator,
            self.debug
        )

        # Инициализация компонента для сохранения данных
        self.saver = DataSaver(self.logger, self.output_dir)

        self.logger.debug("Компоненты парсера инициализированы.")

    def _initialize_http_components(self):
        """
        Инициализирует HTTP-компоненты после успешного решения капчи.
        Передает cookies и заголовки из Selenium в HTTP-клиент.
        """
        self.logger.info("Инициализация HTTP-компонентов...")

        # Получаем cookies из Selenium
        selenium_cookies = self.driver.get_cookies()
        cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}

        # Получаем User-Agent из Selenium
        user_agent = self.driver.execute_script("return navigator.userAgent;")

        # Создаем HTTP-клиент с cookies и User-Agent из Selenium
        self.http_client = CpaQuebecClient(
            base_url=BASE_URL,
            default_headers={'User-Agent': user_agent}
        )

        # Устанавливаем cookies в HTTP-клиент
        for name, value in cookies_dict.items():
            self.http_client.session.cookies.set(name, value)

        # Инициализируем HTTP-парсер
        self.http_parser = CpaQuebecUpdatedParser(
            debug=self.debug,
            output_dir=self.output_dir
        )

        # Передаем сессию из HTTP-клиента в HTTP-парсер
        self.http_parser.session = self.http_client.session

        self.logger.info("HTTP-компоненты инициализированы.")
        return True

    def _solve_captcha(self) -> bool:
        """
        Открывает страницу поиска и решает капчу с помощью Selenium.

        Returns:
            True, если капча успешно решена или не требуется, иначе False.
        """
        self.logger.info("Открытие страницы поиска и решение капчи...")

        try:
            # Открываем страницу поиска
            if not self.navigator.goto(BASE_URL):
                self.logger.error(f"Не удалось загрузить базовый URL {BASE_URL}.")
                return False

            self.navigator.save_debug_info("captcha_solve_after_goto")
            random_sleep(1, 2, "после загрузки страницы")

            # Проверяем наличие капчи и решаем ее
            captcha_result = self.captcha_handler.handle()

            if captcha_result is False:  # Явная ошибка решения
                self.logger.error("Ошибка при обработке капчи.")
                return False
            elif captcha_result is True:
                self.logger.info("Капча успешно обработана.")
                self.navigator.save_debug_info("captcha_solve_success")
                random_sleep(3, 5, "после обработки капчи")
                return True
            else:
                self.logger.info("Капча не обнаружена или не требует решения.")
                random_sleep(1, 2, "после проверки наличия капчи")
                return True

        except Exception as e:
            self.logger.error(f"Ошибка при решении капчи: {e}", exc_info=True)
            self.navigator.save_debug_info("captcha_solve_error")
            return False

    def _extract_form_fields(self) -> Dict[str, str]:
        """
        Извлекает поля формы поиска с помощью Selenium.

        Returns:
            Словарь с полями формы.
        """
        self.logger.info("Извлечение полей формы поиска...")

        try:
            # Получаем HTML-код страницы
            html = self.driver.page_source

            # Используем метод из HTTP-парсера для извлечения полей формы
            form_fields = self.http_parser._extract_form_fields(html)

            self.logger.info(f"Извлечено {len(form_fields)} полей формы.")
            return form_fields

        except Exception as e:
            self.logger.error(f"Ошибка при извлечении полей формы: {e}", exc_info=True)
            return {}

    def parse_category(self, category: str, get_details: bool = True) -> List[Dict[str, Any]]:
        """
        Парсит профили CPA для указанной категории клиентов.

        Args:
            category: Категория клиентов для фильтрации.
            get_details: Получать ли детальную информацию о профилях.

        Returns:
            Список словарей с данными профилей.
        """
        if category not in CLIENT_CATEGORIES_TO_PARSE:
            self.logger.error(f"Категория '{category}' не найдена. Доступные категории: {CLIENT_CATEGORIES_TO_PARSE}")
            return []

        self.logger.info(f"Парсинг категории '{category}'...")

        # Шаг 1: Решаем капчу с помощью Selenium
        if not self._solve_captcha():
            self.logger.error("Не удалось решить капчу. Парсинг невозможен.")
            return []

        # Шаг 2: Инициализируем HTTP-компоненты с cookies из Selenium
        if not self._initialize_http_components():
            self.logger.error("Не удалось инициализировать HTTP-компоненты.")
            return []

        # Шаг 3: Извлекаем поля формы с помощью Selenium
        form_fields = self._extract_form_fields()
        if not form_fields:
            self.logger.error("Не удалось извлечь поля формы.")
            return []

        # Шаг 4: Передаем поля формы в HTTP-парсер
        self.http_parser.form_fields = form_fields

        # Шаг 5: Используем HTTP-парсер для парсинга категории
        try:
            profiles = self.http_parser.parse_category(category)
            self.logger.info(f"Найдено профилей: {len(profiles)}")

            # Шаг 6: Если нужны детали, получаем их для каждого профиля
            if get_details and profiles:
                profiles_with_details = []
                for i, profile in enumerate(profiles):
                    self.logger.info(f"Получение деталей для профиля {i+1}/{len(profiles)}: {profile.get('name', 'Unknown')}")
                    profile_url = profile.get('profile_url')
                    if profile_url:
                        try:
                            # Используем HTTP-запрос для получения деталей
                            response = self.http_parser.session.get(profile_url, timeout=30)
                            response.raise_for_status()

                            # Извлекаем детали из HTML
                            details = self._extract_profile_details(response.text, profile_url)
                            profile.update(details)

                            # Пауза между запросами
                            random_sleep(1, 2, "между запросами деталей профилей")

                        except Exception as e:
                            self.logger.warning(f"Ошибка при получении деталей для профиля {profile_url}: {e}")

                    profiles_with_details.append(profile)

                profiles = profiles_with_details

            return profiles

        except Exception as e:
            self.logger.error(f"Ошибка при парсинге категории '{category}': {e}", exc_info=True)
            return []

    def _extract_profile_details(self, html: str, profile_url: str) -> Dict[str, Any]:
        """
        Извлекает детальную информацию о профиле CPA из HTML-страницы.

        Args:
            html: HTML-код страницы профиля.
            profile_url: URL профиля.

        Returns:
            Словарь с детальной информацией о профиле.
        """
        from bs4 import BeautifulSoup
        from cpa_quebec.utils import extract_emails, extract_phones, clean_text

        details = {
            'profile_url': profile_url,
            'parsed_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Извлекаем основную информацию
            main_info = soup.find('div', class_='main-info')
            if main_info:
                # Имя
                name_elem = main_info.find('h1')
                if name_elem:
                    details['full_name'] = clean_text(name_elem.get_text())

                # Должность
                title_elem = main_info.find('h2')
                if title_elem:
                    details['title'] = clean_text(title_elem.get_text())

                # Компания
                company_elem = main_info.find('h3')
                if company_elem:
                    details['company'] = clean_text(company_elem.get_text())

            # Извлекаем контактную информацию
            contact_info = soup.find('div', class_='contact-info')
            if contact_info:
                # Адрес
                address_elem = contact_info.find('address')
                if address_elem:
                    details['address'] = clean_text(address_elem.get_text())

                # Телефон
                phone_elem = contact_info.find('a', href=lambda h: h and 'tel:' in h)
                if phone_elem:
                    details['phone'] = clean_text(phone_elem.get_text())

                # Email
                email_elem = contact_info.find('a', href=lambda h: h and 'mailto:' in h)
                if email_elem:
                    details['email'] = email_elem.get('href').replace('mailto:', '')

                # Веб-сайт
                website_elem = contact_info.find('a', href=lambda h: h and ('http://' in h or 'https://' in h))
                if website_elem:
                    details['website'] = website_elem.get('href')

            # Извлекаем дополнительную информацию
            sections = soup.find_all('section', class_='profile-section')
            for section in sections:
                # Заголовок секции
                heading = section.find('h2')
                if heading:
                    section_title = clean_text(heading.get_text()).lower()

                    # Содержимое секции
                    content = section.find('div', class_='section-content')
                    if content:
                        section_text = clean_text(content.get_text())

                        # Определяем тип секции по заголовку
                        if 'services' in section_title:
                            details['services'] = section_text
                        elif 'expertise' in section_title:
                            details['expertise'] = section_text
                        elif 'clients' in section_title or 'clientele' in section_title:
                            details['clients'] = section_text
                        elif 'languages' in section_title:
                            details['languages'] = section_text
                        elif 'education' in section_title:
                            details['education'] = section_text
                        elif 'about' in section_title:
                            details['about'] = section_text

            # Извлекаем все email и телефоны со страницы
            all_text = soup.get_text()
            emails = extract_emails(all_text)
            if emails and 'email' not in details:
                details['email'] = emails[0]

            phones = extract_phones(all_text)
            if phones and 'phone' not in details:
                details['phone'] = phones[0]

        except Exception as e:
            self.logger.warning(f"Ошибка при извлечении деталей профиля {profile_url}: {e}")

        return details

    def parse_all(self, get_details: bool = True) -> Dict[str, List[Dict[str, Any]]]:
        """
        Парсит профили CPA для всех категорий клиентов.

        Args:
            get_details: Получать ли детальную информацию о профилях.

        Returns:
            Словарь {категория: список профилей}.
        """
        results = {}

        # Шаг 1: Решаем капчу с помощью Selenium
        if not self._solve_captcha():
            self.logger.error("Не удалось решить капчу. Парсинг невозможен.")
            return results

        # Шаг 2: Инициализируем HTTP-компоненты с cookies из Selenium
        if not self._initialize_http_components():
            self.logger.error("Не удалось инициализировать HTTP-компоненты.")
            return results

        # Шаг 3: Извлекаем поля формы с помощью Selenium
        form_fields = self._extract_form_fields()
        if not form_fields:
            self.logger.error("Не удалось извлечь поля формы.")
            return results

        # Шаг 4: Передаем поля формы в HTTP-парсер
        self.http_parser.form_fields = form_fields

        # Шаг 5: Парсим каждую категорию
        for i, category in enumerate(CLIENT_CATEGORIES_TO_PARSE):
            self.logger.info(f"Парсинг категории '{category}' ({i+1}/{len(CLIENT_CATEGORIES_TO_PARSE)})...")

            retries = 0
            success = False
            category_results = []

            while retries < MAX_RETRY_ATTEMPTS and not success:
                if retries > 0:
                    self.logger.warning(f"Повторная попытка {retries + 1}/{MAX_RETRY_ATTEMPTS} для категории '{category}'.")
                    random_sleep(RETRY_DELAY * retries, RETRY_DELAY * (retries + 1), f"перед retry {retries+1} для категории {category}")

                try:
                    # Используем HTTP-парсер для парсинга категории
                    profiles = self.http_parser.parse_category(category)

                    # Если нужны детали, получаем их для каждого профиля
                    if get_details and profiles:
                        profiles_with_details = []
                        for j, profile in enumerate(profiles):
                            self.logger.info(f"Получение деталей для профиля {j+1}/{len(profiles)}: {profile.get('name', 'Unknown')}")
                            profile_url = profile.get('profile_url')
                            if profile_url:
                                try:
                                    # Используем HTTP-запрос для получения деталей
                                    response = self.http_parser.session.get(profile_url, timeout=30)
                                    response.raise_for_status()

                                    # Извлекаем детали из HTML
                                    details = self._extract_profile_details(response.text, profile_url)
                                    profile.update(details)

                                    # Пауза между запросами
                                    random_sleep(1, 2, "между запросами деталей профилей")

                                except Exception as e:
                                    self.logger.warning(f"Ошибка при получении деталей для профиля {profile_url}: {e}")

                            profiles_with_details.append(profile)

                        category_results = profiles_with_details
                    else:
                        category_results = profiles

                    success = True

                except Exception as e:
                    self.logger.error(f"Ошибка при парсинге категории '{category}' (попытка {retries + 1}): {e}", exc_info=True)
                    retries += 1

            if success:
                results[category] = category_results
                self.logger.info(f"Категория '{category}': найдено {len(category_results)} профилей.")
            else:
                self.logger.error(f"Не удалось обработать категорию '{category}' после {MAX_RETRY_ATTEMPTS} попыток.")
                results[category] = []

            # Пауза между категориями
            if i < len(CLIENT_CATEGORIES_TO_PARSE) - 1:
                random_sleep(2, 5, f"между категориями, после {category}")

        # Шаг 6: Сохраняем результаты
        all_profiles = []
        for category, profiles in results.items():
            for profile in profiles:
                profile['category'] = category
                all_profiles.append(profile)

        if all_profiles:
            self.saver.save(all_profiles)

        self.logger.info(f"Парсинг завершен. Всего найдено профилей: {len(all_profiles)}")
        return results


def main():
    """Основная функция для запуска парсера из командной строки."""
    import argparse

    parser = argparse.ArgumentParser(description="Гибридный парсер для CPA Quebec Directory")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--by-category', action='store_true', help='Парсинг по всем категориям')
    group.add_argument('--category', type=str, help='Парсинг указанной категории')
    parser.add_argument('--debug', action='store_true', help='Включить режим отладки')
    parser.add_argument('--headless', action='store_true', default=True, help='Запускать браузер в фоновом режиме')
    parser.add_argument('--no-headless', action='store_false', dest='headless', help='Запускать браузер в видимом режиме')
    parser.add_argument('--no-details', action='store_false', dest='get_details', default=True, help='Не получать детальную информацию о профилях')
    parser.add_argument('--no-captcha', action='store_false', dest='solve_captcha', default=True, help='Не решать капчу')
    parser.add_argument('--output-dir', type=str, default=OUTPUT_DIR, help='Директория для сохранения результатов')

    args = parser.parse_args()

    # Настройка логгера
    logger = setup_logging(
        log_level=logging.DEBUG if args.debug else logging.INFO,
        log_file_prefix="hybrid_parser"
    )

    logger.info("Запуск гибридного парсера CPA Quebec...")

    try:
        with HybridCpaQuebecParser(
            debug=args.debug,
            headless=args.headless,
            output_dir=args.output_dir,
            solve_captcha=args.solve_captcha
        ) as parser:
            if args.by_category:
                logger.info("Запуск парсинга по всем категориям...")
                results = parser.parse_all(get_details=args.get_details)
                total_profiles = sum(len(profiles) for profiles in results.values())
                logger.info(f"Парсинг завершен. Всего найдено профилей: {total_profiles}")
            else:
                logger.info(f"Запуск парсинга категории '{args.category}'...")
                profiles = parser.parse_category(args.category, get_details=args.get_details)
                logger.info(f"Парсинг завершен. Найдено профилей: {len(profiles)}")

    except Exception as e:
        logger.critical(f"Критическая ошибка при выполнении парсинга: {e}", exc_info=True)
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
