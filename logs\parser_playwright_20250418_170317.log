2025-04-18 17:03:17,649 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: C:\quebec -new\logs\parser_playwright_20250418_170317.log
2025-04-18 17:03:17,669 - C<PERSON>QuebecParser - DEBUG - Режим отладки DEBUG включен.
2025-04-18 17:03:17,671 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-18 17:03:17,674 - C<PERSON>QuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-18 17:03:17,675 - CpaQuebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': True, 'no_captcha': False, 'output_dir': 'C:\\quebec -new\\output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False, 'category': None}
2025-04-18 17:03:17,680 - CpaQuebecParser - INFO - ==================================================
2025-04-18 17:03:17,680 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-18 17:03:53,915 - CpaQuebecParser - DEBUG - Пауза на 1.50 сек. (после driver.get)
2025-04-18 17:03:55,849 - CpaQuebecParser - DEBUG - Пауза на 0.44 сек. (после scroll к кнопке cookie)
2025-04-18 17:04:09,414 - CpaQuebecParser - DEBUG - Пауза на 0.93 сек. (после клика Reset (XPath))
2025-04-18 17:04:15,953 - CpaQuebecParser - DEBUG - Пауза на 0.77 сек. (после очистки формы)
2025-04-18 17:05:35,779 - CpaQuebecParser - DEBUG - Пауза на 4.47 сек. (между категориями, после Individuals)
2025-04-18 17:05:54,489 - CpaQuebecParser - DEBUG - Пауза на 1.44 сек. (после driver.get)
2025-04-18 17:07:29,748 - CpaQuebecParser - DEBUG - Пауза на 0.81 сек. (после клика Reset (XPath))
2025-04-18 17:07:39,429 - CpaQuebecParser - DEBUG - Пауза на 0.58 сек. (после очистки формы)
2025-04-18 17:08:15,908 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 1
