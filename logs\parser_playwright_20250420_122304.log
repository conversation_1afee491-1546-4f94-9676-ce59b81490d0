2025-04-20 12:23:04,061 - <PERSON><PERSON><PERSON><PERSON>becParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/logs/parser_playwright_20250420_122304.log
2025-04-20 12:23:04,064 - <PERSON><PERSON><PERSON><PERSON><PERSON>cParser - DEBUG - Режим отладки DEBUG включен.
2025-04-20 12:23:04,064 - C<PERSON>QuebecParser - INFO - ==================================================
2025-04-20 12:23:04,064 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-20 12:23:04,064 - <PERSON><PERSON><PERSON>uebecParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': True, 'debug': True, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Архивы рабочих проектов/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': True, 'category': None, 'test_form_handler': False, 'test_category': 'Individuals'}
2025-04-20 12:23:04,064 - CpaQuebecParser - INFO - ==================================================
2025-04-20 12:23:04,065 - CpaQuebecParser - INFO - Режим работы: Парсинг по всем категориям.
2025-04-20 12:23:10,143 - CpaQuebecParser - DEBUG - Пауза на 1.14 сек. (после driver.get)
2025-04-20 12:23:16,893 - CpaQuebecParser - DEBUG - Пауза на 0.98 сек. (после очистки формы)
2025-04-20 12:23:18,035 - CpaQuebecParser - DEBUG - Пауза на 5.00 сек. (между категориями, после Individuals)
2025-04-20 12:23:23,063 - CpaQuebecParser - DEBUG - Пауза на 2.80 сек. (перед retry 2 для категории Large companies)
