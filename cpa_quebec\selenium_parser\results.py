import logging
import traceback
import re
from typing import List, Dict, Set, Optional
from urllib.parse import urljoin # Добавляем для аналога page.urljoin

# --- Заменяем импорты Playwright на Selenium ---
# from playwright.sync_api import Page, Locator, TimeoutError as PlaywrightTimeoutError
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from ..utils import random_sleep, clean_text, extract_emails, extract_phones
from .navigation import Navigator
from ..config import MEDIUM_TIMEOUT_SEC, SHORT_TIMEOUT_SEC # Импортируем таймауты

# Импортируем DetailsParser для вызова parse_detail_page
# Используем try-except для обхода циклического импорта, если DetailsParser тоже импортирует что-то отсюда
try:
    from .details import DetailsParser # Оставляем как есть, будет адаптирован позже
except ImportError:
    DetailsParser = None # Или использовать forward reference, если типизация строгая


class ResultsProcessor:
    """Обрабатывает страницы результатов поиска, включая пагинацию и извлечение данных (Selenium)."""

    # --- Селекторы (НУЖНО ПРОВЕРИТЬ АКТУАЛЬНОСТЬ!) ---
    CARD_SELECTOR = "div.search-results-item" # Основной селектор для карточки
    CARD_SELECTOR_ALT = "" # Альтернативный пока не нужен, убираем или оставляем пустым
    # --- Заменяем селекторы Playwright на XPath для Selenium ---
    NO_RESULTS_XPATH = "//div[@id='SectionResultats']//p[contains(normalize-space(.), 'No result found') or contains(normalize-space(.), 'No CPA found')]" # Уточненный XPath внутри контейнера результатов
    # NEXT_BUTTON_SELECTOR = 'a:has-text("Next")' # Playwright text selector
    NEXT_BUTTON_XPATH = "//ul[contains(@class,'pagination')]//a[contains(normalize-space(.), 'Next')]" # Уточненный XPath внутри пагинации

    # RESULTS_CONTAINER_SELECTOR = "#dnn_ctr1009_FindCpa_pnlResults" # Старый CSS, больше неактуален
    RESULTS_CONTAINER_SELECTOR = "#SectionResultats" # Новый CSS ID для контейнера

    # Селекторы внутри карточки (большинство будут не нужны, логика изменится)
    NAME_LINK_SELECTOR = 'h2 > a[href*="/cpa-directory/profile/"]' # Уточненный селектор для ссылки с именем
    # NAME_FALLBACK_SELECTOR = '.cpa-name' # Больше не используется
    # CONTACT_BLOCK_SELECTOR = '.contact-info' # Больше не используется
    # CITY_SELECTOR = '.cpa-city' # Больше не используется
    # REGION_SELECTOR = '.cpa-region' # Больше не используется
    # COMPANY_SELECTOR = '.cpa-company' # Больше не используется


    # --- Обновляем __init__ ---
    def __init__(self, driver: WebDriver, logger: logging.Logger, navigator: Navigator,
                 details_parser: Optional['DetailsParser'], # Принимаем DetailsParser
                 processed_urls: Set[str], # Принимаем общее множество обработанных URL
                 debug: bool = False):
        self.driver = driver
        self.logger = logger
        self.navigator = navigator # Navigator уже адаптирован
        self.details_parser = details_parser # DetailsParser будет адаптирован
        self.processed_urls = processed_urls # Храним ссылку на общее множество
        self.debug = debug
        # Инициализируем WebDriverWait
        self.wait_medium = WebDriverWait(self.driver, MEDIUM_TIMEOUT_SEC)
        self.wait_short = WebDriverWait(self.driver, SHORT_TIMEOUT_SEC)


    # --- Адаптируем process_pages ---
    def process_pages(self, get_details: bool) -> List[Dict]:
        """Обрабатывает все страницы результатов, начиная с текущей (Selenium)."""
        self.logger.info("Обработка результатов поиска (Selenium)...")
        all_page_results = []
        processed_urls_in_run = set() # Для дедупликации в рамках текущего поиска/категории
        page_number = 1
        # Проверяем, есть ли результаты перед началом цикла пагинации
        if not self._check_initial_results():
            self.logger.info("На первой странице нет результатов или контейнер результатов не найден.")
            return []
        current_url = self.driver.current_url # Базовый URL для urljoin

        while True:
            self.logger.info(f"Обработка страницы {page_number} результатов...")
            # Используем navigator для сохранения скриншотов/html (уже адаптирован)
            self.navigator.save_debug_info(f"results_page_{page_number}")

            # --- Проверка на отсутствие результатов (Selenium) ---
            # Убрали первичную проверку отсюда, т.к. она делается в _check_initial_results и _extract_list_from_page

            # --- Вызываем метод извлечения ---
            current_page_items = self._extract_list_from_page()
            if not current_page_items:
                # Если список пуст, проверяем, не появилось ли сообщение "No results" динамически
                try:
                    no_results_elements = self.driver.find_elements(By.XPATH, self.NO_RESULTS_XPATH)
                    if no_results_elements and no_results_elements[0].is_displayed():
                        self.logger.info(f"Сообщение об отсутствии результатов найдено на странице {page_number} после попытки извлечения.")
                        break  # Завершаем, если появилось сообщение
                    else:
                        self.logger.warning(f"Не удалось извлечь данные со страницы {page_number}, и сообщение 'No results' не найдено. Возможно, конец пагинации или ошибка.")
                        # Проверим наличие карточек еще раз на всякий случай
                        card_elements = self.driver.find_elements(By.CSS_SELECTOR, self.CARD_SELECTOR)
                        if not card_elements:
                            self.logger.info(f"Карточки по селектору '{self.CARD_SELECTOR}' также отсутствуют. Завершение обработки.")
                            break
                        else:
                            self.logger.warning(f"Карточки найдены ({len(card_elements)}), но извлечь данные не удалось.")
                            break
                except Exception as e:
                    self.logger.warning(f"Ошибка при повторной проверке на отсутствие результатов/карточек: {e}", exc_info=self.debug)
                    break

            self.logger.info(f"На странице {page_number} извлечено {len(current_page_items)} записей (до дедупликации).")

            new_items_count = 0
            for item in current_page_items:
                profile_url = item.get('profile_url')

                # --- Корректируем создание абсолютного URL (Selenium) ---
                if profile_url and not profile_url.startswith(('http://', 'https://')):
                    try:
                        absolute_profile_url = urljoin(current_url, profile_url)
                        item['profile_url'] = absolute_profile_url # Обновляем URL в словаре
                        profile_url = absolute_profile_url # Используем абсолютный URL дальше
                    except ValueError:
                        self.logger.warning(f"Не удалось создать абсолютный URL из базы '{current_url}' и относительного '{profile_url}'. Оставляем как есть.")

                item_key = profile_url if profile_url else f"{item.get('name')}_{item.get('city')}"

                if item_key and item_key not in self.processed_urls and item_key not in processed_urls_in_run:
                    if get_details and profile_url and self.details_parser:
                        self.logger.debug(f"Получение деталей для: {item.get('name')} ({profile_url})")
                        # Вызов details_parser.parse(profile_url) остается, он будет адаптирован
                        details = self.details_parser.parse(profile_url)
                        if details:
                            item.update(details)
                            item['details_fetched'] = True
                        else:
                            self.logger.warning(f"Не удалось получить детали для {profile_url}")
                            item['details_fetched'] = False
                    else:
                        # Если детали не нужны или нет парсера/url
                        item['details_fetched'] = False # Явно указываем статус

                    all_page_results.append(item)
                    processed_urls_in_run.add(item_key)
                    self.processed_urls.add(item_key) # Обновляем общее множество
                    new_items_count += 1
                elif item_key in self.processed_urls or item_key in processed_urls_in_run:
                    self.logger.debug(f"Запись уже обработана (дубликат): {item_key}")
                else:
                    self.logger.warning(f"Не удалось создать ключ для дедупликации для записи: {item.get('name')}")

            self.logger.info(f"Добавлено {new_items_count} новых уникальных записей со страницы {page_number}.")

            # --- Вызываем метод пагинации ---
            if not self._click_next_page():
                self.logger.info("Пагинация завершена (кнопка 'Next' не найдена/неактивна или произошла ошибка).")
                break

            page_number += 1
            random_sleep(1, 3, f"перед загрузкой страницы {page_number}")
            current_url = self.driver.current_url # Обновляем базовый URL на случай редиректа

        self.logger.info(f"Обработка результатов поиска завершена. Собрано {len(all_page_results)} уникальных записей.")
        return all_page_results

    def _check_initial_results(self) -> bool:
        """Проверяет наличие контейнера результатов и отсутствие сообщения 'No results'."""
        self.logger.debug("Проверка наличия первоначальных результатов...")
        try:
            # Ожидаем контейнер результатов
            self.wait_short.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.RESULTS_CONTAINER_SELECTOR)))
            self.logger.debug(f"Контейнер результатов '{self.RESULTS_CONTAINER_SELECTOR}' найден.")

            # Проверяем на сообщение "No results" внутри контейнера
            no_results_elements = self.driver.find_elements(By.XPATH, self.NO_RESULTS_XPATH)
            if no_results_elements and no_results_elements[0].is_displayed():
                self.logger.info("Найдено сообщение 'No result found'. Результатов нет.")
                return False
            else:
                self.logger.debug("Сообщение 'No result found' не найдено. Результаты, вероятно, есть.")
                return True # Контейнер есть, сообщения нет

        except TimeoutException:
            self.logger.warning(f"Контейнер результатов '{self.RESULTS_CONTAINER_SELECTOR}' не найден.")
            return False
        except Exception as e:
            self.logger.error(f"Ошибка при проверке первоначальных результатов: {e}", exc_info=self.debug)
            return False

    # --- Адаптируем _extract_list_from_page ---
    def _extract_list_from_page(self) -> List[Dict]:
        """Извлекает список CPA с текущей страницы результатов поиска (Selenium)."""
        if not self.driver: return [] # Проверка на Selenium driver
        self.logger.debug("Извлечение списка CPA с текущей страницы (Selenium)...")
        items = []
        card_elements: List[WebElement] = []
        current_url = self.driver.current_url # Нужен для urljoin

        # --- Поиск карточек с результатами ---
        try:
            self.logger.debug(f"Поиск карточек по основному селектору: '{self.CARD_SELECTOR}'")
            # Ожидаем присутствия *хотя бы одной* карточки внутри контейнера
            try:
                self.wait_short.until(
                     EC.presence_of_element_located((By.CSS_SELECTOR, f"{self.RESULTS_CONTAINER_SELECTOR} {self.CARD_SELECTOR}"))
                )
                self.logger.debug(f"Хотя бы одна карточка '{self.CARD_SELECTOR}' найдена внутри '{self.RESULTS_CONTAINER_SELECTOR}'.")
            except TimeoutException:
                self.logger.warning(f"Ни одной карточки '{self.CARD_SELECTOR}' не найдено внутри '{self.RESULTS_CONTAINER_SELECTOR}' за {SHORT_TIMEOUT_SEC} сек.")
                # Проверяем на сообщение об отсутствии результатов, если карточек нет
                no_results_elements = self.driver.find_elements(By.XPATH, self.NO_RESULTS_XPATH)
                if no_results_elements and no_results_elements[0].is_displayed():
                    self.logger.info("Обнаружено сообщение 'No result found' при поиске карточек.")
                return [] # Возвращаем пустой список, если карточек нет

            # Получаем все карточки
            card_elements = self.driver.find_elements(By.CSS_SELECTOR, f"{self.RESULTS_CONTAINER_SELECTOR} {self.CARD_SELECTOR}")
            self.logger.debug(f"Найдено {len(card_elements)} карточек по селектору '{self.CARD_SELECTOR}'.")

            if not card_elements:
                self.logger.warning("Список карточек пуст, хотя ожидание прошло успешно. Странная ситуация.")
                return []

        except Exception as e:
            self.logger.error(f"Ошибка при поиске карточек результатов: {e}", exc_info=self.debug)
            self.navigator.save_debug_info("card_search_error")
            return []

        # --- Извлечение данных из каждой карточки ---
        self.logger.info(f"Начинаем извлечение данных из {len(card_elements)} найденных карточек...")
        for i, card in enumerate(card_elements):
            item_data = {'name': None, 'profile_url': None, 'company': None, 'city': None, 'region': None, 'phones': [], 'emails': []}
            try:
                # --- Имя и ссылка на профиль ---
                name_link_elements = card.find_elements(By.CSS_SELECTOR, self.NAME_LINK_SELECTOR)
                if name_link_elements:
                    name_link = name_link_elements[0]
                    item_data['name'] = clean_text(name_link.text)
                    profile_url_relative = name_link.get_attribute('href')
                    if profile_url_relative:
                        item_data['profile_url'] = urljoin(current_url, profile_url_relative)
                    else:
                        self.logger.warning(f"Найдена ссылка на имя для карточки #{i+1}, но href пустой.")
                else:
                    self.logger.warning(f"Имя/ссылка не найдены в карточке #{i+1} по селектору: {self.NAME_LINK_SELECTOR}")
                    item_data['name'] = "Имя не найдено"

                # --- Извлечение Company, City, Region, Phone из тегов <p> ---
                paragraphs = card.find_elements(By.TAG_NAME, 'p')
                full_card_text = [] # Собираем текст для поиска email
                for p in paragraphs:
                    p_text = p.text
                    if p_text:
                        full_card_text.append(p_text)
                        try:
                            strong_tag = p.find_element(By.TAG_NAME, 'strong')
                            strong_text = clean_text(strong_tag.text).rstrip(':').lower()

                            if strong_text == 'company':
                                # Текст компании идет после <strong>
                                item_data['company'] = clean_text(p_text.split(':', 1)[-1])
                            elif strong_text == 'city':
                                item_data['city'] = clean_text(p_text.split(':', 1)[-1])
                            elif strong_text == 'region':
                                item_data['region'] = clean_text(p_text.split(':', 1)[-1])
                            elif strong_text == 'phone':
                                # Телефон находится в ссылке <a> внутри <p>
                                phone_links = p.find_elements(By.TAG_NAME, 'a')
                                if phone_links:
                                    phone_text = clean_text(phone_links[0].text)
                                    item_data['phones'].append(phone_text)
                                    # Можно также извлечь href=tel:
                                    # phone_tel = phone_links[0].get_attribute('href')
                        except NoSuchElementException:
                            # Тег strong не найден в этом параграфе
                            pass
                        except Exception as p_err:
                            self.logger.warning(f"Ошибка при обработке параграфа в карточке #{i+1}: {p_err}. Текст: {p_text[:100]}...")

                # --- Поиск Email (во всем тексте карточки) ---
                card_text_combined = " ".join(full_card_text) if full_card_text else card.text
                item_data['emails'] = extract_emails(card_text_combined)

                # Очищаем пустые списки
                if not item_data['phones']: item_data.pop('phones')
                if not item_data['emails']: item_data.pop('emails')

                items.append(item_data)
                self.logger.debug(f"Карточка #{i+1}: Извлечены данные для: {item_data.get('name')}")

            except StaleElementReferenceException:
                self.logger.warning(f"Карточка #{i+1} устарела (StaleElementReferenceException) во время извлечения данных. Пропускаем.")
            except Exception as e:
                self.logger.error(f"Ошибка при извлечении данных из карточки #{i+1}: {e}", exc_info=self.debug)
                try:
                    card_html = card.get_attribute('outerHTML')
                    self.logger.debug(f"HTML карточки #{i+1}:\n{card_html[:1000]}...")
                    self.navigator.save_debug_info(f"card_extract_error_{i+1}")
                except Exception as html_err:
                    self.logger.error(f"Не удалось получить HTML проблемной карточки #{i+1} после ошибки извлечения: {html_err}")

        self.logger.info(f"Завершено извлечение данных с текущей страницы. Собрано {len(items)} записей.")
        return items

    # --- Адаптируем _click_next_page ---
    def _click_next_page(self) -> bool:
        """Находит и нажимает активную кнопку 'Next' для перехода на следующую страницу (Selenium)."""
        if not self.driver: return False
        self.logger.debug("Поиск кнопки 'Next' для пагинации (Selenium)...")

        try:
            # --- Поиск кнопки 'Next' внутри элемента пагинации ---
            next_button_elements = self.driver.find_elements(By.XPATH, self.NEXT_BUTTON_XPATH)

            if not next_button_elements:
                self.logger.debug(f"Кнопка 'Next' не найдена по XPath: {self.NEXT_BUTTON_XPATH}")
                return False

            next_button_link = next_button_elements[0] # Берем первую найденную ссылку 'Next'

            # --- Проверка активности кнопки через родительский LI ---
            try:
                # Ищем родительский элемент LI
                parent_li = next_button_link.find_element(By.XPATH, "./parent::li")
                li_class = parent_li.get_attribute("class") or ""

                if "unavailable" in li_class.lower() or "disabled" in li_class.lower():
                    self.logger.debug(f"Кнопка 'Next' найдена, но неактивна (родительский LI имеет класс '{li_class}').")
                    return False
                else:
                    self.logger.info(f"Найдена активная кнопка 'Next' (родительский LI класс: '{li_class}'). Ожидание кликабельности и нажатие...")

            except NoSuchElementException:
                self.logger.warning("Не удалось найти родительский LI для кнопки 'Next'. Пробуем кликнуть по ссылке напрямую, но статус активности неизвестен.")
                # Продолжаем выполнение, но с предупреждением

            # --- Ожидание кликабельности и клик ---
            try:
                clickable_button = self.wait_short.until(
                    EC.element_to_be_clickable(next_button_link)
                )

                # Прокрутка к кнопке
                try:
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", clickable_button)
                    random_sleep(0.3, 0.7, "после scroll к кнопке Next")
                except Exception as scroll_e:
                    self.logger.warning(f"Не удалось прокрутить к кнопке Next: {scroll_e}")

                clickable_button.click()
                self.logger.info("Кнопка 'Next' нажата.")

                # --- Ожидание обновления контента ---
                random_sleep(0.5, 1.5, "после клика Next, перед ожиданием обновления")
                try:
                    # Ожидаем, что контейнер результатов все еще присутствует
                    self.wait_medium.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, self.RESULTS_CONTAINER_SELECTOR))
                    )
                    self.logger.debug("Контейнер результатов обнаружен после нажатия 'Next'.")
                except TimeoutException:
                    self.logger.warning(f"Контейнер результатов '{self.RESULTS_CONTAINER_SELECTOR}' не найден после нажатия 'Next' и ожидания {MEDIUM_TIMEOUT_SEC} сек.")
                    self.navigator.save_debug_info("next_page_container_timeout")

                return True # Клик успешен

            except TimeoutException:
                self.logger.error(f"Кнопка 'Next' не стала кликабельной в течение {SHORT_TIMEOUT_SEC} сек.")
                self.navigator.save_debug_info("next_button_not_clickable")
                return False
            except StaleElementReferenceException:
                self.logger.warning("Кнопка 'Next' устарела (StaleElementReferenceException) перед или во время клика.")
                self.navigator.save_debug_info("next_button_stale")
                return False
            except Exception as click_err:
                self.logger.error(f"Неожиданная ошибка при клике или ожидании после клика 'Next': {click_err}", exc_info=self.debug)
                self.navigator.save_debug_info("next_button_click_error")
                return False

        except Exception as e:
            self.logger.error(f"Ошибка при поиске или проверке кнопки 'Next': {e}", exc_info=self.debug)
            self.navigator.save_debug_info("next_button_find_error")
            return False
