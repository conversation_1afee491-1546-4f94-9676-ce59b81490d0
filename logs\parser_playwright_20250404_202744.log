2025-04-04 20:27:44,772 - CpaQuebecParser - INFO - Логгирование настроено. Уровень: INFO. Файл: /home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/logs/parser_playwright_20250404_202744.log
2025-04-04 20:27:44,776 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - ==================================================
2025-04-04 20:27:44,776 - CpaQuebecParser - INFO - Запуск CPA Quebec Parser (Selenium)
2025-04-04 20:27:44,776 - C<PERSON><PERSON><PERSON><PERSON>cParser - INFO - Аргументы: {'last_name': '', 'first_name': '', 'region': '', 'city': '', 'visible': False, 'debug': False, 'no_captcha': False, 'output_dir': '/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/output', 'wait_timeout': 10, 'page_timeout': 90, 'element_timeout': 20, 'get_details': True, 'by_category': False}
2025-04-04 20:27:44,777 - CpaQuebecParser - INFO - ==================================================
2025-04-04 20:27:44,777 - CpaQuebecParser - WARNING - Режим работы: Поиск без критериев.
2025-04-04 20:27:48,955 - CpaQuebecParser - CRITICAL - Критическая ошибка выполнения (Selenium): Не удалось запустить и настроить undetected_chromedriver.
2025-04-04 20:27:48,958 - CpaQuebecParser - ERROR - Traceback (most recent call last):
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/main.py", line 82, in run_parser
    with CpaQuebecParser(
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/selenium_parser/orchestrator.py", line 153, in __enter__
    self.browser_manager.__enter__() # Запускаем драйвер
  File "/home/<USER>/Documents/Воркзилла/Парсер1/project/quebec -new/cpa_quebec/selenium_parser/browser.py", line 116, in __enter__
    raise RuntimeError("Не удалось запустить и настроить undetected_chromedriver.")
RuntimeError: Не удалось запустить и настроить undetected_chromedriver.

2025-04-04 20:27:48,959 - CpaQuebecParser - INFO - Завершение работы скрипта (Selenium) с кодом: 4
