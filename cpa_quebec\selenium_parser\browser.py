import logging
import time
from typing import Optional

# Импортируем Selenium и undetected_chromedriver
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException

from ..utils import get_random_user_agent # Можно использовать для опций Chrome

class BrowserManager:
    """Управляет жизненным циклом браузера undetected_chromedriver."""

    def __init__(self, logger: logging.Logger, headless: bool = True, 
                 page_load_timeout: int = 60, implicit_wait: int = 5):
        """
        Инициализирует менеджер браузера Selenium.
        
        Args:
            logger: Логгер.
            headless: Запускать ли браузер в headless режиме.
            page_load_timeout: Таймаут загрузки страницы в секундах.
            implicit_wait: Неявное ожидание в секундах.
        """
        self.logger = logger
        self.headless = headless
        self.page_load_timeout = page_load_timeout
        self.implicit_wait = implicit_wait
        self.driver: Optional[uc.Chrome] = None

    def start(self) -> bool:
        """Запускает undetected_chromedriver."""
        if self.driver:
            try:
                # Проверим, жив ли драйвер
                _ = self.driver.window_handles
                self.logger.warning("Драйвер Selenium уже запущен.")
                return True
            except WebDriverException:
                self.logger.info("Существующий драйвер Selenium неактивен. Запускаем новый.")
                self.driver = None

        self.logger.info(f"Запуск undetected_chromedriver (Headless: {self.headless})...")
        try:
            options = uc.ChromeOptions()
            if self.headless:
                options.add_argument('--headless=new') # Новый headless режим
            # Можно добавить другие опции при необходимости
            # options.add_argument(f"user-agent={get_random_user_agent()}")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            # Указываем путь к драйверу (uc сам скачает, если не найдет)
            # driver_executable_path = '/путь/к/вашему/chromedriver' # Раскомментируйте, если нужно указать путь
            
            # Используем версию main для последних патчей, если стандартная не работает
            # Явно указываем версию Chrome (134) для скачивания правильного драйвера
            self.driver = uc.Chrome(
                options=options,
                # driver_executable_path=driver_executable_path, 
                version_main=134 # Указываем основную версию установленного Chrome
                # use_subprocess=True # Попробуйте, если возникают проблемы с запуском
            )

            # Устанавливаем таймауты
            self.driver.set_page_load_timeout(self.page_load_timeout)
            self.driver.implicitly_wait(self.implicit_wait) # Неявное ожидание
            self.driver.maximize_window() # Развернем окно

            self.logger.info("Undetected chromedriver успешно запущен.")
            return True

        except WebDriverException as e:
            self.logger.critical(f"Критическая ошибка Selenium при запуске драйвера: {e}")
            self.stop()
            return False
        except Exception as e:
            # undetected_chromedriver может выбрасывать разные ошибки
            self.logger.critical(f"Неожиданная ошибка при запуске undetected_chromedriver: {e}")
            self.logger.debug(traceback.format_exc()) # Добавим traceback для отладки
            self.stop()
            return False

    def stop(self):
        """Закрывает драйвер браузера."""
        if self.driver:
            self.logger.info("Закрытие драйвера undetected_chromedriver...")
            try:
                self.driver.quit()
                self.logger.info("Драйвер undetected_chromedriver закрыт.")
            except WebDriverException as e:
                self.logger.warning(f"Ошибка при закрытии драйвера: {e}")
            finally:
                self.driver = None
        else:
             self.logger.debug("Драйвер для закрытия не найден.")

    def get_driver(self) -> Optional[uc.Chrome]:
        """Возвращает активный экземпляр драйвера Selenium."""
        # Дополнительная проверка активности драйвера
        if self.driver:
            try:
                _ = self.driver.window_handles # Простая проверка, что драйвер жив
                return self.driver
            except WebDriverException:
                self.logger.error("Запрос драйвера, но он неактивен.")
                self.driver = None
                return None
        self.logger.error("Запрос драйвера, но он не инициализирован.")
        return None

    # --- Контекстный менеджер ---
    def __enter__(self):
        if self.start():
            return self
        else:
            raise RuntimeError("Не удалось запустить и настроить undetected_chromedriver.")

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()
        if exc_type:
            self.logger.error(f"BrowserManager завершился с ошибкой: {exc_type.__name__}: {exc_val}")
            # Можно добавить логирование traceback при необходимости
            # import traceback
            # self.logger.debug(traceback.format_exc())

# --- Вспомогательное для отладки traceback --- 
import traceback
